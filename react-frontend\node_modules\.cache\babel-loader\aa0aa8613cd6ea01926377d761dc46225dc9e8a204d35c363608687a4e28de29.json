{"ast": null, "code": "'use strict';\n\nmodule.exports = decimal;\n\n// Check if the given character code, or the character code at the first\n// character, is decimal.\nfunction decimal(character) {\n  var code = typeof character === 'string' ? character.charCodeAt(0) : character;\n  return code >= 48 && code <= 57; /* 0-9 */\n}", "map": {"version": 3, "names": ["module", "exports", "decimal", "character", "code", "charCodeAt"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/is-decimal/index.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = decimal\n\n// Check if the given character code, or the character code at the first\n// character, is decimal.\nfunction decimal(character) {\n  var code = typeof character === 'string' ? character.charCodeAt(0) : character\n\n  return code >= 48 && code <= 57 /* 0-9 */\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;;AAExB;AACA;AACA,SAASA,OAAOA,CAACC,SAAS,EAAE;EAC1B,IAAIC,IAAI,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAGA,SAAS,CAACE,UAAU,CAAC,CAAC,CAAC,GAAGF,SAAS;EAE9E,OAAOC,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE,EAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}