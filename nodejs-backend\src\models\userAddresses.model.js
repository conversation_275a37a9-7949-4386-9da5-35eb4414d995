module.exports = function (app) {
  const modelName = "user_addresses";
  const mongooseClient = app.get("mongooseClient");
  const { Schema } = mongooseClient;
  const schema = new Schema(
    {
      userId: { type: Schema.Types.ObjectId, ref: "users" },
      Street1: {
        type: String,
        required: false,
        unique: false,
        lowercase: false,
        uppercase: false,
        minLength: 3,
        maxLength: 100000000,
        index: true,
        trim: true,
      },
      Street2: {
        type: String,
        required: false,
        unique: false,
        lowercase: false,
        uppercase: false,
        minLength: 3,
        maxLength: 100000000,
        index: true,
        trim: true,
      },
      Poscode: {
        type: String,
        required: false,
        unique: false,
        lowercase: false,
        uppercase: false,
        minLength: 0,
        maxLength: 100000000,
        index: true,
        trim: true,
      },
      City: {
        type: String,
        required: false,
        unique: false,
        lowercase: false,
        uppercase: false,
        minLength: 3,
        maxLength: 100000000,
        index: true,
        trim: true,
      },
      State: {
        type: String,
        required: false,
        unique: false,
        lowercase: false,
        uppercase: false,
        minLength: 3,
        maxLength: 100000000,
        index: true,
        trim: true,
      },
      Province: {
        type: String,
        required: false,
        unique: false,
        lowercase: false,
        uppercase: false,
        minLength: 3,
        maxLength: 100000000,
        index: true,
        trim: true,
      },
      Country: {
        type: String,
        required: false,
        unique: false,
        lowercase: false,
        uppercase: false,
        minLength: 3,
        maxLength: 100000000,
        index: true,
        trim: true,
      },

      createdBy: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true,
      },
      updatedBy: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true,
      },
    },
    {
      timestamps: true,
    },
  );

  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName);
  }
  return mongooseClient.model(modelName, schema);
};
