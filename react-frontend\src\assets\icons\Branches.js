import * as React from 'react';
const Branches = (props) => (
	<svg width="18" height="18" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="currentColor" {...props}>
<path d="M6.00006 20.5716C6.00006 21.4533 6.00618 22.2859 6.00006 23.1247C6.00006 23.4798 5.83475 23.7492 5.51026 23.9084C5.19801 24.0615 4.89189 24.0186 4.61637 23.8104C4.37147 23.6268 4.27963 23.3635 4.27963 23.0574C4.28576 22.2431 4.27963 21.4288 4.27963 20.5655C4.15718 20.5655 4.04698 20.5655 3.9429 20.5655C2.95105 20.5655 1.96533 20.5655 0.973479 20.5655C0.342861 20.5716 0 20.2349 0 19.592C0 17.9573 0 16.3287 0 14.694C0 14.0512 0.336738 13.7205 0.985724 13.7144C1.96533 13.7144 2.94493 13.7144 3.92453 13.7144C4.03473 13.7144 4.14494 13.7144 4.28576 13.7144C4.28576 13.5369 4.28576 13.3899 4.28576 13.2369C4.28576 12.2756 4.28576 11.3205 4.28576 10.3593C4.28576 9.77765 4.63474 9.42867 5.2225 9.42867C7.0715 9.42867 8.91438 9.42867 10.7634 9.42867C10.8736 9.42867 10.9838 9.42867 11.1185 9.42867C11.1185 8.57764 11.1185 7.73273 11.1185 6.85721C11.0083 6.85721 10.9042 6.85721 10.794 6.85721C9.80214 6.85721 8.81641 6.85721 7.82457 6.85721C7.20007 6.85721 6.85721 6.51435 6.85721 5.88985C6.85721 4.24902 6.85721 2.60207 6.85721 0.961234C6.85721 0.342861 7.20007 0 7.82457 0C10.6103 0 13.3961 0 16.1818 0C16.8002 0 17.143 0.342861 17.143 0.967357C17.143 2.60819 17.143 4.25514 17.143 5.89598C17.143 6.51435 16.8002 6.85721 16.1757 6.85721C15.0859 6.85721 13.9961 6.85721 12.8818 6.85721C12.8818 7.71436 12.8818 8.55315 12.8818 9.42867C12.992 9.42867 13.1022 9.42867 13.2124 9.42867C15.0369 9.42867 16.8675 9.42867 18.692 9.42867C19.3961 9.42867 19.7206 9.75316 19.7206 10.4572C19.7206 11.5287 19.7206 12.6001 19.7206 13.7144C19.8431 13.7144 19.9471 13.7144 20.0573 13.7144C21.0492 13.7144 22.0349 13.7144 23.0268 13.7144C23.6696 13.7144 24.0064 14.0512 24.0064 14.694C24.0064 16.3287 24.0064 17.9573 24.0064 19.592C24.0064 20.2349 23.6635 20.5716 23.0206 20.5716C22.041 20.5716 21.0614 20.5716 20.0818 20.5716C19.9716 20.5716 19.8614 20.5716 19.7206 20.5716C19.7206 21.0553 19.7206 21.5084 19.7206 21.9614C19.7206 22.3594 19.7329 22.7574 19.7145 23.1553C19.6961 23.6329 19.3288 23.988 18.8696 23.9941C18.3981 24.0002 18.0124 23.6329 18.0063 23.1492C17.9941 22.4145 18.0002 21.6798 18.0002 20.9451C18.0002 20.8349 18.0002 20.7247 18.0002 20.5716C17.8777 20.5716 17.7675 20.5716 17.6634 20.5716C16.6716 20.5716 15.6859 20.5716 14.694 20.5716C14.0573 20.5716 13.7205 20.2288 13.7144 19.5859C13.7144 17.9512 13.7144 16.3226 13.7144 14.6879C13.7144 14.045 14.0512 13.7144 14.7001 13.7144C15.6797 13.7144 16.6594 13.7144 17.639 13.7144C17.7492 13.7144 17.8594 13.7144 17.9818 13.7144C17.9818 12.8511 17.9818 12.0185 17.9818 11.1675C14.0022 11.1675 10.0164 11.1675 6.01843 11.1675C6.01843 12.0001 6.01843 12.8328 6.01843 13.7144C6.11639 13.7144 6.22659 13.7144 6.33068 13.7144C7.32252 13.7144 8.30825 13.7144 9.30009 13.7144C9.94296 13.7144 10.2858 14.0512 10.2858 14.694C10.2858 16.3287 10.2858 17.9573 10.2858 19.592C10.2858 20.2349 9.94908 20.5716 9.30622 20.5778C8.32661 20.5778 7.34701 20.5778 6.36741 20.5778C6.25721 20.5716 6.147 20.5716 6.00006 20.5716Z"/>
</svg>

);
export default Branches;
