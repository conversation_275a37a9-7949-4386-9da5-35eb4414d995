import React from "react";
import { render, screen } from "@testing-library/react";

import UserGuideEditDialogComponent from "../UserGuideEditDialogComponent";
import { MemoryRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import { init } from "@rematch/core";
import { Provider } from "react-redux";
import * as models from "../../../models";

test("renders userGuide edit dialog", async () => {
  const store = init({ models });
  render(
    <Provider store={store}>
      <MemoryRouter>
        <UserGuideEditDialogComponent show={true} />
      </MemoryRouter>
    </Provider>,
  );
  expect(
    screen.getByRole("userGuide-edit-dialog-component"),
  ).toBeInTheDocument();
});
