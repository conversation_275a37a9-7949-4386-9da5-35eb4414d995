import * as React from "react";
const DepartmentAdmin = (props) => (
  <svg
width="18" height="18"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M14.3995 2.37395C14.3985 3.67561 13.3281 4.74318 12.0255 4.74223C10.7248 4.74129 9.65628 3.66995 9.65723 2.36734C9.65817 1.06663 10.7286 -0.000942666 12.0321 6.2463e-07C13.3338 0.000945166 14.4004 1.07135 14.3995 2.37395ZM22.4842 15.4069C22.1969 15.0076 21.7879 14.6551 21.3593 14.3999C20.7631 14.0456 20.4009 13.6306 20.3887 13H16.035C16.0237 15.0085 14.3307 16.6544 12.2764 16.6498C10.2446 16.6443 8.55813 14.992 8.55064 13H4.19594C4.19406 13.089 4.18564 13.1818 4.16973 13.28L4.16509 13.309C4.12091 13.5854 4.08768 13.7934 3.82626 13.9593C3.51908 14.1534 3.22627 14.3701 2.93341 14.5868C2.85241 14.6468 2.7714 14.7067 2.69009 14.7662C1.79818 15.4198 1.5511 16.3634 2.06397 17.319C2.49916 18.1305 2.96243 18.9282 3.45003 19.7113C3.95916 20.5301 4.96243 20.8092 5.86837 20.4254C5.9421 20.3944 6.01684 20.3645 6.09166 20.3347L6.09196 20.3346L6.09222 20.3345C6.32213 20.2428 6.55283 20.1507 6.75747 20.0206C7.34989 19.6433 7.87493 19.6589 8.44769 20.0876C8.70506 20.2804 8.86978 20.4429 8.8885 20.7642C8.90458 21.0447 8.93581 21.3245 8.96705 21.6044C8.98673 21.7807 9.00642 21.9571 9.02233 22.1338C9.12341 23.2629 9.90301 23.9844 11.0532 23.9945C11.8796 24.0018 12.7069 24.0018 13.5333 23.9945C14.6826 23.9844 15.4631 23.262 15.5642 22.1329C15.5783 21.9762 15.5957 21.8199 15.613 21.6636L15.613 21.6635L15.613 21.6635L15.613 21.6635L15.613 21.6635L15.613 21.6634C15.6429 21.3938 15.6728 21.1243 15.6859 20.8541C15.7046 20.4677 15.8918 20.2602 16.2203 20.0362C16.7528 19.6736 17.2086 19.6415 17.7467 19.9793C17.9715 20.1203 18.2234 20.2218 18.4743 20.3229L18.4745 20.3229C18.5567 20.356 18.6389 20.3892 18.72 20.4236C19.6428 20.8147 20.6414 20.5191 21.1627 19.6718C21.6127 18.9404 22.0357 18.1936 22.4585 17.4471L22.5067 17.3622C22.8782 16.7095 22.9241 16.0164 22.4842 15.4069ZM16.0294 7.16243C15.616 7.25966 15.228 7.45127 14.8703 7.73822C14.2775 8.21301 13.8933 8.79635 13.6772 9.47031C13.4421 9.47112 13.2071 9.47297 12.9721 9.47482L12.9717 9.47482C12.6585 9.47728 12.3452 9.47975 12.0319 9.47975H10.0582C9.70611 8.41501 8.94248 7.58248 7.9863 7.24739C8.16753 6.82546 8.46297 6.45639 8.89623 6.15056C9.32571 5.84756 9.80994 5.68898 10.3376 5.68709C11.4627 5.68237 12.5879 5.67765 13.713 5.68709C14.7343 5.69559 15.6075 6.29686 16.0294 7.16243ZM5.07572 11.6489L3.94033 11.6491H3.94023H3.94012H3.94002C3.18342 11.6493 2.42695 11.6495 1.67007 11.6489C1.06502 11.6479 0.824326 11.3893 0.802616 10.7823C0.765803 9.76481 1.08768 8.92095 1.93909 8.32062C2.36952 8.01668 2.85374 7.85904 3.38045 7.85715C4.5056 7.85243 5.63074 7.84771 6.75589 7.85715C8.16705 7.86942 9.29597 9.01062 9.34033 10.4208C9.37054 11.3732 9.21007 11.6932 8.12457 11.6574C7.41538 11.6329 6.70481 11.6385 5.99415 11.6441C5.68799 11.6465 5.38181 11.6489 5.07572 11.6489ZM5.07043 6.91136C6.37304 6.91231 7.44344 5.84474 7.44438 4.54308C7.44533 3.24048 6.3787 2.17008 5.07704 2.16913C3.77349 2.16724 2.70309 3.23481 2.70215 4.53647C2.70121 5.83908 3.76972 6.91042 5.07043 6.91136ZM18.7566 11.6488L17.6212 11.6491H17.6211C16.8644 11.6493 16.1079 11.6495 15.3509 11.6488C14.7459 11.6479 14.5052 11.3893 14.4835 10.7823C14.4467 9.76479 14.7685 8.92093 15.62 8.3206C16.0504 8.01666 16.5346 7.85903 17.0613 7.85714C18.1865 7.85242 19.3116 7.8477 20.4368 7.85714C21.8479 7.86941 22.9768 9.0106 23.0212 10.4208C23.0514 11.3732 22.8909 11.6932 21.8054 11.6573C21.0962 11.6329 20.3857 11.6385 19.675 11.6441C19.3688 11.6465 19.0627 11.6488 18.7566 11.6488ZM18.7503 6.91133C20.0529 6.91228 21.1233 5.84471 21.1243 4.54305C21.1252 3.24045 20.0586 2.17005 18.7569 2.1691C17.4534 2.16721 16.383 3.23478 16.382 4.53644C16.3811 5.83905 17.4496 6.91039 18.7503 6.91133Z"
      fill="#2A4454"
    />
  </svg>
);
export default DepartmentAdmin;
