{"ast": null, "code": "'use strict';\n\nvar alphabetical = require('is-alphabetical');\nvar decimal = require('is-decimal');\nmodule.exports = alphanumerical;\n\n// Check if the given character code, or the character code at the first\n// character, is alphanumerical.\nfunction alphanumerical(character) {\n  return alphabetical(character) || decimal(character);\n}", "map": {"version": 3, "names": ["alphabetical", "require", "decimal", "module", "exports", "alphanumerical", "character"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/is-alphanumerical/index.js"], "sourcesContent": ["'use strict'\n\nvar alphabetical = require('is-alphabetical')\nvar decimal = require('is-decimal')\n\nmodule.exports = alphanumerical\n\n// Check if the given character code, or the character code at the first\n// character, is alphanumerical.\nfunction alphanumerical(character) {\n  return alphabetical(character) || decimal(character)\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC7C,IAAIC,OAAO,GAAGD,OAAO,CAAC,YAAY,CAAC;AAEnCE,MAAM,CAACC,OAAO,GAAGC,cAAc;;AAE/B;AACA;AACA,SAASA,cAAcA,CAACC,SAAS,EAAE;EACjC,OAAON,YAAY,CAACM,SAAS,CAAC,IAAIJ,OAAO,CAACI,SAAS,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}