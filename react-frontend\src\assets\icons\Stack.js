import * as React from 'react';
const Stack = (props) => (
	<svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} fill="currentColor" {...props}>
		<path
			fill="currentColor"
			fillRule="evenodd"
			d="M9.004 9.713a2.36 2.36 0 0 1-1.046-.232C6.866 8.94 5.773 8.405 4.68 7.868 3.587 7.33 2.495 6.794 1.403 6.255c-.421-.208-.65-.553-.65-1.026 0-.48.229-.825.658-1.038l1.83-.9.001-.001.002-.001 1.829-.9c.277-.137.554-.272.831-.407.611-.297 1.222-.594 1.826-.908.845-.44 1.658-.425 2.508.004 1.502.754 3.011 1.495 4.52 2.236h.001l1.51.741c.108.052.216.104.32.16.405.22.617.565.613 1.026-.004.473-.24.81-.657 1.018-.718.357-1.435.71-2.156 1.062l-.726.358a293.078 293.078 0 0 1-3.642 1.778 3.77 3.77 0 0 1-.7.224 8.906 8.906 0 0 0-.301.08.275.275 0 0 1-.004-.013c-.003-.01-.007-.024-.012-.035Zm-.045 7.537a7.844 7.844 0 0 0-.328-.086 3.578 3.578 0 0 1-.694-.223 756.605 756.605 0 0 1-6.519-3.205c-.825-.409-.897-1.479-.152-1.984.196-.132.38-.152.597-.044.821.409 1.645.813 2.469 1.218 1.141.56 2.282 1.121 3.417 1.691.846.425 1.65.413 2.488-.008 1.333-.669 2.672-1.327 4.01-1.985.626-.307 1.251-.615 1.877-.924a.512.512 0 0 1 .528.025c.754.456.75 1.538-.024 1.963-.487.265-.987.507-1.487.75-.18.086-.358.172-.536.26l-4.364 2.151c-.4.197-.805.357-1.262.34l-.01.03a.342.342 0 0 1-.01.031Zm-.327-3.83c.**************.163.04.58-.017.95-.109 1.306-.281.954-.473 1.907-.942 2.86-1.411l1.431-.705.73-.359c.488-.238.975-.477 1.458-.723.754-.384.85-1.414.185-1.919-.22-.168-.433-.208-.702-.076a905.45 905.45 0 0 1-3.822 1.888l-2.12 1.045c-.713.353-1.434.393-2.16.056a29.47 29.47 0 0 1-.874-.427l-.364-.182-.982-.484a1355.5 1355.5 0 0 1-3.866-1.908c-.233-.116-.429-.088-.633.052-.722.497-.63 1.57.18 1.971l.539.267c1.991.985 3.98 1.969 5.98 2.939.*************.69.218Z"
			clipRule="evenodd"
		/>
	</svg>
);
export default Stack;
