import * as React from "react";
const Tests = (props) => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.00024 11.9849C2.00024 9.30904 1.9997 6.63322 2.00024 3.9574C2.00079 2.21335 3.19894 1.00315 4.93641 1.00206C9.63033 0.999315 14.3248 0.999315 19.0187 1.00206C20.7474 1.00315 21.9527 2.20897 21.9527 3.93164C21.9538 9.31069 21.9538 14.6897 21.9527 20.0688C21.9521 21.7766 20.7485 22.9951 19.0445 22.9967C14.3319 23.0011 9.61991 23.0011 4.90736 22.9967C3.20551 22.9951 2.00134 21.7745 2.00024 20.0677C1.9997 17.3732 2.00024 14.6788 2.00024 11.9849ZM7.36503 17.8758C7.03837 17.5382 6.74349 17.2285 6.44313 16.9249C6.08248 16.5609 5.62208 16.5291 5.29815 16.8383C4.96875 17.1529 4.99122 17.6352 5.3557 18.0019C5.79309 18.442 6.23376 18.8788 6.67224 19.3179C7.1447 19.7909 7.53823 19.7887 8.01289 19.3206C8.85148 18.4952 9.6961 17.6758 10.5309 16.8465C10.6169 16.7615 10.6788 16.6349 10.7112 16.5165C10.8044 16.1778 10.6542 15.832 10.3653 15.6686C10.0567 15.4943 9.695 15.5354 9.43356 15.7958C8.75501 16.4716 8.0825 17.1534 7.36503 17.8758ZM7.37326 12.2474C7.02741 11.895 6.7265 11.5765 6.41134 11.2723C6.30994 11.1742 6.18169 11.086 6.04905 11.0405C5.71964 10.927 5.36776 11.0641 5.18799 11.3485C4.99122 11.6604 5.01862 12.0232 5.29048 12.3033C5.75417 12.7818 6.22937 13.2499 6.70293 13.7185C7.15128 14.1619 7.5333 14.1625 7.9811 13.7218C8.8016 12.915 9.61662 12.1032 10.4415 11.3014C10.7452 11.0065 10.7923 10.5582 10.5248 10.2457C10.2524 9.92784 9.79366 9.87961 9.46919 10.1443C9.34258 10.2479 9.22967 10.3691 9.11402 10.4858C8.54399 11.0619 7.97397 11.639 7.37326 12.2474ZM7.38915 6.61733C7.06248 6.27915 6.77144 5.96454 6.46506 5.66528C6.14716 5.35396 5.69388 5.36985 5.39078 5.68611C5.10851 5.98098 5.11619 6.44468 5.416 6.74668C5.87859 7.21311 6.34338 7.67845 6.812 8.13885C7.14908 8.4699 7.59085 8.4688 7.93506 8.1394C8.19212 7.8933 8.4426 7.64008 8.69582 7.39015C9.27406 6.81958 9.83422 6.22927 10.4388 5.68775C10.6975 5.45591 10.766 5.21529 10.7024 4.91658C10.6344 4.59868 10.4103 4.42055 10.0929 4.36519C9.7953 4.31312 9.56401 4.4348 9.35902 4.64198C8.71719 5.29038 8.07099 5.9344 7.38915 6.61733ZM15.5366 18.6486C16.3851 18.6486 17.2341 18.6547 18.0826 18.6465C18.5468 18.6421 18.8795 18.3116 18.8806 17.8835C18.8817 17.4532 18.5534 17.1238 18.0826 17.1222C16.4032 17.1151 14.7244 17.114 13.045 17.1271C12.8663 17.1288 12.6651 17.2017 12.5161 17.3031C12.2513 17.4839 12.1708 17.8413 12.2765 18.1384C12.3829 18.4371 12.6668 18.6437 13.0176 18.6465C13.8573 18.653 14.697 18.6486 15.5366 18.6486ZM15.5597 7.39453C16.3719 7.39453 17.1842 7.39563 17.9965 7.39398C18.5265 7.39289 18.8784 7.08869 18.8811 6.63213C18.8839 6.17611 18.5298 5.8626 18.0047 5.86205C16.3714 5.86095 14.7375 5.86095 13.1042 5.86205C12.578 5.8626 12.2316 6.17446 12.2365 6.63651C12.2415 7.08431 12.5879 7.39234 13.096 7.39344C13.917 7.39618 14.7381 7.39453 15.5597 7.39453ZM15.5624 13.0186C16.3928 13.0186 17.2237 13.0235 18.0541 13.0169C18.5462 13.0131 18.8806 12.6957 18.8806 12.2556C18.8806 11.8182 18.5402 11.4916 18.0568 11.4905C16.3867 11.4861 14.7167 11.4855 13.0466 11.4943C12.891 11.4954 12.7178 11.5442 12.5835 11.6231C12.2913 11.7947 12.1762 12.1323 12.2596 12.4557C12.344 12.7807 12.6361 13.0114 13.0165 13.0153C13.8649 13.0241 14.7139 13.018 15.5624 13.0186Z"
      fill="#2A4454"
    />
  </svg>
);
export default Tests;
