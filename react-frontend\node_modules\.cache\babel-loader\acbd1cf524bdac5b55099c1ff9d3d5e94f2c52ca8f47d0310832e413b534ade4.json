{"ast": null, "code": "'use strict';\n\nmodule.exports = require('./html');", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/hastscript/index.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = require('./html')\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}