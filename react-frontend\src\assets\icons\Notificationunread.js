import * as React from 'react';
const Notificationunread = (props) => (
	<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width={18} height={18} fill="currentColor" {...props}>
		<path
			fill="currentColor"
			fillRule="evenodd"
			d="M12 21C13.104 21 14 20.104 14 19H10C10 20.104 10.896 21 12 21ZM17.9975 9.87462L18 14L20 16.44V18H4V16.44L6 14V9C6.027 6.226 7.942 3.848 10.52 3.191C10.518 3.17553 10.5158 3.16005 10.5136 3.14455C10.5068 3.09693 10.5 3.04903 10.5 3C10.5 2.172 11.172 1.5 12 1.5C12.828 1.5 13.5 2.172 13.5 3C13.5 3.04904 13.4932 3.09693 13.4864 3.14455C13.4842 3.16005 13.482 3.17553 13.48 3.191C13.6576 3.23628 13.832 3.28972 14.0029 3.35091C13.3788 4.05645 13 4.98399 13 6C13 8.20914 14.7909 10 17 10C17.3444 10 17.6786 9.95647 17.9975 9.87462Z"
			clipRule="evenodd"
		/>
		<circle cx="17" cy="6" r="3" fill="#C51322"/>
	</svg>
);
export default Notificationunread;
