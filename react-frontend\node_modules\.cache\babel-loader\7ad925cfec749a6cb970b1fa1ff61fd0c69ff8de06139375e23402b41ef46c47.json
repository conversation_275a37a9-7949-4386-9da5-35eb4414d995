{"ast": null, "code": "'use strict';\n\nvar types = require('./util/types');\nvar create = require('./util/create');\nvar booleanish = types.booleanish;\nvar number = types.number;\nvar spaceSeparated = types.spaceSeparated;\nmodule.exports = create({\n  transform: ariaTransform,\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  }\n});\nfunction ariaTransform(_, prop) {\n  return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase();\n}", "map": {"version": 3, "names": ["types", "require", "create", "booleanish", "number", "spaceSeparated", "module", "exports", "transform", "ariaTransform", "properties", "ariaActiveDescendant", "ariaAtomic", "ariaAutoComplete", "ariaBusy", "ariaChe<PERSON>", "ariaColCount", "ariaColIndex", "ariaColSpan", "ariaControls", "aria<PERSON>urrent", "ariaDescribedBy", "ariaDetails", "ariaDisabled", "ariaDropEffect", "ariaErrorMessage", "ariaExpanded", "ariaFlowTo", "ariaGrabbed", "aria<PERSON>as<PERSON><PERSON><PERSON>", "ariaHidden", "ariaInvalid", "ariaKeyShortcuts", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "ariaLevel", "ariaLive", "ariaModal", "ariaMultiLine", "ariaMultiSelectable", "ariaOrientation", "ariaOwns", "ariaPlaceholder", "ariaPosInSet", "ariaPressed", "ariaReadOnly", "ariaRelevant", "ariaRequired", "ariaRoleDescription", "ariaRowCount", "ariaRowIndex", "ariaRowSpan", "ariaSelected", "ariaSetSize", "ariaSort", "ariaValueMax", "ariaValueMin", "ariaValueNow", "ariaValueText", "role", "_", "prop", "slice", "toLowerCase"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/property-information/lib/aria.js"], "sourcesContent": ["'use strict'\n\nvar types = require('./util/types')\nvar create = require('./util/create')\n\nvar booleanish = types.booleanish\nvar number = types.number\nvar spaceSeparated = types.spaceSeparated\n\nmodule.exports = create({\n  transform: ariaTransform,\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  }\n})\n\nfunction ariaTransform(_, prop) {\n  return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,cAAc,CAAC;AACnC,IAAIC,MAAM,GAAGD,OAAO,CAAC,eAAe,CAAC;AAErC,IAAIE,UAAU,GAAGH,KAAK,CAACG,UAAU;AACjC,IAAIC,MAAM,GAAGJ,KAAK,CAACI,MAAM;AACzB,IAAIC,cAAc,GAAGL,KAAK,CAACK,cAAc;AAEzCC,MAAM,CAACC,OAAO,GAAGL,MAAM,CAAC;EACtBM,SAAS,EAAEC,aAAa;EACxBC,UAAU,EAAE;IACVC,oBAAoB,EAAE,IAAI;IAC1BC,UAAU,EAAET,UAAU;IACtBU,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAEX,UAAU;IACpBY,WAAW,EAAEZ,UAAU;IACvBa,YAAY,EAAEZ,MAAM;IACpBa,YAAY,EAAEb,MAAM;IACpBc,WAAW,EAAEd,MAAM;IACnBe,YAAY,EAAEd,cAAc;IAC5Be,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAEhB,cAAc;IAC/BiB,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAEpB,UAAU;IACxBqB,cAAc,EAAEnB,cAAc;IAC9BoB,gBAAgB,EAAE,IAAI;IACtBC,YAAY,EAAEvB,UAAU;IACxBwB,UAAU,EAAEtB,cAAc;IAC1BuB,WAAW,EAAEzB,UAAU;IACvB0B,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE3B,UAAU;IACtB4B,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,IAAI;IACtBC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE7B,cAAc;IAC9B8B,SAAS,EAAE/B,MAAM;IACjBgC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAElC,UAAU;IACrBmC,aAAa,EAAEnC,UAAU;IACzBoC,mBAAmB,EAAEpC,UAAU;IAC/BqC,eAAe,EAAE,IAAI;IACrBC,QAAQ,EAAEpC,cAAc;IACxBqC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAEvC,MAAM;IACpBwC,WAAW,EAAEzC,UAAU;IACvB0C,YAAY,EAAE1C,UAAU;IACxB2C,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE5C,UAAU;IACxB6C,mBAAmB,EAAE3C,cAAc;IACnC4C,YAAY,EAAE7C,MAAM;IACpB8C,YAAY,EAAE9C,MAAM;IACpB+C,WAAW,EAAE/C,MAAM;IACnBgD,YAAY,EAAEjD,UAAU;IACxBkD,WAAW,EAAEjD,MAAM;IACnBkD,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAEnD,MAAM;IACpBoD,YAAY,EAAEpD,MAAM;IACpBqD,YAAY,EAAErD,MAAM;IACpBsD,aAAa,EAAE,IAAI;IACnBC,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,SAASlD,aAAaA,CAACmD,CAAC,EAAEC,IAAI,EAAE;EAC9B,OAAOA,IAAI,KAAK,MAAM,GAAGA,IAAI,GAAG,OAAO,GAAGA,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}