import React from "react";
import { render, screen } from "@testing-library/react";

import InboxCreateDialogComponent from "../InboxCreateDialogComponent";
import { MemoryRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import { init } from "@rematch/core";
import { Provider } from "react-redux";
import * as models from "../../../../models";

test("renders inbox create dialog", async () => {
  const store = init({ models });
  render(
    <Provider store={store}>
      <MemoryRouter>
        <InboxCreateDialogComponent show={true} />
      </MemoryRouter>
    </Provider>,
  );
  expect(screen.getByRole("inbox-create-dialog-component")).toBeInTheDocument();
});
