{"ast": null, "code": "// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n  type: 'cancelation',\n  msg: 'operation is manually canceled'\n};\nfunction makeCancelable(promise) {\n  var hasCanceled_ = false;\n  var wrappedPromise = new Promise(function (resolve, reject) {\n    promise.then(function (val) {\n      return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n    });\n    promise[\"catch\"](reject);\n  });\n  return wrappedPromise.cancel = function () {\n    return hasCanceled_ = true;\n  }, wrappedPromise;\n}\nexport default makeCancelable;\nexport { CANCELATION_MESSAGE };", "map": {"version": 3, "names": ["CANCELATION_MESSAGE", "type", "msg", "makeCancelable", "promise", "hasCanceled_", "wrappedPromise", "Promise", "resolve", "reject", "then", "val", "cancel"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js"], "sourcesContent": ["// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n  type: 'cancelation',\n  msg: 'operation is manually canceled'\n};\n\nfunction makeCancelable(promise) {\n  var hasCanceled_ = false;\n  var wrappedPromise = new Promise(function (resolve, reject) {\n    promise.then(function (val) {\n      return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n    });\n    promise[\"catch\"](reject);\n  });\n  return wrappedPromise.cancel = function () {\n    return hasCanceled_ = true;\n  }, wrappedPromise;\n}\n\nexport default makeCancelable;\nexport { CANCELATION_MESSAGE };\n"], "mappings": "AAAA;AACA,IAAIA,mBAAmB,GAAG;EACxBC,IAAI,EAAE,aAAa;EACnBC,GAAG,EAAE;AACP,CAAC;AAED,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC/B,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,cAAc,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;IAC1DL,OAAO,CAACM,IAAI,CAAC,UAAUC,GAAG,EAAE;MAC1B,OAAON,YAAY,GAAGI,MAAM,CAACT,mBAAmB,CAAC,GAAGQ,OAAO,CAACG,GAAG,CAAC;IAClE,CAAC,CAAC;IACFP,OAAO,CAAC,OAAO,CAAC,CAACK,MAAM,CAAC;EAC1B,CAAC,CAAC;EACF,OAAOH,cAAc,CAACM,MAAM,GAAG,YAAY;IACzC,OAAOP,YAAY,GAAG,IAAI;EAC5B,CAAC,EAAEC,cAAc;AACnB;AAEA,eAAeH,cAAc;AAC7B,SAASH,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}