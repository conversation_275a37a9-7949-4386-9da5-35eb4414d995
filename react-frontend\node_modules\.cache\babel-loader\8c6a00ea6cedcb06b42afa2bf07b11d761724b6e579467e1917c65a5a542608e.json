{"ast": null, "code": "function deepFreeze(obj) {\n  if (obj instanceof Map) {\n    obj.clear = obj.delete = obj.set = function () {\n      throw new Error('map is read-only');\n    };\n  } else if (obj instanceof Set) {\n    obj.add = obj.clear = obj.delete = function () {\n      throw new Error('set is read-only');\n    };\n  }\n\n  // Freeze self\n  Object.freeze(obj);\n  Object.getOwnPropertyNames(obj).forEach(function (name) {\n    var prop = obj[name];\n\n    // Freeze prop if it is an object\n    if (typeof prop == 'object' && !Object.isFrozen(prop)) {\n      deepFreeze(prop);\n    }\n  });\n  return obj;\n}\nvar deepFreezeEs6 = deepFreeze;\nvar _default = deepFreeze;\ndeepFreezeEs6.default = _default;\n\n/** @implements CallbackResponse */\nclass Response {\n  /**\n   * @param {CompiledMode} mode\n   */\n  constructor(mode) {\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n  ignoreMatch() {\n    this.isMatchIgnored = true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\"/g, '&quot;').replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit(original, ...objects) {\n  /** @type Record<string,any> */\n  const result = Object.create(null);\n  for (const key in original) {\n    result[key] = original[key];\n  }\n  objects.forEach(function (obj) {\n    for (const key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return /** @type {T} */result;\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{kind?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nconst SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nconst emitsWrappingTags = node => {\n  return !!node.kind;\n};\n\n/** @type {Renderer} */\nclass HTMLRenderer {\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  constructor(parseTree, options) {\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  addText(text) {\n    this.buffer += escapeHTML(text);\n  }\n\n  /**\n   * Adds a node open to the output stream (if needed)\n   *\n   * @param {Node} node */\n  openNode(node) {\n    if (!emitsWrappingTags(node)) return;\n    let className = node.kind;\n    if (!node.sublanguage) {\n      className = `${this.classPrefix}${className}`;\n    }\n    this.span(className);\n  }\n\n  /**\n   * Adds a node close to the output stream (if needed)\n   *\n   * @param {Node} node */\n  closeNode(node) {\n    if (!emitsWrappingTags(node)) return;\n    this.buffer += SPAN_CLOSE;\n  }\n\n  /**\n   * returns the accumulated buffer\n  */\n  value() {\n    return this.buffer;\n  }\n\n  // helpers\n\n  /**\n   * Builds a span element\n   *\n   * @param {string} className */\n  span(className) {\n    this.buffer += `<span class=\"${className}\">`;\n  }\n}\n\n/** @typedef {{kind?: string, sublanguage?: boolean, children: Node[]} | string} Node */\n/** @typedef {{kind?: string, sublanguage?: boolean, children: Node[]} } DataNode */\n/**  */\n\nclass TokenTree {\n  constructor() {\n    /** @type DataNode */\n    this.rootNode = {\n      children: []\n    };\n    this.stack = [this.rootNode];\n  }\n  get top() {\n    return this.stack[this.stack.length - 1];\n  }\n  get root() {\n    return this.rootNode;\n  }\n\n  /** @param {Node} node */\n  add(node) {\n    this.top.children.push(node);\n  }\n\n  /** @param {string} kind */\n  openNode(kind) {\n    /** @type Node */\n    const node = {\n      kind,\n      children: []\n    };\n    this.add(node);\n    this.stack.push(node);\n  }\n  closeNode() {\n    if (this.stack.length > 1) {\n      return this.stack.pop();\n    }\n    // eslint-disable-next-line no-undefined\n    return undefined;\n  }\n  closeAllNodes() {\n    while (this.closeNode());\n  }\n  toJSON() {\n    return JSON.stringify(this.rootNode, null, 4);\n  }\n\n  /**\n   * @typedef { import(\"./html_renderer\").Renderer } Renderer\n   * @param {Renderer} builder\n   */\n  walk(builder) {\n    // this does not\n    return this.constructor._walk(builder, this.rootNode);\n    // this works\n    // return TokenTree._walk(builder, this.rootNode);\n  }\n\n  /**\n   * @param {Renderer} builder\n   * @param {Node} node\n   */\n  static _walk(builder, node) {\n    if (typeof node === \"string\") {\n      builder.addText(node);\n    } else if (node.children) {\n      builder.openNode(node);\n      node.children.forEach(child => this._walk(builder, child));\n      builder.closeNode(node);\n    }\n    return builder;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  static _collapse(node) {\n    if (typeof node === \"string\") return;\n    if (!node.children) return;\n    if (node.children.every(el => typeof el === \"string\")) {\n      // node.text = node.children.join(\"\");\n      // delete node.children;\n      node.children = [node.children.join(\"\")];\n    } else {\n      node.children.forEach(child => {\n        TokenTree._collapse(child);\n      });\n    }\n  }\n}\n\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addKeyword(text, kind)\n  - addText(text)\n  - addSublanguage(emitter, subLanguageName)\n  - finalize()\n  - openNode(kind)\n  - closeNode()\n  - closeAllNodes()\n  - toHTML()\n\n*/\n\n/**\n * @implements {Emitter}\n */\nclass TokenTreeEmitter extends TokenTree {\n  /**\n   * @param {*} options\n   */\n  constructor(options) {\n    super();\n    this.options = options;\n  }\n\n  /**\n   * @param {string} text\n   * @param {string} kind\n   */\n  addKeyword(text, kind) {\n    if (text === \"\") {\n      return;\n    }\n    this.openNode(kind);\n    this.addText(text);\n    this.closeNode();\n  }\n\n  /**\n   * @param {string} text\n   */\n  addText(text) {\n    if (text === \"\") {\n      return;\n    }\n    this.add(text);\n  }\n\n  /**\n   * @param {Emitter & {root: DataNode}} emitter\n   * @param {string} name\n   */\n  addSublanguage(emitter, name) {\n    /** @type DataNode */\n    const node = emitter.root;\n    node.kind = name;\n    node.sublanguage = true;\n    this.add(node);\n  }\n  toHTML() {\n    const renderer = new HTMLRenderer(this, this.options);\n    return renderer.value();\n  }\n  finalize() {\n    return true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\nfunction escape(value) {\n  return new RegExp(value.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), 'm');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map(x => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return new RegExp(re.toString() + '|').exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  const match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nconst BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {string} separator\n * @returns {string}\n */\nfunction join(regexps, separator = \"|\") {\n  let numCaptures = 0;\n  return regexps.map(regex => {\n    numCaptures += 1;\n    const offset = numCaptures;\n    let re = source(regex);\n    let out = '';\n    while (re.length > 0) {\n      const match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(re => `(${re})`).join(separator);\n}\n\n// Common regexps\nconst MATCH_NOTHING_RE = /\\b\\B/;\nconst IDENT_RE = '[a-zA-Z]\\\\w*';\nconst UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nconst NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nconst C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nconst BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nconst RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nconst SHEBANG = (opts = {}) => {\n  const beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(beginShebang, /.*\\b/, opts.binary, /\\b.*/);\n  }\n  return inherit({\n    className: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": (m, resp) => {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nconst BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]',\n  relevance: 0\n};\nconst APOS_STRING_MODE = {\n  className: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst QUOTE_STRING_MODE = {\n  className: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nconst COMMENT = function (begin, end, modeOptions = {}) {\n  const mode = inherit({\n    className: 'comment',\n    begin,\n    end,\n    contains: []\n  }, modeOptions);\n  mode.contains.push(PHRASAL_WORDS_MODE);\n  mode.contains.push({\n    className: 'doctag',\n    begin: '(?:TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):',\n    relevance: 0\n  });\n  return mode;\n};\nconst C_LINE_COMMENT_MODE = COMMENT('//', '$');\nconst C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nconst HASH_COMMENT_MODE = COMMENT('#', '$');\nconst NUMBER_MODE = {\n  className: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nconst C_NUMBER_MODE = {\n  className: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nconst BINARY_NUMBER_MODE = {\n  className: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nconst CSS_NUMBER_MODE = {\n  className: 'number',\n  begin: NUMBER_RE + '(' + '%|em|ex|ch|rem' + '|vw|vh|vmin|vmax' + '|cm|mm|in|pt|pc|px' + '|deg|grad|rad|turn' + '|s|ms' + '|Hz|kHz' + '|dpi|dpcm|dppx' + ')?',\n  relevance: 0\n};\nconst REGEXP_MODE = {\n  // this outer rule makes sure we actually have a WHOLE regex and not simply\n  // an expression such as:\n  //\n  //     3 / something\n  //\n  // (which will then blow up when regex's `illegal` sees the newline)\n  begin: /(?=\\/[^/\\n]*\\/)/,\n  contains: [{\n    className: 'regexp',\n    begin: /\\//,\n    end: /\\/[gimuy]*/,\n    illegal: /\\n/,\n    contains: [BACKSLASH_ESCAPE, {\n      begin: /\\[/,\n      end: /\\]/,\n      relevance: 0,\n      contains: [BACKSLASH_ESCAPE]\n    }]\n  }]\n};\nconst TITLE_MODE = {\n  className: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nconst UNDERSCORE_TITLE_MODE = {\n  className: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nconst METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nconst END_SAME_AS_BEGIN = function (mode) {\n  return Object.assign(mode, {\n    /** @type {ModeCallback} */\n    'on:begin': (m, resp) => {\n      resp.data._beginMatch = m[1];\n    },\n    /** @type {ModeCallback} */\n    'on:end': (m, resp) => {\n      if (resp.data._beginMatch !== m[1]) resp.ignoreMatch();\n    }\n  });\n};\nvar MODES = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n  IDENT_RE: IDENT_RE,\n  UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n  NUMBER_RE: NUMBER_RE,\n  C_NUMBER_RE: C_NUMBER_RE,\n  BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n  RE_STARTERS_RE: RE_STARTERS_RE,\n  SHEBANG: SHEBANG,\n  BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n  APOS_STRING_MODE: APOS_STRING_MODE,\n  QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n  PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n  COMMENT: COMMENT,\n  C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n  C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n  HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n  NUMBER_MODE: NUMBER_MODE,\n  C_NUMBER_MODE: C_NUMBER_MODE,\n  BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n  CSS_NUMBER_MODE: CSS_NUMBER_MODE,\n  REGEXP_MODE: REGEXP_MODE,\n  TITLE_MODE: TITLE_MODE,\n  UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE,\n  METHOD_GUARD: METHOD_GUARD,\n  END_SAME_AS_BEGIN: END_SAME_AS_BEGIN\n});\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfhasPrecedingDot(match, response) {\n  const before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfhasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n  mode.illegal = either(...mode.illegal);\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// keywords that should have no default relevance value\nconst COMMON_KEYWORDS = ['of', 'and', 'for', 'in', 'not', 'or', 'if', 'then', 'parent',\n// common variable name\n'list',\n// common variable name\n'value' // common variable name\n];\nconst DEFAULT_KEYWORD_CLASSNAME = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive, className = DEFAULT_KEYWORD_CLASSNAME) {\n  /** @type KeywordDict */\n  const compiledKeywords = {};\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing className (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(className, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(className, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function (className) {\n      // collapse all our objects back into the parent object\n      Object.assign(compiledKeywords, compileKeywords(rawKeywords[className], caseInsensitive, className));\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} className\n   * @param {Array<string>} keywordList\n   */\n  function compileList(className, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(x => x.toLowerCase());\n    }\n    keywordList.forEach(function (keyword) {\n      const pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [className, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @param {{plugins: HLJSPlugin[]}} opts\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language, {\n  plugins\n}) {\n  /**\n   * Builds a regex with the case sensativility of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(source(value), 'm' + (language.case_insensitive ? 'i' : '') + (global ? 'g' : ''));\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n     The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  class MultiRegex {\n    constructor() {\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      opts.position = this.position++;\n      // @ts-ignore\n      this.matchIndexes[this.matchAt] = opts;\n      this.regexes.push([opts, re]);\n      this.matchAt += countMatchGroups(re) + 1;\n    }\n    compile() {\n      if (this.regexes.length === 0) {\n        // avoids the need to check length every time exec is called\n        // @ts-ignore\n        this.exec = () => null;\n      }\n      const terminators = this.regexes.map(el => el[1]);\n      this.matcherRe = langRe(join(terminators), true);\n      this.lastIndex = 0;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      this.matcherRe.lastIndex = this.lastIndex;\n      const match = this.matcherRe.exec(s);\n      if (!match) {\n        return null;\n      }\n\n      // eslint-disable-next-line no-undefined\n      const i = match.findIndex((el, i) => i > 0 && el !== undefined);\n      // @ts-ignore\n      const matchData = this.matchIndexes[i];\n      // trim off any earlier non-relevant match groups (ie, the other regex\n      // match groups that make up the multi-matcher)\n      match.splice(0, i);\n      return Object.assign(match, matchData);\n    }\n  }\n\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n     So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n     NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n     Say this is our search group, and we match regex3, but wish to ignore it.\n       regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n     What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n       regex4 | regex5                               ' ie, startAt = 3\n     This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n     MOST of the time the parser will be setting startAt manually to 0.\n  */\n  class ResumableMultiRegex {\n    constructor() {\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    getMatcher(index) {\n      if (this.multiRegexes[index]) return this.multiRegexes[index];\n      const matcher = new MultiRegex();\n      this.rules.slice(index).forEach(([re, opts]) => matcher.addRule(re, opts));\n      matcher.compile();\n      this.multiRegexes[index] = matcher;\n      return matcher;\n    }\n    resumingScanAtSamePosition() {\n      return this.regexIndex !== 0;\n    }\n    considerAll() {\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      this.rules.push([re, opts]);\n      if (opts.type === \"begin\") this.count++;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      const m = this.getMatcher(this.regexIndex);\n      m.lastIndex = this.lastIndex;\n      let result = m.exec(s);\n\n      // The following is because we have no easy way to say \"resume scanning at the\n      // existing position but also skip the current rule ONLY\". What happens is\n      // all prior rules are also skipped which can result in matching the wrong\n      // thing. Example of matching \"booger\":\n\n      // our matcher is [string, \"booger\", number]\n      //\n      // ....booger....\n\n      // if \"booger\" is ignored then we'd really need a regex to scan from the\n      // SAME position for only: [string, number] but ignoring \"booger\" (if it\n      // was the first match), a simple resume would scan ahead who knows how\n      // far looking only for \"number\", ignoring potential string matches (or\n      // future \"booger\" matches that might be valid.)\n\n      // So what we do: We execute two matchers, one resuming at the same\n      // position, but the second full matcher starting at the position after:\n\n      //     /--- resume first regex match here (for [number])\n      //     |/---- full match here for [string, \"booger\", number]\n      //     vv\n      // ....booger....\n\n      // Which ever results in a match first is then used. So this 3-4 step\n      // process essentially allows us to say \"match at this position, excluding\n      // a prior rule that was ignored\".\n      //\n      // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n      // 2. Resume matching for [number]\n      // 3. Match at index + 1 for [string, \"booger\", number]\n      // 4. If #2 and #3 result in matches, which came first?\n      if (this.resumingScanAtSamePosition()) {\n        if (result && result.index === this.lastIndex) ;else {\n          // use the second matcher result\n          const m2 = this.getMatcher(0);\n          m2.lastIndex = this.lastIndex + 1;\n          result = m2.exec(s);\n        }\n      }\n      if (result) {\n        this.regexIndex += result.position + 1;\n        if (this.regexIndex === this.count) {\n          // wrap-around to considering all matches again\n          this.considerAll();\n        }\n      }\n      return result;\n    }\n  }\n\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    const mm = new ResumableMultiRegex();\n    mode.contains.forEach(term => mm.addRule(term.begin, {\n      rule: term,\n      type: \"begin\"\n    }));\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, {\n        type: \"end\"\n      });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, {\n        type: \"illegal\"\n      });\n    }\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    const cmode = /** @type CompiledMode */mode;\n    if (mode.isCompiled) return cmode;\n    [\n    // do this early so compiler extensions generally don't have to worry about\n    // the distinction between match/begin\n    compileMatch].forEach(ext => ext(mode, parent));\n    language.compilerExtensions.forEach(ext => ext(mode, parent));\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n    [beginKeywords,\n    // do this later so compiler extensions that come earlier have access to the\n    // raw array if they wanted to perhaps manipulate it, etc.\n    compileIllegal,\n    // default to 1 relevance if not specified\n    compileRelevance].forEach(ext => ext(mode, parent));\n    mode.isCompiled = true;\n    let keywordPattern = null;\n    if (typeof mode.keywords === \"object\") {\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n\n    // both are not allowed\n    if (mode.lexemes && keywordPattern) {\n      throw new Error(\"ERR: Prefer `keywords.$pattern` to `mode.lexemes`, BOTH are not allowed. (see mode reference) \");\n    }\n\n    // `mode.lexemes` was the old standard before we added and now recommend\n    // using `keywords.$pattern` to pass the keyword pattern\n    keywordPattern = keywordPattern || mode.lexemes || /\\w+/;\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(mode.begin);\n      if (mode.endSameAsBegin) mode.end = mode.begin;\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(mode.end);\n      cmode.terminatorEnd = source(mode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */mode.illegal);\n    if (!mode.contains) mode.contains = [];\n    mode.contains = [].concat(...mode.contains.map(function (c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    }));\n    mode.contains.forEach(function (c) {\n      compileMode(/** @type Mode */c, cmode);\n    });\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit(language.classNameAliases || {});\n  return compileMode(/** @type Mode */language);\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function (variant) {\n      return inherit(mode, {\n        variants: null\n      }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit(mode, {\n      starts: mode.starts ? inherit(mode.starts) : null\n    });\n  }\n  if (Object.isFrozen(mode)) {\n    return inherit(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\nvar version = \"10.7.3\";\n\n// @ts-nocheck\n\nfunction hasValueOrEmptyAttribute(value) {\n  return Boolean(value || value === \"\");\n}\nfunction BuildVuePlugin(hljs) {\n  const Component = {\n    props: [\"language\", \"code\", \"autodetect\"],\n    data: function () {\n      return {\n        detectedLanguage: \"\",\n        unknownLanguage: false\n      };\n    },\n    computed: {\n      className() {\n        if (this.unknownLanguage) return \"\";\n        return \"hljs \" + this.detectedLanguage;\n      },\n      highlighted() {\n        // no idea what language to use, return raw code\n        if (!this.autoDetect && !hljs.getLanguage(this.language)) {\n          console.warn(`The language \"${this.language}\" you specified could not be found.`);\n          this.unknownLanguage = true;\n          return escapeHTML(this.code);\n        }\n        let result = {};\n        if (this.autoDetect) {\n          result = hljs.highlightAuto(this.code);\n          this.detectedLanguage = result.language;\n        } else {\n          result = hljs.highlight(this.language, this.code, this.ignoreIllegals);\n          this.detectedLanguage = this.language;\n        }\n        return result.value;\n      },\n      autoDetect() {\n        return !this.language || hasValueOrEmptyAttribute(this.autodetect);\n      },\n      ignoreIllegals() {\n        return true;\n      }\n    },\n    // this avoids needing to use a whole Vue compilation pipeline just\n    // to build Highlight.js\n    render(createElement) {\n      return createElement(\"pre\", {}, [createElement(\"code\", {\n        class: this.className,\n        domProps: {\n          innerHTML: this.highlighted\n        }\n      })]);\n    }\n    // template: `<pre><code :class=\"className\" v-html=\"highlighted\"></code></pre>`\n  };\n  const VuePlugin = {\n    install(Vue) {\n      Vue.component('highlightjs', Component);\n    }\n  };\n  return {\n    Component,\n    VuePlugin\n  };\n}\n\n/* plugin itself */\n\n/** @type {HLJSPlugin} */\nconst mergeHTMLPlugin = {\n  \"after:highlightElement\": ({\n    el,\n    result,\n    text\n  }) => {\n    const originalStream = nodeStream(el);\n    if (!originalStream.length) return;\n    const resultNode = document.createElement('div');\n    resultNode.innerHTML = result.value;\n    result.value = mergeStreams(originalStream, nodeStream(resultNode), text);\n  }\n};\n\n/* Stream merging support functions */\n\n/**\n * @typedef Event\n * @property {'start'|'stop'} event\n * @property {number} offset\n * @property {Node} node\n */\n\n/**\n * @param {Node} node\n */\nfunction tag(node) {\n  return node.nodeName.toLowerCase();\n}\n\n/**\n * @param {Node} node\n */\nfunction nodeStream(node) {\n  /** @type Event[] */\n  const result = [];\n  (function _nodeStream(node, offset) {\n    for (let child = node.firstChild; child; child = child.nextSibling) {\n      if (child.nodeType === 3) {\n        offset += child.nodeValue.length;\n      } else if (child.nodeType === 1) {\n        result.push({\n          event: 'start',\n          offset: offset,\n          node: child\n        });\n        offset = _nodeStream(child, offset);\n        // Prevent void elements from having an end tag that would actually\n        // double them in the output. There are more void elements in HTML\n        // but we list only those realistically expected in code display.\n        if (!tag(child).match(/br|hr|img|input/)) {\n          result.push({\n            event: 'stop',\n            offset: offset,\n            node: child\n          });\n        }\n      }\n    }\n    return offset;\n  })(node, 0);\n  return result;\n}\n\n/**\n * @param {any} original - the original stream\n * @param {any} highlighted - stream of the highlighted source\n * @param {string} value - the original source itself\n */\nfunction mergeStreams(original, highlighted, value) {\n  let processed = 0;\n  let result = '';\n  const nodeStack = [];\n  function selectStream() {\n    if (!original.length || !highlighted.length) {\n      return original.length ? original : highlighted;\n    }\n    if (original[0].offset !== highlighted[0].offset) {\n      return original[0].offset < highlighted[0].offset ? original : highlighted;\n    }\n\n    /*\n    To avoid starting the stream just before it should stop the order is\n    ensured that original always starts first and closes last:\n     if (event1 == 'start' && event2 == 'start')\n      return original;\n    if (event1 == 'start' && event2 == 'stop')\n      return highlighted;\n    if (event1 == 'stop' && event2 == 'start')\n      return original;\n    if (event1 == 'stop' && event2 == 'stop')\n      return highlighted;\n     ... which is collapsed to:\n    */\n    return highlighted[0].event === 'start' ? original : highlighted;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  function open(node) {\n    /** @param {Attr} attr */\n    function attributeString(attr) {\n      return ' ' + attr.nodeName + '=\"' + escapeHTML(attr.value) + '\"';\n    }\n    // @ts-ignore\n    result += '<' + tag(node) + [].map.call(node.attributes, attributeString).join('') + '>';\n  }\n\n  /**\n   * @param {Node} node\n   */\n  function close(node) {\n    result += '</' + tag(node) + '>';\n  }\n\n  /**\n   * @param {Event} event\n   */\n  function render(event) {\n    (event.event === 'start' ? open : close)(event.node);\n  }\n  while (original.length || highlighted.length) {\n    let stream = selectStream();\n    result += escapeHTML(value.substring(processed, stream[0].offset));\n    processed = stream[0].offset;\n    if (stream === original) {\n      /*\n      On any opening or closing tag of the original markup we first close\n      the entire highlighted node stack, then render the original tag along\n      with all the following original tags at the same offset and then\n      reopen all the tags on the highlighted stack.\n      */\n      nodeStack.reverse().forEach(close);\n      do {\n        render(stream.splice(0, 1)[0]);\n        stream = selectStream();\n      } while (stream === original && stream.length && stream[0].offset === processed);\n      nodeStack.reverse().forEach(open);\n    } else {\n      if (stream[0].event === 'start') {\n        nodeStack.push(stream[0].node);\n      } else {\n        nodeStack.pop();\n      }\n      render(stream.splice(0, 1)[0]);\n    }\n  }\n  return result + escapeHTML(value.substr(processed));\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-747275419\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nconst seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nconst error = message => {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nconst warn = (message, ...args) => {\n  console.log(`WARN: ${message}`, ...args);\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nconst deprecated = (version, message) => {\n  if (seenDeprecations[`${version}/${message}`]) return;\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n\nconst escape$1 = escapeHTML;\nconst inherit$1 = inherit;\nconst NO_MATCH = Symbol(\"nomatch\");\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nconst HLJS = function (hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  const languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  const aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  const plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  let SAFE_MODE = true;\n  const fixMarkupRe = /(^(<[^>]+>|\\t|)+|\\n)/gm;\n  const LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  const PLAINTEXT_LANGUAGE = {\n    disableAutodetect: true,\n    name: 'Plain text',\n    contains: []\n  };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  let options = {\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    tabReplace: null,\n    useBR: false,\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    let classes = block.className + ' ';\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    const match = options.languageDetectRe.exec(classes);\n    if (match) {\n      const language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n    return classes.split(/\\s+/).find(_class => shouldNotHighlight(_class) || getLanguage(_class));\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrlanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode} [continuation] - current continuation mode, if any\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrlanguageName, optionsOrCode, ignoreIllegals, continuation) {\n    let code = \"\";\n    let languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrlanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n      // continuation not supported at all via the new API\n      // eslint-disable-next-line no-undefined\n      continuation = undefined;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrlanguageName;\n      code = optionsOrCode;\n    }\n\n    /** @type {BeforeHighlightContext} */\n    const context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    const result = context.result ? context.result : _highlight(context.language, context.code, ignoreIllegals, continuation);\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {RegExpMatchArray} match - regexp match data\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, match) {\n      const matchText = language.case_insensitive ? match[0].toLowerCase() : match[0];\n      return Object.prototype.hasOwnProperty.call(mode.keywords, matchText) && mode.keywords[matchText];\n    }\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n      let lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      let match = top.keywordPatternRe.exec(modeBuffer);\n      let buf = \"\";\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        const data = keywordData(top, match);\n        if (data) {\n          const [kind, keywordRelevance] = data;\n          emitter.addText(buf);\n          buf = \"\";\n          relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            const cssClass = language.classNameAliases[kind] || kind;\n            emitter.addKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substr(lastIndex);\n      emitter.addText(buf);\n    }\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      let result = null;\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */result.top;\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.addSublanguage(result.emitter, result.language);\n    }\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {Mode} mode - new mode to start\n     */\n    function startNewMode(mode) {\n      if (mode.className) {\n        emitter.openNode(language.classNameAliases[mode.className] || mode.className);\n      }\n      top = Object.create(mode, {\n        parent: {\n          value: top\n        }\n      });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      let matched = startsWith(mode.endRe, matchPlusRemainder);\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          const resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexs to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      const lexeme = match[0];\n      const newMode = match.rule;\n      const resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      const beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (const cb of beforeCallbacks) {\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n      if (newMode && newMode.endSameAsBegin) {\n        newMode.endRe = escape(lexeme);\n      }\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode);\n      // if (mode[\"after:begin\"]) {\n      //   let resp = new Response(mode);\n      //   mode[\"after:begin\"](match, resp);\n      // }\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      const lexeme = match[0];\n      const matchPlusRemainder = codeToHighlight.substr(match.index);\n      const endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) {\n        return NO_MATCH;\n      }\n      const origin = top;\n      if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.className) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        if (endMode.endSameAsBegin) {\n          endMode.starts.endRe = endMode.endRe;\n        }\n        startNewMode(endMode.starts);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n    function processContinuations() {\n      const list = [];\n      for (let current = top; current !== language; current = current.parent) {\n        if (current.className) {\n          list.unshift(current.className);\n        }\n      }\n      list.forEach(item => emitter.openNode(item));\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    let lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceeding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      const lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          const err = new Error('0 width match regex');\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        const err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.className || '<unnamed>') + '\"');\n        err.mode = top;\n        throw err;\n      } else if (match.type === \"end\") {\n        const processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        const err = new Error('potential infinite loop, way more iterations than matches');\n        throw err;\n      }\n\n      /*\n      Why might be find ourselves here?  Only one occasion now.  An end match that was\n      triggered but could not be completed.  When might this happen?  When an `endSameasBegin`\n      rule sets the end rule to a specific match.  Since the overall mode termination rule that's\n      being used to scan the text isn't recompiled that means that any match that LOOKS like\n      the end (but is not, because it is not an exact match to the beginning) will\n      end up here.  A definite end match, but when `doEndMatch` tries to \"reapply\"\n      the end rule and fails to match, we wind up here, and just silently ignore the end.\n       This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n    const language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n    const md = compileLanguage(language, {\n      plugins\n    });\n    let result = '';\n    /** @type {CompiledMode} */\n    let top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    const continuations = {}; // keep continuations for sub-languages\n    const emitter = new options.__emitter(options);\n    processContinuations();\n    let modeBuffer = '';\n    let relevance = 0;\n    let index = 0;\n    let iterations = 0;\n    let resumeScanAtSamePosition = false;\n    try {\n      top.matcher.considerAll();\n      for (;;) {\n        iterations++;\n        if (resumeScanAtSamePosition) {\n          // only regexes not matched previously will now be\n          // considered for a potential match\n          resumeScanAtSamePosition = false;\n        } else {\n          top.matcher.considerAll();\n        }\n        top.matcher.lastIndex = index;\n        const match = top.matcher.exec(codeToHighlight);\n        // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n        if (!match) break;\n        const beforeMatch = codeToHighlight.substring(index, match.index);\n        const processedCount = processLexeme(beforeMatch, match);\n        index = match.index + processedCount;\n      }\n      processLexeme(codeToHighlight.substr(index));\n      emitter.closeAllNodes();\n      emitter.finalize();\n      result = emitter.toHTML();\n      return {\n        // avoid possible breakage with v10 clients expecting\n        // this to always be an integer\n        relevance: Math.floor(relevance),\n        value: result,\n        language: languageName,\n        illegal: false,\n        emitter: emitter,\n        top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          illegal: true,\n          illegalBy: {\n            msg: err.message,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode\n          },\n          sofar: result,\n          relevance: 0,\n          value: escape$1(codeToHighlight),\n          emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          illegal: false,\n          relevance: 0,\n          value: escape$1(codeToHighlight),\n          emitter: emitter,\n          language: languageName,\n          top: top,\n          errorRaised: err\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    const result = {\n      relevance: 0,\n      emitter: new options.__emitter(options),\n      value: escape$1(code),\n      illegal: false,\n      top: PLAINTEXT_LANGUAGE\n    };\n    result.emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n   - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - second_best (object with the same structure for second-best heuristically\n    detected language, may be absent)\n     @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    const plaintext = justTextHighlightResult(code);\n    const results = languageSubset.filter(getLanguage).filter(autoDetection).map(name => _highlight(name, code, false));\n    results.unshift(plaintext); // plaintext is always an option\n\n    const sorted = results.sort((a, b) => {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n    const [best, secondBest] = sorted;\n\n    /** @type {AutoHighlightResult} */\n    const result = best;\n    result.second_best = secondBest;\n    return result;\n  }\n\n  /**\n  Post-processing of the highlighted markup:\n   - replace TABs with something more useful\n  - replace real line-breaks with '<br>' for non-pre containers\n     @param {string} html\n    @returns {string}\n  */\n  function fixMarkup(html) {\n    if (!(options.tabReplace || options.useBR)) {\n      return html;\n    }\n    return html.replace(fixMarkupRe, match => {\n      if (match === '\\n') {\n        return options.useBR ? '<br>' : match;\n      } else if (options.tabReplace) {\n        return match.replace(/\\t/g, options.tabReplace);\n      }\n      return match;\n    });\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    const language = currentLang ? aliases[currentLang] : resultLang;\n    element.classList.add(\"hljs\");\n    if (language) element.classList.add(language);\n  }\n\n  /** @type {HLJSPlugin} */\n  const brPlugin = {\n    \"before:highlightElement\": ({\n      el\n    }) => {\n      if (options.useBR) {\n        el.innerHTML = el.innerHTML.replace(/\\n/g, '').replace(/<br[ /]*>/g, '\\n');\n      }\n    },\n    \"after:highlightElement\": ({\n      result\n    }) => {\n      if (options.useBR) {\n        result.value = result.value.replace(/\\n/g, \"<br>\");\n      }\n    }\n  };\n  const TAB_REPLACE_RE = /^(<[^>]+>|\\t)+/gm;\n  /** @type {HLJSPlugin} */\n  const tabReplacePlugin = {\n    \"after:highlightElement\": ({\n      result\n    }) => {\n      if (options.tabReplace) {\n        result.value = result.value.replace(TAB_REPLACE_RE, m => m.replace(/\\t/g, options.tabReplace));\n      }\n    }\n  };\n\n  /**\n   * Applies highlighting to a DOM node containing code. Accepts a DOM node and\n   * two optional parameters for fixMarkup.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    let node = null;\n    const language = blockLanguage(element);\n    if (shouldNotHighlight(language)) return;\n\n    // support for v10 API\n    fire(\"before:highlightElement\", {\n      el: element,\n      language: language\n    });\n    node = element;\n    const text = node.textContent;\n    const result = language ? highlight(text, {\n      language,\n      ignoreIllegals: true\n    }) : highlightAuto(text);\n\n    // support for v10 API\n    fire(\"after:highlightElement\", {\n      el: element,\n      result,\n      text\n    });\n    element.innerHTML = result.value;\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relavance: result.relevance\n    };\n    if (result.second_best) {\n      element.second_best = {\n        language: result.second_best.language,\n        // TODO: remove with version 11.0\n        re: result.second_best.relevance,\n        relavance: result.second_best.relevance\n      };\n    }\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    if (userOptions.useBR) {\n      deprecated(\"10.3.0\", \"'useBR' will be removed entirely in v11.0\");\n      deprecated(\"10.3.0\", \"Please see https://github.com/highlightjs/highlight.js/issues/2559\");\n    }\n    options = inherit$1(options, userOptions);\n  }\n\n  /**\n   * Highlights to all <pre><code> blocks on a page\n   *\n   * @type {Function & {called?: boolean}}\n   */\n  // TODO: remove v12, deprecated\n  const initHighlighting = () => {\n    if (initHighlighting.called) return;\n    initHighlighting.called = true;\n    deprecated(\"10.6.0\", \"initHighlighting() is deprecated.  Use highlightAll() instead.\");\n    const blocks = document.querySelectorAll('pre code');\n    blocks.forEach(highlightElement);\n  };\n\n  // Higlights all when DOMContentLoaded fires\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() is deprecated.  Use highlightAll() instead.\");\n    wantsHighlight = true;\n  }\n  let wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      wantsHighlight = true;\n      return;\n    }\n    const blocks = document.querySelectorAll('pre code');\n    blocks.forEach(highlightElement);\n  }\n  function boot() {\n    // if a highlight was requested before DOM was loaded, do now\n    if (wantsHighlight) highlightAll();\n  }\n\n  // make sure we are in the browser environment\n  if (typeof window !== 'undefined' && window.addEventListener) {\n    window.addEventListener('DOMContentLoaded', boot, false);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    let lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) {\n        throw error$1;\n      } else {\n        error(error$1);\n      }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n    if (lang.aliases) {\n      registerAliases(lang.aliases, {\n        languageName\n      });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (const alias of Object.keys(aliases)) {\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n    intended usage: When one language truly requires another\n     Unlike `getLanguage`, this will throw when the requested language\n    is not available.\n     @param {string} name - name of the language to fetch/require\n    @returns {Language | never}\n  */\n  function requireLanguage(name) {\n    deprecated(\"10.4.0\", \"requireLanguage will be removed entirely in v11.\");\n    deprecated(\"10.4.0\", \"Please see https://github.com/highlightjs/highlight.js/pull/2844\");\n    const lang = getLanguage(name);\n    if (lang) {\n      return lang;\n    }\n    const err = new Error('The \\'{}\\' language is required, but not loaded.'.replace('{}', name));\n    throw err;\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, {\n    languageName\n  }) {\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(alias => {\n      aliases[alias.toLowerCase()] = languageName;\n    });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    const lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = data => {\n        plugin[\"before:highlightBlock\"](Object.assign({\n          block: data.el\n        }, data));\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = data => {\n        plugin[\"after:highlightBlock\"](Object.assign({\n          block: data.el\n        }, data));\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    const cb = event;\n    plugins.forEach(function (plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n  Note: fixMarkup is deprecated and will be removed entirely in v11\n   @param {string} arg\n  @returns {string}\n  */\n  function deprecateFixMarkup(arg) {\n    deprecated(\"10.2.0\", \"fixMarkup will be removed entirely in v11.0\");\n    deprecated(\"10.2.0\", \"Please see https://github.com/highlightjs/highlight.js/issues/2534\");\n    return fixMarkup(arg);\n  }\n\n  /**\n   *\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    fixMarkup: deprecateFixMarkup,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    requireLanguage,\n    autoDetection,\n    inherit: inherit$1,\n    addPlugin,\n    // plugins for frameworks\n    vuePlugin: BuildVuePlugin(hljs).VuePlugin\n  });\n  hljs.debugMode = function () {\n    SAFE_MODE = false;\n  };\n  hljs.safeMode = function () {\n    SAFE_MODE = true;\n  };\n  hljs.versionString = version;\n  for (const key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreezeEs6(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexs into our main object\n  Object.assign(hljs, MODES);\n\n  // built-in plugins, likely to be moved out of core in the future\n  hljs.addPlugin(brPlugin); // slated to be removed in v11\n  hljs.addPlugin(mergeHTMLPlugin);\n  hljs.addPlugin(tabReplacePlugin);\n  return hljs;\n};\n\n// export an \"instance\" of the highlighter\nvar highlight = HLJS({});\nmodule.exports = highlight;", "map": {"version": 3, "names": ["deepFreeze", "obj", "Map", "clear", "delete", "set", "Error", "Set", "add", "Object", "freeze", "getOwnPropertyNames", "for<PERSON>ach", "name", "prop", "isFrozen", "deepFreezeEs6", "_default", "default", "Response", "constructor", "mode", "data", "undefined", "isMatchIgnored", "ignoreMatch", "escapeHTML", "value", "replace", "inherit", "original", "objects", "result", "create", "key", "SPAN_CLOSE", "emitsWrappingTags", "node", "kind", "HTMLR<PERSON><PERSON>", "parseTree", "options", "buffer", "classPrefix", "walk", "addText", "text", "openNode", "className", "sublanguage", "span", "closeNode", "TokenTree", "rootNode", "children", "stack", "top", "length", "root", "push", "pop", "closeAllNodes", "toJSON", "JSON", "stringify", "builder", "_walk", "child", "_collapse", "every", "el", "join", "TokenTreeEmitter", "addKeyword", "addSublanguage", "emitter", "toHTML", "renderer", "finalize", "escape", "RegExp", "source", "re", "concat", "args", "joined", "map", "x", "either", "countMatchGroups", "toString", "exec", "startsWith", "lexeme", "match", "index", "BACKREF_RE", "regexps", "separator", "numCaptures", "regex", "offset", "out", "substring", "String", "Number", "MATCH_NOTHING_RE", "IDENT_RE", "UNDERSCORE_IDENT_RE", "NUMBER_RE", "C_NUMBER_RE", "BINARY_NUMBER_RE", "RE_STARTERS_RE", "SHEBANG", "opts", "beginShebang", "binary", "begin", "end", "relevance", "on:begin", "m", "resp", "BACKSLASH_ESCAPE", "APOS_STRING_MODE", "illegal", "contains", "QUOTE_STRING_MODE", "PHRASAL_WORDS_MODE", "COMMENT", "modeOptions", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "HASH_COMMENT_MODE", "NUMBER_MODE", "C_NUMBER_MODE", "BINARY_NUMBER_MODE", "CSS_NUMBER_MODE", "REGEXP_MODE", "TITLE_MODE", "UNDERSCORE_TITLE_MODE", "METHOD_GUARD", "END_SAME_AS_BEGIN", "assign", "_beginMatch", "on:end", "MODES", "__proto__", "skipIfhasPrecedingDot", "response", "before", "input", "beginKeywords", "parent", "split", "__beforeBegin", "keywords", "compileIllegal", "_parent", "Array", "isArray", "compileMatch", "compileRelevance", "COMMON_KEYWORDS", "DEFAULT_KEYWORD_CLASSNAME", "compileKeywords", "rawKeywords", "caseInsensitive", "compiledKeywords", "compileList", "keys", "keywordList", "toLowerCase", "keyword", "pair", "scoreForKeyword", "providedScore", "commonKeyword", "includes", "compileLanguage", "language", "plugins", "langRe", "global", "case_insensitive", "MultiRegex", "matchIndexes", "regexes", "matchAt", "position", "addRule", "compile", "terminators", "matcherRe", "lastIndex", "s", "i", "findIndex", "matchData", "splice", "ResumableMultiRegex", "rules", "multiRegexes", "count", "regexIndex", "getMatcher", "matcher", "slice", "resumingScanAtSamePosition", "considerAll", "type", "m2", "buildModeRegex", "mm", "term", "rule", "terminatorEnd", "compileMode", "cmode", "isCompiled", "ext", "compilerExtensions", "keywordPattern", "$pattern", "lexemes", "keywordPatternRe", "beginRe", "endSameAsBegin", "endsWithParent", "endRe", "illegalRe", "c", "expandOrCloneMode", "starts", "classNameAliases", "dependencyOnParent", "variants", "cachedVariants", "variant", "version", "hasValueOrEmptyAttribute", "Boolean", "BuildVuePlugin", "hljs", "Component", "props", "detectedLanguage", "unknownLanguage", "computed", "highlighted", "autoDetect", "getLanguage", "console", "warn", "code", "highlightAuto", "highlight", "ignoreIllegals", "autodetect", "render", "createElement", "class", "domProps", "innerHTML", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "install", "<PERSON><PERSON>", "component", "mergeHTMLPlugin", "after:highlightElement", "originalStream", "nodeStream", "resultNode", "document", "mergeStreams", "tag", "nodeName", "_nodeStream", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "nodeType", "nodeValue", "event", "processed", "nodeStack", "selectStream", "open", "attributeString", "attr", "call", "attributes", "close", "stream", "reverse", "substr", "seenDeprecations", "error", "message", "log", "deprecated", "escape$1", "inherit$1", "NO_MATCH", "Symbol", "HLJS", "languages", "aliases", "SAFE_MODE", "fixMarkupRe", "LANGUAGE_NOT_FOUND", "PLAINTEXT_LANGUAGE", "disableAutodetect", "noHighlightRe", "languageDetectRe", "tabReplace", "useBR", "__emitter", "shouldNotHighlight", "languageName", "test", "blockLanguage", "block", "classes", "parentNode", "find", "_class", "codeOrlanguageName", "optionsOrCode", "continuation", "context", "fire", "_highlight", "codeToHighlight", "keywordData", "matchText", "prototype", "hasOwnProperty", "processKeywords", "modeBuffer", "buf", "keywordRelevance", "cssClass", "processSubLanguage", "subLanguage", "continuations", "processBuffer", "startNewMode", "endOfMode", "matchPlusRemainder", "matched", "endsParent", "doIgnore", "resumeScanAtSamePosition", "doBeginMatch", "newMode", "beforeCallbacks", "cb", "skip", "excludeBegin", "returnBegin", "doEndMatch", "endMode", "origin", "returnEnd", "excludeEnd", "processContinuations", "list", "current", "unshift", "item", "lastMatch", "processLexeme", "textBeforeMatch", "err", "badRule", "iterations", "md", "beforeMatch", "processedCount", "Math", "floor", "illegalBy", "msg", "sofar", "errorRaised", "justTextHighlightResult", "languageSubset", "plaintext", "results", "filter", "autoDetection", "sorted", "sort", "a", "b", "supersetOf", "best", "secondBest", "second_best", "fixMarkup", "html", "updateClassName", "element", "currentLang", "resultLang", "classList", "brPlugin", "before:highlightElement", "TAB_REPLACE_RE", "tabReplacePlugin", "highlightElement", "textContent", "relavance", "configure", "userOptions", "initHighlighting", "called", "blocks", "querySelectorAll", "initHighlightingOnLoad", "wantsHighlight", "highlightAll", "readyState", "boot", "window", "addEventListener", "registerLanguage", "languageDefinition", "lang", "error$1", "rawDefinition", "bind", "registerAliases", "unregisterLanguage", "alias", "listLanguages", "requireLanguage", "aliasList", "upgradePluginAPI", "plugin", "addPlugin", "deprecateFixMarkup", "arg", "deprecateHighlightBlock", "highlightBlock", "vuePlugin", "debugMode", "safeMode", "versionString", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/highlight.js/lib/core.js"], "sourcesContent": ["function deepFreeze(obj) {\n    if (obj instanceof Map) {\n        obj.clear = obj.delete = obj.set = function () {\n            throw new Error('map is read-only');\n        };\n    } else if (obj instanceof Set) {\n        obj.add = obj.clear = obj.delete = function () {\n            throw new Error('set is read-only');\n        };\n    }\n\n    // Freeze self\n    Object.freeze(obj);\n\n    Object.getOwnPropertyNames(obj).forEach(function (name) {\n        var prop = obj[name];\n\n        // Freeze prop if it is an object\n        if (typeof prop == 'object' && !Object.isFrozen(prop)) {\n            deepFreeze(prop);\n        }\n    });\n\n    return obj;\n}\n\nvar deepFreezeEs6 = deepFreeze;\nvar _default = deepFreeze;\ndeepFreezeEs6.default = _default;\n\n/** @implements CallbackResponse */\nclass Response {\n  /**\n   * @param {CompiledMode} mode\n   */\n  constructor(mode) {\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n\n  ignoreMatch() {\n    this.isMatchIgnored = true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit(original, ...objects) {\n  /** @type Record<string,any> */\n  const result = Object.create(null);\n\n  for (const key in original) {\n    result[key] = original[key];\n  }\n  objects.forEach(function(obj) {\n    for (const key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return /** @type {T} */ (result);\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{kind?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nconst SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nconst emitsWrappingTags = (node) => {\n  return !!node.kind;\n};\n\n/** @type {Renderer} */\nclass HTMLRenderer {\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  constructor(parseTree, options) {\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  addText(text) {\n    this.buffer += escapeHTML(text);\n  }\n\n  /**\n   * Adds a node open to the output stream (if needed)\n   *\n   * @param {Node} node */\n  openNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    let className = node.kind;\n    if (!node.sublanguage) {\n      className = `${this.classPrefix}${className}`;\n    }\n    this.span(className);\n  }\n\n  /**\n   * Adds a node close to the output stream (if needed)\n   *\n   * @param {Node} node */\n  closeNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    this.buffer += SPAN_CLOSE;\n  }\n\n  /**\n   * returns the accumulated buffer\n  */\n  value() {\n    return this.buffer;\n  }\n\n  // helpers\n\n  /**\n   * Builds a span element\n   *\n   * @param {string} className */\n  span(className) {\n    this.buffer += `<span class=\"${className}\">`;\n  }\n}\n\n/** @typedef {{kind?: string, sublanguage?: boolean, children: Node[]} | string} Node */\n/** @typedef {{kind?: string, sublanguage?: boolean, children: Node[]} } DataNode */\n/**  */\n\nclass TokenTree {\n  constructor() {\n    /** @type DataNode */\n    this.rootNode = { children: [] };\n    this.stack = [this.rootNode];\n  }\n\n  get top() {\n    return this.stack[this.stack.length - 1];\n  }\n\n  get root() { return this.rootNode; }\n\n  /** @param {Node} node */\n  add(node) {\n    this.top.children.push(node);\n  }\n\n  /** @param {string} kind */\n  openNode(kind) {\n    /** @type Node */\n    const node = { kind, children: [] };\n    this.add(node);\n    this.stack.push(node);\n  }\n\n  closeNode() {\n    if (this.stack.length > 1) {\n      return this.stack.pop();\n    }\n    // eslint-disable-next-line no-undefined\n    return undefined;\n  }\n\n  closeAllNodes() {\n    while (this.closeNode());\n  }\n\n  toJSON() {\n    return JSON.stringify(this.rootNode, null, 4);\n  }\n\n  /**\n   * @typedef { import(\"./html_renderer\").Renderer } Renderer\n   * @param {Renderer} builder\n   */\n  walk(builder) {\n    // this does not\n    return this.constructor._walk(builder, this.rootNode);\n    // this works\n    // return TokenTree._walk(builder, this.rootNode);\n  }\n\n  /**\n   * @param {Renderer} builder\n   * @param {Node} node\n   */\n  static _walk(builder, node) {\n    if (typeof node === \"string\") {\n      builder.addText(node);\n    } else if (node.children) {\n      builder.openNode(node);\n      node.children.forEach((child) => this._walk(builder, child));\n      builder.closeNode(node);\n    }\n    return builder;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  static _collapse(node) {\n    if (typeof node === \"string\") return;\n    if (!node.children) return;\n\n    if (node.children.every(el => typeof el === \"string\")) {\n      // node.text = node.children.join(\"\");\n      // delete node.children;\n      node.children = [node.children.join(\"\")];\n    } else {\n      node.children.forEach((child) => {\n        TokenTree._collapse(child);\n      });\n    }\n  }\n}\n\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addKeyword(text, kind)\n  - addText(text)\n  - addSublanguage(emitter, subLanguageName)\n  - finalize()\n  - openNode(kind)\n  - closeNode()\n  - closeAllNodes()\n  - toHTML()\n\n*/\n\n/**\n * @implements {Emitter}\n */\nclass TokenTreeEmitter extends TokenTree {\n  /**\n   * @param {*} options\n   */\n  constructor(options) {\n    super();\n    this.options = options;\n  }\n\n  /**\n   * @param {string} text\n   * @param {string} kind\n   */\n  addKeyword(text, kind) {\n    if (text === \"\") { return; }\n\n    this.openNode(kind);\n    this.addText(text);\n    this.closeNode();\n  }\n\n  /**\n   * @param {string} text\n   */\n  addText(text) {\n    if (text === \"\") { return; }\n\n    this.add(text);\n  }\n\n  /**\n   * @param {Emitter & {root: DataNode}} emitter\n   * @param {string} name\n   */\n  addSublanguage(emitter, name) {\n    /** @type DataNode */\n    const node = emitter.root;\n    node.kind = name;\n    node.sublanguage = true;\n    this.add(node);\n  }\n\n  toHTML() {\n    const renderer = new HTMLRenderer(this, this.options);\n    return renderer.value();\n  }\n\n  finalize() {\n    return true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\nfunction escape(value) {\n  return new RegExp(value.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), 'm');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return (new RegExp(re.toString() + '|')).exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  const match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nconst BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {string} separator\n * @returns {string}\n */\nfunction join(regexps, separator = \"|\") {\n  let numCaptures = 0;\n\n  return regexps.map((regex) => {\n    numCaptures += 1;\n    const offset = numCaptures;\n    let re = source(regex);\n    let out = '';\n\n    while (re.length > 0) {\n      const match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(re => `(${re})`).join(separator);\n}\n\n// Common regexps\nconst MATCH_NOTHING_RE = /\\b\\B/;\nconst IDENT_RE = '[a-zA-Z]\\\\w*';\nconst UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nconst NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nconst C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nconst BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nconst RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nconst SHEBANG = (opts = {}) => {\n  const beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(\n      beginShebang,\n      /.*\\b/,\n      opts.binary,\n      /\\b.*/);\n  }\n  return inherit({\n    className: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": (m, resp) => {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nconst BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]', relevance: 0\n};\nconst APOS_STRING_MODE = {\n  className: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst QUOTE_STRING_MODE = {\n  className: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nconst COMMENT = function(begin, end, modeOptions = {}) {\n  const mode = inherit(\n    {\n      className: 'comment',\n      begin,\n      end,\n      contains: []\n    },\n    modeOptions\n  );\n  mode.contains.push(PHRASAL_WORDS_MODE);\n  mode.contains.push({\n    className: 'doctag',\n    begin: '(?:TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):',\n    relevance: 0\n  });\n  return mode;\n};\nconst C_LINE_COMMENT_MODE = COMMENT('//', '$');\nconst C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nconst HASH_COMMENT_MODE = COMMENT('#', '$');\nconst NUMBER_MODE = {\n  className: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nconst C_NUMBER_MODE = {\n  className: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nconst BINARY_NUMBER_MODE = {\n  className: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nconst CSS_NUMBER_MODE = {\n  className: 'number',\n  begin: NUMBER_RE + '(' +\n    '%|em|ex|ch|rem' +\n    '|vw|vh|vmin|vmax' +\n    '|cm|mm|in|pt|pc|px' +\n    '|deg|grad|rad|turn' +\n    '|s|ms' +\n    '|Hz|kHz' +\n    '|dpi|dpcm|dppx' +\n    ')?',\n  relevance: 0\n};\nconst REGEXP_MODE = {\n  // this outer rule makes sure we actually have a WHOLE regex and not simply\n  // an expression such as:\n  //\n  //     3 / something\n  //\n  // (which will then blow up when regex's `illegal` sees the newline)\n  begin: /(?=\\/[^/\\n]*\\/)/,\n  contains: [{\n    className: 'regexp',\n    begin: /\\//,\n    end: /\\/[gimuy]*/,\n    illegal: /\\n/,\n    contains: [\n      BACKSLASH_ESCAPE,\n      {\n        begin: /\\[/,\n        end: /\\]/,\n        relevance: 0,\n        contains: [BACKSLASH_ESCAPE]\n      }\n    ]\n  }]\n};\nconst TITLE_MODE = {\n  className: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nconst UNDERSCORE_TITLE_MODE = {\n  className: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nconst METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nconst END_SAME_AS_BEGIN = function(mode) {\n  return Object.assign(mode,\n    {\n      /** @type {ModeCallback} */\n      'on:begin': (m, resp) => { resp.data._beginMatch = m[1]; },\n      /** @type {ModeCallback} */\n      'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); }\n    });\n};\n\nvar MODES = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n    IDENT_RE: IDENT_RE,\n    UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n    NUMBER_RE: NUMBER_RE,\n    C_NUMBER_RE: C_NUMBER_RE,\n    BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n    RE_STARTERS_RE: RE_STARTERS_RE,\n    SHEBANG: SHEBANG,\n    BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n    APOS_STRING_MODE: APOS_STRING_MODE,\n    QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n    PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n    COMMENT: COMMENT,\n    C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n    C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n    HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n    NUMBER_MODE: NUMBER_MODE,\n    C_NUMBER_MODE: C_NUMBER_MODE,\n    BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n    CSS_NUMBER_MODE: CSS_NUMBER_MODE,\n    REGEXP_MODE: REGEXP_MODE,\n    TITLE_MODE: TITLE_MODE,\n    UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE,\n    METHOD_GUARD: METHOD_GUARD,\n    END_SAME_AS_BEGIN: END_SAME_AS_BEGIN\n});\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfhasPrecedingDot(match, response) {\n  const before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfhasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n\n  mode.illegal = either(...mode.illegal);\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// keywords that should have no default relevance value\nconst COMMON_KEYWORDS = [\n  'of',\n  'and',\n  'for',\n  'in',\n  'not',\n  'or',\n  'if',\n  'then',\n  'parent', // common variable name\n  'list', // common variable name\n  'value' // common variable name\n];\n\nconst DEFAULT_KEYWORD_CLASSNAME = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive, className = DEFAULT_KEYWORD_CLASSNAME) {\n  /** @type KeywordDict */\n  const compiledKeywords = {};\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing className (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(className, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(className, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function(className) {\n      // collapse all our objects back into the parent object\n      Object.assign(\n        compiledKeywords,\n        compileKeywords(rawKeywords[className], caseInsensitive, className)\n      );\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} className\n   * @param {Array<string>} keywordList\n   */\n  function compileList(className, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(x => x.toLowerCase());\n    }\n    keywordList.forEach(function(keyword) {\n      const pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [className, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @param {{plugins: HLJSPlugin[]}} opts\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language, { plugins }) {\n  /**\n   * Builds a regex with the case sensativility of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(\n      source(value),\n      'm' + (language.case_insensitive ? 'i' : '') + (global ? 'g' : '')\n    );\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n\n    The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  class MultiRegex {\n    constructor() {\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      opts.position = this.position++;\n      // @ts-ignore\n      this.matchIndexes[this.matchAt] = opts;\n      this.regexes.push([opts, re]);\n      this.matchAt += countMatchGroups(re) + 1;\n    }\n\n    compile() {\n      if (this.regexes.length === 0) {\n        // avoids the need to check length every time exec is called\n        // @ts-ignore\n        this.exec = () => null;\n      }\n      const terminators = this.regexes.map(el => el[1]);\n      this.matcherRe = langRe(join(terminators), true);\n      this.lastIndex = 0;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      this.matcherRe.lastIndex = this.lastIndex;\n      const match = this.matcherRe.exec(s);\n      if (!match) { return null; }\n\n      // eslint-disable-next-line no-undefined\n      const i = match.findIndex((el, i) => i > 0 && el !== undefined);\n      // @ts-ignore\n      const matchData = this.matchIndexes[i];\n      // trim off any earlier non-relevant match groups (ie, the other regex\n      // match groups that make up the multi-matcher)\n      match.splice(0, i);\n\n      return Object.assign(match, matchData);\n    }\n  }\n\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n\n    So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n\n    NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n\n    Say this is our search group, and we match regex3, but wish to ignore it.\n\n      regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n\n    What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n\n      regex4 | regex5                               ' ie, startAt = 3\n\n    This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n\n    MOST of the time the parser will be setting startAt manually to 0.\n  */\n  class ResumableMultiRegex {\n    constructor() {\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    getMatcher(index) {\n      if (this.multiRegexes[index]) return this.multiRegexes[index];\n\n      const matcher = new MultiRegex();\n      this.rules.slice(index).forEach(([re, opts]) => matcher.addRule(re, opts));\n      matcher.compile();\n      this.multiRegexes[index] = matcher;\n      return matcher;\n    }\n\n    resumingScanAtSamePosition() {\n      return this.regexIndex !== 0;\n    }\n\n    considerAll() {\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      this.rules.push([re, opts]);\n      if (opts.type === \"begin\") this.count++;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      const m = this.getMatcher(this.regexIndex);\n      m.lastIndex = this.lastIndex;\n      let result = m.exec(s);\n\n      // The following is because we have no easy way to say \"resume scanning at the\n      // existing position but also skip the current rule ONLY\". What happens is\n      // all prior rules are also skipped which can result in matching the wrong\n      // thing. Example of matching \"booger\":\n\n      // our matcher is [string, \"booger\", number]\n      //\n      // ....booger....\n\n      // if \"booger\" is ignored then we'd really need a regex to scan from the\n      // SAME position for only: [string, number] but ignoring \"booger\" (if it\n      // was the first match), a simple resume would scan ahead who knows how\n      // far looking only for \"number\", ignoring potential string matches (or\n      // future \"booger\" matches that might be valid.)\n\n      // So what we do: We execute two matchers, one resuming at the same\n      // position, but the second full matcher starting at the position after:\n\n      //     /--- resume first regex match here (for [number])\n      //     |/---- full match here for [string, \"booger\", number]\n      //     vv\n      // ....booger....\n\n      // Which ever results in a match first is then used. So this 3-4 step\n      // process essentially allows us to say \"match at this position, excluding\n      // a prior rule that was ignored\".\n      //\n      // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n      // 2. Resume matching for [number]\n      // 3. Match at index + 1 for [string, \"booger\", number]\n      // 4. If #2 and #3 result in matches, which came first?\n      if (this.resumingScanAtSamePosition()) {\n        if (result && result.index === this.lastIndex) ; else { // use the second matcher result\n          const m2 = this.getMatcher(0);\n          m2.lastIndex = this.lastIndex + 1;\n          result = m2.exec(s);\n        }\n      }\n\n      if (result) {\n        this.regexIndex += result.position + 1;\n        if (this.regexIndex === this.count) {\n          // wrap-around to considering all matches again\n          this.considerAll();\n        }\n      }\n\n      return result;\n    }\n  }\n\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    const mm = new ResumableMultiRegex();\n\n    mode.contains.forEach(term => mm.addRule(term.begin, { rule: term, type: \"begin\" }));\n\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, { type: \"end\" });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, { type: \"illegal\" });\n    }\n\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    const cmode = /** @type CompiledMode */ (mode);\n    if (mode.isCompiled) return cmode;\n\n    [\n      // do this early so compiler extensions generally don't have to worry about\n      // the distinction between match/begin\n      compileMatch\n    ].forEach(ext => ext(mode, parent));\n\n    language.compilerExtensions.forEach(ext => ext(mode, parent));\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n\n    [\n      beginKeywords,\n      // do this later so compiler extensions that come earlier have access to the\n      // raw array if they wanted to perhaps manipulate it, etc.\n      compileIllegal,\n      // default to 1 relevance if not specified\n      compileRelevance\n    ].forEach(ext => ext(mode, parent));\n\n    mode.isCompiled = true;\n\n    let keywordPattern = null;\n    if (typeof mode.keywords === \"object\") {\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n\n    // both are not allowed\n    if (mode.lexemes && keywordPattern) {\n      throw new Error(\"ERR: Prefer `keywords.$pattern` to `mode.lexemes`, BOTH are not allowed. (see mode reference) \");\n    }\n\n    // `mode.lexemes` was the old standard before we added and now recommend\n    // using `keywords.$pattern` to pass the keyword pattern\n    keywordPattern = keywordPattern || mode.lexemes || /\\w+/;\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(mode.begin);\n      if (mode.endSameAsBegin) mode.end = mode.begin;\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(mode.end);\n      cmode.terminatorEnd = source(mode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */ (mode.illegal));\n    if (!mode.contains) mode.contains = [];\n\n    mode.contains = [].concat(...mode.contains.map(function(c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    }));\n    mode.contains.forEach(function(c) { compileMode(/** @type Mode */ (c), cmode); });\n\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit(language.classNameAliases || {});\n\n  return compileMode(/** @type Mode */ (language));\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function(variant) {\n      return inherit(mode, { variants: null }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit(mode, { starts: mode.starts ? inherit(mode.starts) : null });\n  }\n\n  if (Object.isFrozen(mode)) {\n    return inherit(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\n\nvar version = \"10.7.3\";\n\n// @ts-nocheck\n\nfunction hasValueOrEmptyAttribute(value) {\n  return Boolean(value || value === \"\");\n}\n\nfunction BuildVuePlugin(hljs) {\n  const Component = {\n    props: [\"language\", \"code\", \"autodetect\"],\n    data: function() {\n      return {\n        detectedLanguage: \"\",\n        unknownLanguage: false\n      };\n    },\n    computed: {\n      className() {\n        if (this.unknownLanguage) return \"\";\n\n        return \"hljs \" + this.detectedLanguage;\n      },\n      highlighted() {\n        // no idea what language to use, return raw code\n        if (!this.autoDetect && !hljs.getLanguage(this.language)) {\n          console.warn(`The language \"${this.language}\" you specified could not be found.`);\n          this.unknownLanguage = true;\n          return escapeHTML(this.code);\n        }\n\n        let result = {};\n        if (this.autoDetect) {\n          result = hljs.highlightAuto(this.code);\n          this.detectedLanguage = result.language;\n        } else {\n          result = hljs.highlight(this.language, this.code, this.ignoreIllegals);\n          this.detectedLanguage = this.language;\n        }\n        return result.value;\n      },\n      autoDetect() {\n        return !this.language || hasValueOrEmptyAttribute(this.autodetect);\n      },\n      ignoreIllegals() {\n        return true;\n      }\n    },\n    // this avoids needing to use a whole Vue compilation pipeline just\n    // to build Highlight.js\n    render(createElement) {\n      return createElement(\"pre\", {}, [\n        createElement(\"code\", {\n          class: this.className,\n          domProps: { innerHTML: this.highlighted }\n        })\n      ]);\n    }\n    // template: `<pre><code :class=\"className\" v-html=\"highlighted\"></code></pre>`\n  };\n\n  const VuePlugin = {\n    install(Vue) {\n      Vue.component('highlightjs', Component);\n    }\n  };\n\n  return { Component, VuePlugin };\n}\n\n/* plugin itself */\n\n/** @type {HLJSPlugin} */\nconst mergeHTMLPlugin = {\n  \"after:highlightElement\": ({ el, result, text }) => {\n    const originalStream = nodeStream(el);\n    if (!originalStream.length) return;\n\n    const resultNode = document.createElement('div');\n    resultNode.innerHTML = result.value;\n    result.value = mergeStreams(originalStream, nodeStream(resultNode), text);\n  }\n};\n\n/* Stream merging support functions */\n\n/**\n * @typedef Event\n * @property {'start'|'stop'} event\n * @property {number} offset\n * @property {Node} node\n */\n\n/**\n * @param {Node} node\n */\nfunction tag(node) {\n  return node.nodeName.toLowerCase();\n}\n\n/**\n * @param {Node} node\n */\nfunction nodeStream(node) {\n  /** @type Event[] */\n  const result = [];\n  (function _nodeStream(node, offset) {\n    for (let child = node.firstChild; child; child = child.nextSibling) {\n      if (child.nodeType === 3) {\n        offset += child.nodeValue.length;\n      } else if (child.nodeType === 1) {\n        result.push({\n          event: 'start',\n          offset: offset,\n          node: child\n        });\n        offset = _nodeStream(child, offset);\n        // Prevent void elements from having an end tag that would actually\n        // double them in the output. There are more void elements in HTML\n        // but we list only those realistically expected in code display.\n        if (!tag(child).match(/br|hr|img|input/)) {\n          result.push({\n            event: 'stop',\n            offset: offset,\n            node: child\n          });\n        }\n      }\n    }\n    return offset;\n  })(node, 0);\n  return result;\n}\n\n/**\n * @param {any} original - the original stream\n * @param {any} highlighted - stream of the highlighted source\n * @param {string} value - the original source itself\n */\nfunction mergeStreams(original, highlighted, value) {\n  let processed = 0;\n  let result = '';\n  const nodeStack = [];\n\n  function selectStream() {\n    if (!original.length || !highlighted.length) {\n      return original.length ? original : highlighted;\n    }\n    if (original[0].offset !== highlighted[0].offset) {\n      return (original[0].offset < highlighted[0].offset) ? original : highlighted;\n    }\n\n    /*\n    To avoid starting the stream just before it should stop the order is\n    ensured that original always starts first and closes last:\n\n    if (event1 == 'start' && event2 == 'start')\n      return original;\n    if (event1 == 'start' && event2 == 'stop')\n      return highlighted;\n    if (event1 == 'stop' && event2 == 'start')\n      return original;\n    if (event1 == 'stop' && event2 == 'stop')\n      return highlighted;\n\n    ... which is collapsed to:\n    */\n    return highlighted[0].event === 'start' ? original : highlighted;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  function open(node) {\n    /** @param {Attr} attr */\n    function attributeString(attr) {\n      return ' ' + attr.nodeName + '=\"' + escapeHTML(attr.value) + '\"';\n    }\n    // @ts-ignore\n    result += '<' + tag(node) + [].map.call(node.attributes, attributeString).join('') + '>';\n  }\n\n  /**\n   * @param {Node} node\n   */\n  function close(node) {\n    result += '</' + tag(node) + '>';\n  }\n\n  /**\n   * @param {Event} event\n   */\n  function render(event) {\n    (event.event === 'start' ? open : close)(event.node);\n  }\n\n  while (original.length || highlighted.length) {\n    let stream = selectStream();\n    result += escapeHTML(value.substring(processed, stream[0].offset));\n    processed = stream[0].offset;\n    if (stream === original) {\n      /*\n      On any opening or closing tag of the original markup we first close\n      the entire highlighted node stack, then render the original tag along\n      with all the following original tags at the same offset and then\n      reopen all the tags on the highlighted stack.\n      */\n      nodeStack.reverse().forEach(close);\n      do {\n        render(stream.splice(0, 1)[0]);\n        stream = selectStream();\n      } while (stream === original && stream.length && stream[0].offset === processed);\n      nodeStack.reverse().forEach(open);\n    } else {\n      if (stream[0].event === 'start') {\n        nodeStack.push(stream[0].node);\n      } else {\n        nodeStack.pop();\n      }\n      render(stream.splice(0, 1)[0]);\n    }\n  }\n  return result + escapeHTML(value.substr(processed));\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-747275419\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nconst seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nconst error = (message) => {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nconst warn = (message, ...args) => {\n  console.log(`WARN: ${message}`, ...args);\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nconst deprecated = (version, message) => {\n  if (seenDeprecations[`${version}/${message}`]) return;\n\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n\nconst escape$1 = escapeHTML;\nconst inherit$1 = inherit;\nconst NO_MATCH = Symbol(\"nomatch\");\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nconst HLJS = function(hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  const languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  const aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  const plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  let SAFE_MODE = true;\n  const fixMarkupRe = /(^(<[^>]+>|\\t|)+|\\n)/gm;\n  const LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  const PLAINTEXT_LANGUAGE = { disableAutodetect: true, name: 'Plain text', contains: [] };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  let options = {\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    tabReplace: null,\n    useBR: false,\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    let classes = block.className + ' ';\n\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    const match = options.languageDetectRe.exec(classes);\n    if (match) {\n      const language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n\n    return classes\n      .split(/\\s+/)\n      .find((_class) => shouldNotHighlight(_class) || getLanguage(_class));\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrlanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode} [continuation] - current continuation mode, if any\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrlanguageName, optionsOrCode, ignoreIllegals, continuation) {\n    let code = \"\";\n    let languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrlanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n      // continuation not supported at all via the new API\n      // eslint-disable-next-line no-undefined\n      continuation = undefined;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrlanguageName;\n      code = optionsOrCode;\n    }\n\n    /** @type {BeforeHighlightContext} */\n    const context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    const result = context.result\n      ? context.result\n      : _highlight(context.language, context.code, ignoreIllegals, continuation);\n\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {RegExpMatchArray} match - regexp match data\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, match) {\n      const matchText = language.case_insensitive ? match[0].toLowerCase() : match[0];\n      return Object.prototype.hasOwnProperty.call(mode.keywords, matchText) && mode.keywords[matchText];\n    }\n\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n\n      let lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      let match = top.keywordPatternRe.exec(modeBuffer);\n      let buf = \"\";\n\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        const data = keywordData(top, match);\n        if (data) {\n          const [kind, keywordRelevance] = data;\n          emitter.addText(buf);\n          buf = \"\";\n\n          relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            const cssClass = language.classNameAliases[kind] || kind;\n            emitter.addKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substr(lastIndex);\n      emitter.addText(buf);\n    }\n\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      let result = null;\n\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */ (result.top);\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.addSublanguage(result.emitter, result.language);\n    }\n\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {Mode} mode - new mode to start\n     */\n    function startNewMode(mode) {\n      if (mode.className) {\n        emitter.openNode(language.classNameAliases[mode.className] || mode.className);\n      }\n      top = Object.create(mode, { parent: { value: top } });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      let matched = startsWith(mode.endRe, matchPlusRemainder);\n\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          const resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexs to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      const lexeme = match[0];\n      const newMode = match.rule;\n\n      const resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      const beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (const cb of beforeCallbacks) {\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n\n      if (newMode && newMode.endSameAsBegin) {\n        newMode.endRe = escape(lexeme);\n      }\n\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode);\n      // if (mode[\"after:begin\"]) {\n      //   let resp = new Response(mode);\n      //   mode[\"after:begin\"](match, resp);\n      // }\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      const lexeme = match[0];\n      const matchPlusRemainder = codeToHighlight.substr(match.index);\n\n      const endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) { return NO_MATCH; }\n\n      const origin = top;\n      if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.className) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        if (endMode.endSameAsBegin) {\n          endMode.starts.endRe = endMode.endRe;\n        }\n        startNewMode(endMode.starts);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n\n    function processContinuations() {\n      const list = [];\n      for (let current = top; current !== language; current = current.parent) {\n        if (current.className) {\n          list.unshift(current.className);\n        }\n      }\n      list.forEach(item => emitter.openNode(item));\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    let lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceeding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      const lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          const err = new Error('0 width match regex');\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        const err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.className || '<unnamed>') + '\"');\n        err.mode = top;\n        throw err;\n      } else if (match.type === \"end\") {\n        const processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        const err = new Error('potential infinite loop, way more iterations than matches');\n        throw err;\n      }\n\n      /*\n      Why might be find ourselves here?  Only one occasion now.  An end match that was\n      triggered but could not be completed.  When might this happen?  When an `endSameasBegin`\n      rule sets the end rule to a specific match.  Since the overall mode termination rule that's\n      being used to scan the text isn't recompiled that means that any match that LOOKS like\n      the end (but is not, because it is not an exact match to the beginning) will\n      end up here.  A definite end match, but when `doEndMatch` tries to \"reapply\"\n      the end rule and fails to match, we wind up here, and just silently ignore the end.\n\n      This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n\n    const language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n\n    const md = compileLanguage(language, { plugins });\n    let result = '';\n    /** @type {CompiledMode} */\n    let top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    const continuations = {}; // keep continuations for sub-languages\n    const emitter = new options.__emitter(options);\n    processContinuations();\n    let modeBuffer = '';\n    let relevance = 0;\n    let index = 0;\n    let iterations = 0;\n    let resumeScanAtSamePosition = false;\n\n    try {\n      top.matcher.considerAll();\n\n      for (;;) {\n        iterations++;\n        if (resumeScanAtSamePosition) {\n          // only regexes not matched previously will now be\n          // considered for a potential match\n          resumeScanAtSamePosition = false;\n        } else {\n          top.matcher.considerAll();\n        }\n        top.matcher.lastIndex = index;\n\n        const match = top.matcher.exec(codeToHighlight);\n        // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n        if (!match) break;\n\n        const beforeMatch = codeToHighlight.substring(index, match.index);\n        const processedCount = processLexeme(beforeMatch, match);\n        index = match.index + processedCount;\n      }\n      processLexeme(codeToHighlight.substr(index));\n      emitter.closeAllNodes();\n      emitter.finalize();\n      result = emitter.toHTML();\n\n      return {\n        // avoid possible breakage with v10 clients expecting\n        // this to always be an integer\n        relevance: Math.floor(relevance),\n        value: result,\n        language: languageName,\n        illegal: false,\n        emitter: emitter,\n        top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          illegal: true,\n          illegalBy: {\n            msg: err.message,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode\n          },\n          sofar: result,\n          relevance: 0,\n          value: escape$1(codeToHighlight),\n          emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          illegal: false,\n          relevance: 0,\n          value: escape$1(codeToHighlight),\n          emitter: emitter,\n          language: languageName,\n          top: top,\n          errorRaised: err\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    const result = {\n      relevance: 0,\n      emitter: new options.__emitter(options),\n      value: escape$1(code),\n      illegal: false,\n      top: PLAINTEXT_LANGUAGE\n    };\n    result.emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n\n  - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - second_best (object with the same structure for second-best heuristically\n    detected language, may be absent)\n\n    @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    const plaintext = justTextHighlightResult(code);\n\n    const results = languageSubset.filter(getLanguage).filter(autoDetection).map(name =>\n      _highlight(name, code, false)\n    );\n    results.unshift(plaintext); // plaintext is always an option\n\n    const sorted = results.sort((a, b) => {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n\n    const [best, secondBest] = sorted;\n\n    /** @type {AutoHighlightResult} */\n    const result = best;\n    result.second_best = secondBest;\n\n    return result;\n  }\n\n  /**\n  Post-processing of the highlighted markup:\n\n  - replace TABs with something more useful\n  - replace real line-breaks with '<br>' for non-pre containers\n\n    @param {string} html\n    @returns {string}\n  */\n  function fixMarkup(html) {\n    if (!(options.tabReplace || options.useBR)) {\n      return html;\n    }\n\n    return html.replace(fixMarkupRe, match => {\n      if (match === '\\n') {\n        return options.useBR ? '<br>' : match;\n      } else if (options.tabReplace) {\n        return match.replace(/\\t/g, options.tabReplace);\n      }\n      return match;\n    });\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    const language = currentLang ? aliases[currentLang] : resultLang;\n\n    element.classList.add(\"hljs\");\n    if (language) element.classList.add(language);\n  }\n\n  /** @type {HLJSPlugin} */\n  const brPlugin = {\n    \"before:highlightElement\": ({ el }) => {\n      if (options.useBR) {\n        el.innerHTML = el.innerHTML.replace(/\\n/g, '').replace(/<br[ /]*>/g, '\\n');\n      }\n    },\n    \"after:highlightElement\": ({ result }) => {\n      if (options.useBR) {\n        result.value = result.value.replace(/\\n/g, \"<br>\");\n      }\n    }\n  };\n\n  const TAB_REPLACE_RE = /^(<[^>]+>|\\t)+/gm;\n  /** @type {HLJSPlugin} */\n  const tabReplacePlugin = {\n    \"after:highlightElement\": ({ result }) => {\n      if (options.tabReplace) {\n        result.value = result.value.replace(TAB_REPLACE_RE, (m) =>\n          m.replace(/\\t/g, options.tabReplace)\n        );\n      }\n    }\n  };\n\n  /**\n   * Applies highlighting to a DOM node containing code. Accepts a DOM node and\n   * two optional parameters for fixMarkup.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    let node = null;\n    const language = blockLanguage(element);\n\n    if (shouldNotHighlight(language)) return;\n\n    // support for v10 API\n    fire(\"before:highlightElement\",\n      { el: element, language: language });\n\n    node = element;\n    const text = node.textContent;\n    const result = language ? highlight(text, { language, ignoreIllegals: true }) : highlightAuto(text);\n\n    // support for v10 API\n    fire(\"after:highlightElement\", { el: element, result, text });\n\n    element.innerHTML = result.value;\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relavance: result.relevance\n    };\n    if (result.second_best) {\n      element.second_best = {\n        language: result.second_best.language,\n        // TODO: remove with version 11.0\n        re: result.second_best.relevance,\n        relavance: result.second_best.relevance\n      };\n    }\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    if (userOptions.useBR) {\n      deprecated(\"10.3.0\", \"'useBR' will be removed entirely in v11.0\");\n      deprecated(\"10.3.0\", \"Please see https://github.com/highlightjs/highlight.js/issues/2559\");\n    }\n    options = inherit$1(options, userOptions);\n  }\n\n  /**\n   * Highlights to all <pre><code> blocks on a page\n   *\n   * @type {Function & {called?: boolean}}\n   */\n  // TODO: remove v12, deprecated\n  const initHighlighting = () => {\n    if (initHighlighting.called) return;\n    initHighlighting.called = true;\n\n    deprecated(\"10.6.0\", \"initHighlighting() is deprecated.  Use highlightAll() instead.\");\n\n    const blocks = document.querySelectorAll('pre code');\n    blocks.forEach(highlightElement);\n  };\n\n  // Higlights all when DOMContentLoaded fires\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() is deprecated.  Use highlightAll() instead.\");\n    wantsHighlight = true;\n  }\n\n  let wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      wantsHighlight = true;\n      return;\n    }\n\n    const blocks = document.querySelectorAll('pre code');\n    blocks.forEach(highlightElement);\n  }\n\n  function boot() {\n    // if a highlight was requested before DOM was loaded, do now\n    if (wantsHighlight) highlightAll();\n  }\n\n  // make sure we are in the browser environment\n  if (typeof window !== 'undefined' && window.addEventListener) {\n    window.addEventListener('DOMContentLoaded', boot, false);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    let lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) { throw error$1; } else { error(error$1); }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n\n    if (lang.aliases) {\n      registerAliases(lang.aliases, { languageName });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (const alias of Object.keys(aliases)) {\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n    intended usage: When one language truly requires another\n\n    Unlike `getLanguage`, this will throw when the requested language\n    is not available.\n\n    @param {string} name - name of the language to fetch/require\n    @returns {Language | never}\n  */\n  function requireLanguage(name) {\n    deprecated(\"10.4.0\", \"requireLanguage will be removed entirely in v11.\");\n    deprecated(\"10.4.0\", \"Please see https://github.com/highlightjs/highlight.js/pull/2844\");\n\n    const lang = getLanguage(name);\n    if (lang) { return lang; }\n\n    const err = new Error('The \\'{}\\' language is required, but not loaded.'.replace('{}', name));\n    throw err;\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, { languageName }) {\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(alias => { aliases[alias.toLowerCase()] = languageName; });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    const lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = (data) => {\n        plugin[\"before:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = (data) => {\n        plugin[\"after:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    const cb = event;\n    plugins.forEach(function(plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n  Note: fixMarkup is deprecated and will be removed entirely in v11\n\n  @param {string} arg\n  @returns {string}\n  */\n  function deprecateFixMarkup(arg) {\n    deprecated(\"10.2.0\", \"fixMarkup will be removed entirely in v11.0\");\n    deprecated(\"10.2.0\", \"Please see https://github.com/highlightjs/highlight.js/issues/2534\");\n\n    return fixMarkup(arg);\n  }\n\n  /**\n   *\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    fixMarkup: deprecateFixMarkup,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    requireLanguage,\n    autoDetection,\n    inherit: inherit$1,\n    addPlugin,\n    // plugins for frameworks\n    vuePlugin: BuildVuePlugin(hljs).VuePlugin\n  });\n\n  hljs.debugMode = function() { SAFE_MODE = false; };\n  hljs.safeMode = function() { SAFE_MODE = true; };\n  hljs.versionString = version;\n\n  for (const key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreezeEs6(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexs into our main object\n  Object.assign(hljs, MODES);\n\n  // built-in plugins, likely to be moved out of core in the future\n  hljs.addPlugin(brPlugin); // slated to be removed in v11\n  hljs.addPlugin(mergeHTMLPlugin);\n  hljs.addPlugin(tabReplacePlugin);\n  return hljs;\n};\n\n// export an \"instance\" of the highlighter\nvar highlight = HLJS({});\n\nmodule.exports = highlight;\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,GAAG,EAAE;EACrB,IAAIA,GAAG,YAAYC,GAAG,EAAE;IACpBD,GAAG,CAACE,KAAK,GAAGF,GAAG,CAACG,MAAM,GAAGH,GAAG,CAACI,GAAG,GAAG,YAAY;MAC3C,MAAM,IAAIC,KAAK,CAAC,kBAAkB,CAAC;IACvC,CAAC;EACL,CAAC,MAAM,IAAIL,GAAG,YAAYM,GAAG,EAAE;IAC3BN,GAAG,CAACO,GAAG,GAAGP,GAAG,CAACE,KAAK,GAAGF,GAAG,CAACG,MAAM,GAAG,YAAY;MAC3C,MAAM,IAAIE,KAAK,CAAC,kBAAkB,CAAC;IACvC,CAAC;EACL;;EAEA;EACAG,MAAM,CAACC,MAAM,CAACT,GAAG,CAAC;EAElBQ,MAAM,CAACE,mBAAmB,CAACV,GAAG,CAAC,CAACW,OAAO,CAAC,UAAUC,IAAI,EAAE;IACpD,IAAIC,IAAI,GAAGb,GAAG,CAACY,IAAI,CAAC;;IAEpB;IACA,IAAI,OAAOC,IAAI,IAAI,QAAQ,IAAI,CAACL,MAAM,CAACM,QAAQ,CAACD,IAAI,CAAC,EAAE;MACnDd,UAAU,CAACc,IAAI,CAAC;IACpB;EACJ,CAAC,CAAC;EAEF,OAAOb,GAAG;AACd;AAEA,IAAIe,aAAa,GAAGhB,UAAU;AAC9B,IAAIiB,QAAQ,GAAGjB,UAAU;AACzBgB,aAAa,CAACE,OAAO,GAAGD,QAAQ;;AAEhC;AACA,MAAME,QAAQ,CAAC;EACb;AACF;AACA;EACEC,WAAWA,CAACC,IAAI,EAAE;IAChB;IACA,IAAIA,IAAI,CAACC,IAAI,KAAKC,SAAS,EAAEF,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;IAE3C,IAAI,CAACA,IAAI,GAAGD,IAAI,CAACC,IAAI;IACrB,IAAI,CAACE,cAAc,GAAG,KAAK;EAC7B;EAEAC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACD,cAAc,GAAG,IAAI;EAC5B;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAOA,KAAK,CACTC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CACtBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CACvBA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,QAAQ,EAAE,GAAGC,OAAO,EAAE;EACrC;EACA,MAAMC,MAAM,GAAGvB,MAAM,CAACwB,MAAM,CAAC,IAAI,CAAC;EAElC,KAAK,MAAMC,GAAG,IAAIJ,QAAQ,EAAE;IAC1BE,MAAM,CAACE,GAAG,CAAC,GAAGJ,QAAQ,CAACI,GAAG,CAAC;EAC7B;EACAH,OAAO,CAACnB,OAAO,CAAC,UAASX,GAAG,EAAE;IAC5B,KAAK,MAAMiC,GAAG,IAAIjC,GAAG,EAAE;MACrB+B,MAAM,CAACE,GAAG,CAAC,GAAGjC,GAAG,CAACiC,GAAG,CAAC;IACxB;EACF,CAAC,CAAC;EACF,OAAO,gBAAkBF,MAAM;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,MAAMG,UAAU,GAAG,SAAS;;AAE5B;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAIC,IAAI,IAAK;EAClC,OAAO,CAAC,CAACA,IAAI,CAACC,IAAI;AACpB,CAAC;;AAED;AACA,MAAMC,YAAY,CAAC;EACjB;AACF;AACA;AACA;AACA;AACA;EACEnB,WAAWA,CAACoB,SAAS,EAAEC,OAAO,EAAE;IAC9B,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,WAAW,GAAGF,OAAO,CAACE,WAAW;IACtCH,SAAS,CAACI,IAAI,CAAC,IAAI,CAAC;EACtB;;EAEA;AACF;AACA;AACA;EACEC,OAAOA,CAACC,IAAI,EAAE;IACZ,IAAI,CAACJ,MAAM,IAAIhB,UAAU,CAACoB,IAAI,CAAC;EACjC;;EAEA;AACF;AACA;AACA;EACEC,QAAQA,CAACV,IAAI,EAAE;IACb,IAAI,CAACD,iBAAiB,CAACC,IAAI,CAAC,EAAE;IAE9B,IAAIW,SAAS,GAAGX,IAAI,CAACC,IAAI;IACzB,IAAI,CAACD,IAAI,CAACY,WAAW,EAAE;MACrBD,SAAS,GAAG,GAAG,IAAI,CAACL,WAAW,GAAGK,SAAS,EAAE;IAC/C;IACA,IAAI,CAACE,IAAI,CAACF,SAAS,CAAC;EACtB;;EAEA;AACF;AACA;AACA;EACEG,SAASA,CAACd,IAAI,EAAE;IACd,IAAI,CAACD,iBAAiB,CAACC,IAAI,CAAC,EAAE;IAE9B,IAAI,CAACK,MAAM,IAAIP,UAAU;EAC3B;;EAEA;AACF;AACA;EACER,KAAKA,CAAA,EAAG;IACN,OAAO,IAAI,CAACe,MAAM;EACpB;;EAEA;;EAEA;AACF;AACA;AACA;EACEQ,IAAIA,CAACF,SAAS,EAAE;IACd,IAAI,CAACN,MAAM,IAAI,gBAAgBM,SAAS,IAAI;EAC9C;AACF;;AAEA;AACA;AACA;;AAEA,MAAMI,SAAS,CAAC;EACdhC,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAACiC,QAAQ,GAAG;MAAEC,QAAQ,EAAE;IAAG,CAAC;IAChC,IAAI,CAACC,KAAK,GAAG,CAAC,IAAI,CAACF,QAAQ,CAAC;EAC9B;EAEA,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEA,IAAIC,IAAIA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACL,QAAQ;EAAE;;EAEnC;EACA7C,GAAGA,CAAC6B,IAAI,EAAE;IACR,IAAI,CAACmB,GAAG,CAACF,QAAQ,CAACK,IAAI,CAACtB,IAAI,CAAC;EAC9B;;EAEA;EACAU,QAAQA,CAACT,IAAI,EAAE;IACb;IACA,MAAMD,IAAI,GAAG;MAAEC,IAAI;MAAEgB,QAAQ,EAAE;IAAG,CAAC;IACnC,IAAI,CAAC9C,GAAG,CAAC6B,IAAI,CAAC;IACd,IAAI,CAACkB,KAAK,CAACI,IAAI,CAACtB,IAAI,CAAC;EACvB;EAEAc,SAASA,CAAA,EAAG;IACV,IAAI,IAAI,CAACI,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MACzB,OAAO,IAAI,CAACF,KAAK,CAACK,GAAG,CAAC,CAAC;IACzB;IACA;IACA,OAAOrC,SAAS;EAClB;EAEAsC,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAACV,SAAS,CAAC,CAAC,CAAC;EAC1B;EAEAW,MAAMA,CAAA,EAAG;IACP,OAAOC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACX,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;EACET,IAAIA,CAACqB,OAAO,EAAE;IACZ;IACA,OAAO,IAAI,CAAC7C,WAAW,CAAC8C,KAAK,CAACD,OAAO,EAAE,IAAI,CAACZ,QAAQ,CAAC;IACrD;IACA;EACF;;EAEA;AACF;AACA;AACA;EACE,OAAOa,KAAKA,CAACD,OAAO,EAAE5B,IAAI,EAAE;IAC1B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B4B,OAAO,CAACpB,OAAO,CAACR,IAAI,CAAC;IACvB,CAAC,MAAM,IAAIA,IAAI,CAACiB,QAAQ,EAAE;MACxBW,OAAO,CAAClB,QAAQ,CAACV,IAAI,CAAC;MACtBA,IAAI,CAACiB,QAAQ,CAAC1C,OAAO,CAAEuD,KAAK,IAAK,IAAI,CAACD,KAAK,CAACD,OAAO,EAAEE,KAAK,CAAC,CAAC;MAC5DF,OAAO,CAACd,SAAS,CAACd,IAAI,CAAC;IACzB;IACA,OAAO4B,OAAO;EAChB;;EAEA;AACF;AACA;EACE,OAAOG,SAASA,CAAC/B,IAAI,EAAE;IACrB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC9B,IAAI,CAACA,IAAI,CAACiB,QAAQ,EAAE;IAEpB,IAAIjB,IAAI,CAACiB,QAAQ,CAACe,KAAK,CAACC,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,CAAC,EAAE;MACrD;MACA;MACAjC,IAAI,CAACiB,QAAQ,GAAG,CAACjB,IAAI,CAACiB,QAAQ,CAACiB,IAAI,CAAC,EAAE,CAAC,CAAC;IAC1C,CAAC,MAAM;MACLlC,IAAI,CAACiB,QAAQ,CAAC1C,OAAO,CAAEuD,KAAK,IAAK;QAC/Bf,SAAS,CAACgB,SAAS,CAACD,KAAK,CAAC;MAC5B,CAAC,CAAC;IACJ;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAMK,gBAAgB,SAASpB,SAAS,CAAC;EACvC;AACF;AACA;EACEhC,WAAWA,CAACqB,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;;EAEA;AACF;AACA;AACA;EACEgC,UAAUA,CAAC3B,IAAI,EAAER,IAAI,EAAE;IACrB,IAAIQ,IAAI,KAAK,EAAE,EAAE;MAAE;IAAQ;IAE3B,IAAI,CAACC,QAAQ,CAACT,IAAI,CAAC;IACnB,IAAI,CAACO,OAAO,CAACC,IAAI,CAAC;IAClB,IAAI,CAACK,SAAS,CAAC,CAAC;EAClB;;EAEA;AACF;AACA;EACEN,OAAOA,CAACC,IAAI,EAAE;IACZ,IAAIA,IAAI,KAAK,EAAE,EAAE;MAAE;IAAQ;IAE3B,IAAI,CAACtC,GAAG,CAACsC,IAAI,CAAC;EAChB;;EAEA;AACF;AACA;AACA;EACE4B,cAAcA,CAACC,OAAO,EAAE9D,IAAI,EAAE;IAC5B;IACA,MAAMwB,IAAI,GAAGsC,OAAO,CAACjB,IAAI;IACzBrB,IAAI,CAACC,IAAI,GAAGzB,IAAI;IAChBwB,IAAI,CAACY,WAAW,GAAG,IAAI;IACvB,IAAI,CAACzC,GAAG,CAAC6B,IAAI,CAAC;EAChB;EAEAuC,MAAMA,CAAA,EAAG;IACP,MAAMC,QAAQ,GAAG,IAAItC,YAAY,CAAC,IAAI,EAAE,IAAI,CAACE,OAAO,CAAC;IACrD,OAAOoC,QAAQ,CAAClD,KAAK,CAAC,CAAC;EACzB;EAEAmD,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACpD,KAAK,EAAE;EACrB,OAAO,IAAIqD,MAAM,CAACrD,KAAK,CAACC,OAAO,CAAC,uBAAuB,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC;AACxE;;AAEA;AACA;AACA;AACA;AACA,SAASqD,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAAChB,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOc,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,MAAMA,CAAC,GAAGJ,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAG,GAAG,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAAChB,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC/D,OAAOc,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA,SAASI,gBAAgBA,CAACP,EAAE,EAAE;EAC5B,OAAQ,IAAIF,MAAM,CAACE,EAAE,CAACQ,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAEC,IAAI,CAAC,EAAE,CAAC,CAAClC,MAAM,GAAG,CAAC;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASmC,UAAUA,CAACV,EAAE,EAAEW,MAAM,EAAE;EAC9B,MAAMC,KAAK,GAAGZ,EAAE,IAAIA,EAAE,CAACS,IAAI,CAACE,MAAM,CAAC;EACnC,OAAOC,KAAK,IAAIA,KAAK,CAACC,KAAK,KAAK,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,gDAAgD;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASzB,IAAIA,CAAC0B,OAAO,EAAEC,SAAS,GAAG,GAAG,EAAE;EACtC,IAAIC,WAAW,GAAG,CAAC;EAEnB,OAAOF,OAAO,CAACX,GAAG,CAAEc,KAAK,IAAK;IAC5BD,WAAW,IAAI,CAAC;IAChB,MAAME,MAAM,GAAGF,WAAW;IAC1B,IAAIjB,EAAE,GAAGD,MAAM,CAACmB,KAAK,CAAC;IACtB,IAAIE,GAAG,GAAG,EAAE;IAEZ,OAAOpB,EAAE,CAACzB,MAAM,GAAG,CAAC,EAAE;MACpB,MAAMqC,KAAK,GAAGE,UAAU,CAACL,IAAI,CAACT,EAAE,CAAC;MACjC,IAAI,CAACY,KAAK,EAAE;QACVQ,GAAG,IAAIpB,EAAE;QACT;MACF;MACAoB,GAAG,IAAIpB,EAAE,CAACqB,SAAS,CAAC,CAAC,EAAET,KAAK,CAACC,KAAK,CAAC;MACnCb,EAAE,GAAGA,EAAE,CAACqB,SAAS,CAACT,KAAK,CAACC,KAAK,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACrC,MAAM,CAAC;MAChD,IAAIqC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACpC;QACAQ,GAAG,IAAI,IAAI,GAAGE,MAAM,CAACC,MAAM,CAACX,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGO,MAAM,CAAC;MACjD,CAAC,MAAM;QACLC,GAAG,IAAIR,KAAK,CAAC,CAAC,CAAC;QACf,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACpBK,WAAW,EAAE;QACf;MACF;IACF;IACA,OAAOG,GAAG;EACZ,CAAC,CAAC,CAAChB,GAAG,CAACJ,EAAE,IAAI,IAAIA,EAAE,GAAG,CAAC,CAACX,IAAI,CAAC2B,SAAS,CAAC;AACzC;;AAEA;AACA,MAAMQ,gBAAgB,GAAG,MAAM;AAC/B,MAAMC,QAAQ,GAAG,cAAc;AAC/B,MAAMC,mBAAmB,GAAG,eAAe;AAC3C,MAAMC,SAAS,GAAG,mBAAmB;AACrC,MAAMC,WAAW,GAAG,wEAAwE,CAAC,CAAC;AAC9F,MAAMC,gBAAgB,GAAG,cAAc,CAAC,CAAC;AACzC,MAAMC,cAAc,GAAG,8IAA8I;;AAErK;AACA;AACA;AACA,MAAMC,OAAO,GAAGA,CAACC,IAAI,GAAG,CAAC,CAAC,KAAK;EAC7B,MAAMC,YAAY,GAAG,WAAW;EAChC,IAAID,IAAI,CAACE,MAAM,EAAE;IACfF,IAAI,CAACG,KAAK,GAAGlC,MAAM,CACjBgC,YAAY,EACZ,MAAM,EACND,IAAI,CAACE,MAAM,EACX,MAAM,CAAC;EACX;EACA,OAAOvF,OAAO,CAAC;IACbmB,SAAS,EAAE,MAAM;IACjBqE,KAAK,EAAEF,YAAY;IACnBG,GAAG,EAAE,GAAG;IACRC,SAAS,EAAE,CAAC;IACZ;IACA,UAAU,EAAEC,CAACC,CAAC,EAAEC,IAAI,KAAK;MACvB,IAAID,CAAC,CAAC1B,KAAK,KAAK,CAAC,EAAE2B,IAAI,CAACjG,WAAW,CAAC,CAAC;IACvC;EACF,CAAC,EAAEyF,IAAI,CAAC;AACV,CAAC;;AAED;AACA,MAAMS,gBAAgB,GAAG;EACvBN,KAAK,EAAE,cAAc;EAAEE,SAAS,EAAE;AACpC,CAAC;AACD,MAAMK,gBAAgB,GAAG;EACvB5E,SAAS,EAAE,QAAQ;EACnBqE,KAAK,EAAE,IAAI;EACXC,GAAG,EAAE,IAAI;EACTO,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,CAACH,gBAAgB;AAC7B,CAAC;AACD,MAAMI,iBAAiB,GAAG;EACxB/E,SAAS,EAAE,QAAQ;EACnBqE,KAAK,EAAE,GAAG;EACVC,GAAG,EAAE,GAAG;EACRO,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,CAACH,gBAAgB;AAC7B,CAAC;AACD,MAAMK,kBAAkB,GAAG;EACzBX,KAAK,EAAE;AACT,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,OAAO,GAAG,SAAAA,CAASZ,KAAK,EAAEC,GAAG,EAAEY,WAAW,GAAG,CAAC,CAAC,EAAE;EACrD,MAAM7G,IAAI,GAAGQ,OAAO,CAClB;IACEmB,SAAS,EAAE,SAAS;IACpBqE,KAAK;IACLC,GAAG;IACHQ,QAAQ,EAAE;EACZ,CAAC,EACDI,WACF,CAAC;EACD7G,IAAI,CAACyG,QAAQ,CAACnE,IAAI,CAACqE,kBAAkB,CAAC;EACtC3G,IAAI,CAACyG,QAAQ,CAACnE,IAAI,CAAC;IACjBX,SAAS,EAAE,QAAQ;IACnBqE,KAAK,EAAE,4CAA4C;IACnDE,SAAS,EAAE;EACb,CAAC,CAAC;EACF,OAAOlG,IAAI;AACb,CAAC;AACD,MAAM8G,mBAAmB,GAAGF,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;AAC9C,MAAMG,oBAAoB,GAAGH,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;AACpD,MAAMI,iBAAiB,GAAGJ,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;AAC3C,MAAMK,WAAW,GAAG;EAClBtF,SAAS,EAAE,QAAQ;EACnBqE,KAAK,EAAER,SAAS;EAChBU,SAAS,EAAE;AACb,CAAC;AACD,MAAMgB,aAAa,GAAG;EACpBvF,SAAS,EAAE,QAAQ;EACnBqE,KAAK,EAAEP,WAAW;EAClBS,SAAS,EAAE;AACb,CAAC;AACD,MAAMiB,kBAAkB,GAAG;EACzBxF,SAAS,EAAE,QAAQ;EACnBqE,KAAK,EAAEN,gBAAgB;EACvBQ,SAAS,EAAE;AACb,CAAC;AACD,MAAMkB,eAAe,GAAG;EACtBzF,SAAS,EAAE,QAAQ;EACnBqE,KAAK,EAAER,SAAS,GAAG,GAAG,GACpB,gBAAgB,GAChB,kBAAkB,GAClB,oBAAoB,GACpB,oBAAoB,GACpB,OAAO,GACP,SAAS,GACT,gBAAgB,GAChB,IAAI;EACNU,SAAS,EAAE;AACb,CAAC;AACD,MAAMmB,WAAW,GAAG;EAClB;EACA;EACA;EACA;EACA;EACA;EACArB,KAAK,EAAE,iBAAiB;EACxBS,QAAQ,EAAE,CAAC;IACT9E,SAAS,EAAE,QAAQ;IACnBqE,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,YAAY;IACjBO,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CACRH,gBAAgB,EAChB;MACEN,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTC,SAAS,EAAE,CAAC;MACZO,QAAQ,EAAE,CAACH,gBAAgB;IAC7B,CAAC;EAEL,CAAC;AACH,CAAC;AACD,MAAMgB,UAAU,GAAG;EACjB3F,SAAS,EAAE,OAAO;EAClBqE,KAAK,EAAEV,QAAQ;EACfY,SAAS,EAAE;AACb,CAAC;AACD,MAAMqB,qBAAqB,GAAG;EAC5B5F,SAAS,EAAE,OAAO;EAClBqE,KAAK,EAAET,mBAAmB;EAC1BW,SAAS,EAAE;AACb,CAAC;AACD,MAAMsB,YAAY,GAAG;EACnB;EACAxB,KAAK,EAAE,SAAS,GAAGT,mBAAmB;EACtCW,SAAS,EAAE;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,iBAAiB,GAAG,SAAAA,CAASzH,IAAI,EAAE;EACvC,OAAOZ,MAAM,CAACsI,MAAM,CAAC1H,IAAI,EACvB;IACE;IACA,UAAU,EAAEmG,CAACC,CAAC,EAAEC,IAAI,KAAK;MAAEA,IAAI,CAACpG,IAAI,CAAC0H,WAAW,GAAGvB,CAAC,CAAC,CAAC,CAAC;IAAE,CAAC;IAC1D;IACA,QAAQ,EAAEwB,CAACxB,CAAC,EAAEC,IAAI,KAAK;MAAE,IAAIA,IAAI,CAACpG,IAAI,CAAC0H,WAAW,KAAKvB,CAAC,CAAC,CAAC,CAAC,EAAEC,IAAI,CAACjG,WAAW,CAAC,CAAC;IAAE;EACnF,CAAC,CAAC;AACN,CAAC;AAED,IAAIyH,KAAK,GAAG,aAAazI,MAAM,CAACC,MAAM,CAAC;EACnCyI,SAAS,EAAE,IAAI;EACfzC,gBAAgB,EAAEA,gBAAgB;EAClCC,QAAQ,EAAEA,QAAQ;EAClBC,mBAAmB,EAAEA,mBAAmB;EACxCC,SAAS,EAAEA,SAAS;EACpBC,WAAW,EAAEA,WAAW;EACxBC,gBAAgB,EAAEA,gBAAgB;EAClCC,cAAc,EAAEA,cAAc;EAC9BC,OAAO,EAAEA,OAAO;EAChBU,gBAAgB,EAAEA,gBAAgB;EAClCC,gBAAgB,EAAEA,gBAAgB;EAClCG,iBAAiB,EAAEA,iBAAiB;EACpCC,kBAAkB,EAAEA,kBAAkB;EACtCC,OAAO,EAAEA,OAAO;EAChBE,mBAAmB,EAAEA,mBAAmB;EACxCC,oBAAoB,EAAEA,oBAAoB;EAC1CC,iBAAiB,EAAEA,iBAAiB;EACpCC,WAAW,EAAEA,WAAW;EACxBC,aAAa,EAAEA,aAAa;EAC5BC,kBAAkB,EAAEA,kBAAkB;EACtCC,eAAe,EAAEA,eAAe;EAChCC,WAAW,EAAEA,WAAW;EACxBC,UAAU,EAAEA,UAAU;EACtBC,qBAAqB,EAAEA,qBAAqB;EAC5CC,YAAY,EAAEA,YAAY;EAC1BC,iBAAiB,EAAEA;AACvB,CAAC,CAAC;;AAEF;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,qBAAqBA,CAACtD,KAAK,EAAEuD,QAAQ,EAAE;EAC9C,MAAMC,MAAM,GAAGxD,KAAK,CAACyD,KAAK,CAACzD,KAAK,CAACC,KAAK,GAAG,CAAC,CAAC;EAC3C,IAAIuD,MAAM,KAAK,GAAG,EAAE;IAClBD,QAAQ,CAAC5H,WAAW,CAAC,CAAC;EACxB;AACF;;AAGA;AACA;AACA;AACA;AACA,SAAS+H,aAAaA,CAACnI,IAAI,EAAEoI,MAAM,EAAE;EACnC,IAAI,CAACA,MAAM,EAAE;EACb,IAAI,CAACpI,IAAI,CAACmI,aAAa,EAAE;;EAEzB;EACA;EACA;EACA;EACA;EACAnI,IAAI,CAACgG,KAAK,GAAG,MAAM,GAAGhG,IAAI,CAACmI,aAAa,CAACE,KAAK,CAAC,GAAG,CAAC,CAACnF,IAAI,CAAC,GAAG,CAAC,GAAG,qBAAqB;EACrFlD,IAAI,CAACsI,aAAa,GAAGP,qBAAqB;EAC1C/H,IAAI,CAACuI,QAAQ,GAAGvI,IAAI,CAACuI,QAAQ,IAAIvI,IAAI,CAACmI,aAAa;EACnD,OAAOnI,IAAI,CAACmI,aAAa;;EAEzB;EACA;EACA;EACA,IAAInI,IAAI,CAACkG,SAAS,KAAKhG,SAAS,EAAEF,IAAI,CAACkG,SAAS,GAAG,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA,SAASsC,cAAcA,CAACxI,IAAI,EAAEyI,OAAO,EAAE;EACrC,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC3I,IAAI,CAACwG,OAAO,CAAC,EAAE;EAElCxG,IAAI,CAACwG,OAAO,GAAGrC,MAAM,CAAC,GAAGnE,IAAI,CAACwG,OAAO,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA,SAASoC,YAAYA,CAAC5I,IAAI,EAAEyI,OAAO,EAAE;EACnC,IAAI,CAACzI,IAAI,CAACyE,KAAK,EAAE;EACjB,IAAIzE,IAAI,CAACgG,KAAK,IAAIhG,IAAI,CAACiG,GAAG,EAAE,MAAM,IAAIhH,KAAK,CAAC,0CAA0C,CAAC;EAEvFe,IAAI,CAACgG,KAAK,GAAGhG,IAAI,CAACyE,KAAK;EACvB,OAAOzE,IAAI,CAACyE,KAAK;AACnB;;AAEA;AACA;AACA;AACA;AACA,SAASoE,gBAAgBA,CAAC7I,IAAI,EAAEyI,OAAO,EAAE;EACvC;EACA,IAAIzI,IAAI,CAACkG,SAAS,KAAKhG,SAAS,EAAEF,IAAI,CAACkG,SAAS,GAAG,CAAC;AACtD;;AAEA;AACA,MAAM4C,eAAe,GAAG,CACtB,IAAI,EACJ,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,QAAQ;AAAE;AACV,MAAM;AAAE;AACR,OAAO,CAAC;AAAA,CACT;AAED,MAAMC,yBAAyB,GAAG,SAAS;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,WAAW,EAAEC,eAAe,EAAEvH,SAAS,GAAGoH,yBAAyB,EAAE;EAC5F;EACA,MAAMI,gBAAgB,GAAG,CAAC,CAAC;;EAE3B;EACA;EACA,IAAI,OAAOF,WAAW,KAAK,QAAQ,EAAE;IACnCG,WAAW,CAACzH,SAAS,EAAEsH,WAAW,CAACZ,KAAK,CAAC,GAAG,CAAC,CAAC;EAChD,CAAC,MAAM,IAAIK,KAAK,CAACC,OAAO,CAACM,WAAW,CAAC,EAAE;IACrCG,WAAW,CAACzH,SAAS,EAAEsH,WAAW,CAAC;EACrC,CAAC,MAAM;IACL7J,MAAM,CAACiK,IAAI,CAACJ,WAAW,CAAC,CAAC1J,OAAO,CAAC,UAASoC,SAAS,EAAE;MACnD;MACAvC,MAAM,CAACsI,MAAM,CACXyB,gBAAgB,EAChBH,eAAe,CAACC,WAAW,CAACtH,SAAS,CAAC,EAAEuH,eAAe,EAAEvH,SAAS,CACpE,CAAC;IACH,CAAC,CAAC;EACJ;EACA,OAAOwH,gBAAgB;;EAEvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,WAAWA,CAACzH,SAAS,EAAE2H,WAAW,EAAE;IAC3C,IAAIJ,eAAe,EAAE;MACnBI,WAAW,GAAGA,WAAW,CAACrF,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACqF,WAAW,CAAC,CAAC,CAAC;IACrD;IACAD,WAAW,CAAC/J,OAAO,CAAC,UAASiK,OAAO,EAAE;MACpC,MAAMC,IAAI,GAAGD,OAAO,CAACnB,KAAK,CAAC,GAAG,CAAC;MAC/Bc,gBAAgB,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC9H,SAAS,EAAE+H,eAAe,CAACD,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACF,OAAO,EAAEG,aAAa,EAAE;EAC/C;EACA;EACA,IAAIA,aAAa,EAAE;IACjB,OAAOvE,MAAM,CAACuE,aAAa,CAAC;EAC9B;EAEA,OAAOC,aAAa,CAACJ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;AACvC;;AAEA;AACA;AACA;AACA;AACA,SAASI,aAAaA,CAACJ,OAAO,EAAE;EAC9B,OAAOV,eAAe,CAACe,QAAQ,CAACL,OAAO,CAACD,WAAW,CAAC,CAAC,CAAC;AACxD;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,eAAeA,CAACC,QAAQ,EAAE;EAAEC;AAAQ,CAAC,EAAE;EAC9C;AACF;AACA;AACA;AACA;AACA;EACE,SAASC,MAAMA,CAAC3J,KAAK,EAAE4J,MAAM,EAAE;IAC7B,OAAO,IAAIvG,MAAM,CACfC,MAAM,CAACtD,KAAK,CAAC,EACb,GAAG,IAAIyJ,QAAQ,CAACI,gBAAgB,GAAG,GAAG,GAAG,EAAE,CAAC,IAAID,MAAM,GAAG,GAAG,GAAG,EAAE,CACnE,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAEE,MAAME,UAAU,CAAC;IACfrK,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACsK,YAAY,GAAG,CAAC,CAAC;MACtB;MACA,IAAI,CAACC,OAAO,GAAG,EAAE;MACjB,IAAI,CAACC,OAAO,GAAG,CAAC;MAChB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACnB;;IAEA;IACAC,OAAOA,CAAC5G,EAAE,EAAEgC,IAAI,EAAE;MAChBA,IAAI,CAAC2E,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;MAC/B;MACA,IAAI,CAACH,YAAY,CAAC,IAAI,CAACE,OAAO,CAAC,GAAG1E,IAAI;MACtC,IAAI,CAACyE,OAAO,CAAChI,IAAI,CAAC,CAACuD,IAAI,EAAEhC,EAAE,CAAC,CAAC;MAC7B,IAAI,CAAC0G,OAAO,IAAInG,gBAAgB,CAACP,EAAE,CAAC,GAAG,CAAC;IAC1C;IAEA6G,OAAOA,CAAA,EAAG;MACR,IAAI,IAAI,CAACJ,OAAO,CAAClI,MAAM,KAAK,CAAC,EAAE;QAC7B;QACA;QACA,IAAI,CAACkC,IAAI,GAAG,MAAM,IAAI;MACxB;MACA,MAAMqG,WAAW,GAAG,IAAI,CAACL,OAAO,CAACrG,GAAG,CAAChB,EAAE,IAAIA,EAAE,CAAC,CAAC,CAAC,CAAC;MACjD,IAAI,CAAC2H,SAAS,GAAGX,MAAM,CAAC/G,IAAI,CAACyH,WAAW,CAAC,EAAE,IAAI,CAAC;MAChD,IAAI,CAACE,SAAS,GAAG,CAAC;IACpB;;IAEA;IACAvG,IAAIA,CAACwG,CAAC,EAAE;MACN,IAAI,CAACF,SAAS,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS;MACzC,MAAMpG,KAAK,GAAG,IAAI,CAACmG,SAAS,CAACtG,IAAI,CAACwG,CAAC,CAAC;MACpC,IAAI,CAACrG,KAAK,EAAE;QAAE,OAAO,IAAI;MAAE;;MAE3B;MACA,MAAMsG,CAAC,GAAGtG,KAAK,CAACuG,SAAS,CAAC,CAAC/H,EAAE,EAAE8H,CAAC,KAAKA,CAAC,GAAG,CAAC,IAAI9H,EAAE,KAAK/C,SAAS,CAAC;MAC/D;MACA,MAAM+K,SAAS,GAAG,IAAI,CAACZ,YAAY,CAACU,CAAC,CAAC;MACtC;MACA;MACAtG,KAAK,CAACyG,MAAM,CAAC,CAAC,EAAEH,CAAC,CAAC;MAElB,OAAO3L,MAAM,CAACsI,MAAM,CAACjD,KAAK,EAAEwG,SAAS,CAAC;IACxC;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EASE,MAAME,mBAAmB,CAAC;IACxBpL,WAAWA,CAAA,EAAG;MACZ;MACA,IAAI,CAACqL,KAAK,GAAG,EAAE;MACf;MACA,IAAI,CAACC,YAAY,GAAG,EAAE;MACtB,IAAI,CAACC,KAAK,GAAG,CAAC;MAEd,IAAI,CAACT,SAAS,GAAG,CAAC;MAClB,IAAI,CAACU,UAAU,GAAG,CAAC;IACrB;;IAEA;IACAC,UAAUA,CAAC9G,KAAK,EAAE;MAChB,IAAI,IAAI,CAAC2G,YAAY,CAAC3G,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC2G,YAAY,CAAC3G,KAAK,CAAC;MAE7D,MAAM+G,OAAO,GAAG,IAAIrB,UAAU,CAAC,CAAC;MAChC,IAAI,CAACgB,KAAK,CAACM,KAAK,CAAChH,KAAK,CAAC,CAACnF,OAAO,CAAC,CAAC,CAACsE,EAAE,EAAEgC,IAAI,CAAC,KAAK4F,OAAO,CAAChB,OAAO,CAAC5G,EAAE,EAAEgC,IAAI,CAAC,CAAC;MAC1E4F,OAAO,CAACf,OAAO,CAAC,CAAC;MACjB,IAAI,CAACW,YAAY,CAAC3G,KAAK,CAAC,GAAG+G,OAAO;MAClC,OAAOA,OAAO;IAChB;IAEAE,0BAA0BA,CAAA,EAAG;MAC3B,OAAO,IAAI,CAACJ,UAAU,KAAK,CAAC;IAC9B;IAEAK,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACL,UAAU,GAAG,CAAC;IACrB;;IAEA;IACAd,OAAOA,CAAC5G,EAAE,EAAEgC,IAAI,EAAE;MAChB,IAAI,CAACuF,KAAK,CAAC9I,IAAI,CAAC,CAACuB,EAAE,EAAEgC,IAAI,CAAC,CAAC;MAC3B,IAAIA,IAAI,CAACgG,IAAI,KAAK,OAAO,EAAE,IAAI,CAACP,KAAK,EAAE;IACzC;;IAEA;IACAhH,IAAIA,CAACwG,CAAC,EAAE;MACN,MAAM1E,CAAC,GAAG,IAAI,CAACoF,UAAU,CAAC,IAAI,CAACD,UAAU,CAAC;MAC1CnF,CAAC,CAACyE,SAAS,GAAG,IAAI,CAACA,SAAS;MAC5B,IAAIlK,MAAM,GAAGyF,CAAC,CAAC9B,IAAI,CAACwG,CAAC,CAAC;;MAEtB;MACA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACa,0BAA0B,CAAC,CAAC,EAAE;QACrC,IAAIhL,MAAM,IAAIA,MAAM,CAAC+D,KAAK,KAAK,IAAI,CAACmG,SAAS,EAAE,CAAC,KAAM;UAAE;UACtD,MAAMiB,EAAE,GAAG,IAAI,CAACN,UAAU,CAAC,CAAC,CAAC;UAC7BM,EAAE,CAACjB,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,CAAC;UACjClK,MAAM,GAAGmL,EAAE,CAACxH,IAAI,CAACwG,CAAC,CAAC;QACrB;MACF;MAEA,IAAInK,MAAM,EAAE;QACV,IAAI,CAAC4K,UAAU,IAAI5K,MAAM,CAAC6J,QAAQ,GAAG,CAAC;QACtC,IAAI,IAAI,CAACe,UAAU,KAAK,IAAI,CAACD,KAAK,EAAE;UAClC;UACA,IAAI,CAACM,WAAW,CAAC,CAAC;QACpB;MACF;MAEA,OAAOjL,MAAM;IACf;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASoL,cAAcA,CAAC/L,IAAI,EAAE;IAC5B,MAAMgM,EAAE,GAAG,IAAIb,mBAAmB,CAAC,CAAC;IAEpCnL,IAAI,CAACyG,QAAQ,CAAClH,OAAO,CAAC0M,IAAI,IAAID,EAAE,CAACvB,OAAO,CAACwB,IAAI,CAACjG,KAAK,EAAE;MAAEkG,IAAI,EAAED,IAAI;MAAEJ,IAAI,EAAE;IAAQ,CAAC,CAAC,CAAC;IAEpF,IAAI7L,IAAI,CAACmM,aAAa,EAAE;MACtBH,EAAE,CAACvB,OAAO,CAACzK,IAAI,CAACmM,aAAa,EAAE;QAAEN,IAAI,EAAE;MAAM,CAAC,CAAC;IACjD;IACA,IAAI7L,IAAI,CAACwG,OAAO,EAAE;MAChBwF,EAAE,CAACvB,OAAO,CAACzK,IAAI,CAACwG,OAAO,EAAE;QAAEqF,IAAI,EAAE;MAAU,CAAC,CAAC;IAC/C;IAEA,OAAOG,EAAE;EACX;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASI,WAAWA,CAACpM,IAAI,EAAEoI,MAAM,EAAE;IACjC,MAAMiE,KAAK,GAAG,yBAA2BrM,IAAK;IAC9C,IAAIA,IAAI,CAACsM,UAAU,EAAE,OAAOD,KAAK;IAEjC;IACE;IACA;IACAzD,YAAY,CACb,CAACrJ,OAAO,CAACgN,GAAG,IAAIA,GAAG,CAACvM,IAAI,EAAEoI,MAAM,CAAC,CAAC;IAEnC2B,QAAQ,CAACyC,kBAAkB,CAACjN,OAAO,CAACgN,GAAG,IAAIA,GAAG,CAACvM,IAAI,EAAEoI,MAAM,CAAC,CAAC;;IAE7D;IACApI,IAAI,CAACsI,aAAa,GAAG,IAAI;IAEzB,CACEH,aAAa;IACb;IACA;IACAK,cAAc;IACd;IACAK,gBAAgB,CACjB,CAACtJ,OAAO,CAACgN,GAAG,IAAIA,GAAG,CAACvM,IAAI,EAAEoI,MAAM,CAAC,CAAC;IAEnCpI,IAAI,CAACsM,UAAU,GAAG,IAAI;IAEtB,IAAIG,cAAc,GAAG,IAAI;IACzB,IAAI,OAAOzM,IAAI,CAACuI,QAAQ,KAAK,QAAQ,EAAE;MACrCkE,cAAc,GAAGzM,IAAI,CAACuI,QAAQ,CAACmE,QAAQ;MACvC,OAAO1M,IAAI,CAACuI,QAAQ,CAACmE,QAAQ;IAC/B;IAEA,IAAI1M,IAAI,CAACuI,QAAQ,EAAE;MACjBvI,IAAI,CAACuI,QAAQ,GAAGS,eAAe,CAAChJ,IAAI,CAACuI,QAAQ,EAAEwB,QAAQ,CAACI,gBAAgB,CAAC;IAC3E;;IAEA;IACA,IAAInK,IAAI,CAAC2M,OAAO,IAAIF,cAAc,EAAE;MAClC,MAAM,IAAIxN,KAAK,CAAC,gGAAgG,CAAC;IACnH;;IAEA;IACA;IACAwN,cAAc,GAAGA,cAAc,IAAIzM,IAAI,CAAC2M,OAAO,IAAI,KAAK;IACxDN,KAAK,CAACO,gBAAgB,GAAG3C,MAAM,CAACwC,cAAc,EAAE,IAAI,CAAC;IAErD,IAAIrE,MAAM,EAAE;MACV,IAAI,CAACpI,IAAI,CAACgG,KAAK,EAAEhG,IAAI,CAACgG,KAAK,GAAG,OAAO;MACrCqG,KAAK,CAACQ,OAAO,GAAG5C,MAAM,CAACjK,IAAI,CAACgG,KAAK,CAAC;MAClC,IAAIhG,IAAI,CAAC8M,cAAc,EAAE9M,IAAI,CAACiG,GAAG,GAAGjG,IAAI,CAACgG,KAAK;MAC9C,IAAI,CAAChG,IAAI,CAACiG,GAAG,IAAI,CAACjG,IAAI,CAAC+M,cAAc,EAAE/M,IAAI,CAACiG,GAAG,GAAG,OAAO;MACzD,IAAIjG,IAAI,CAACiG,GAAG,EAAEoG,KAAK,CAACW,KAAK,GAAG/C,MAAM,CAACjK,IAAI,CAACiG,GAAG,CAAC;MAC5CoG,KAAK,CAACF,aAAa,GAAGvI,MAAM,CAAC5D,IAAI,CAACiG,GAAG,CAAC,IAAI,EAAE;MAC5C,IAAIjG,IAAI,CAAC+M,cAAc,IAAI3E,MAAM,CAAC+D,aAAa,EAAE;QAC/CE,KAAK,CAACF,aAAa,IAAI,CAACnM,IAAI,CAACiG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAImC,MAAM,CAAC+D,aAAa;MACrE;IACF;IACA,IAAInM,IAAI,CAACwG,OAAO,EAAE6F,KAAK,CAACY,SAAS,GAAGhD,MAAM,CAAC,8BAAgCjK,IAAI,CAACwG,OAAQ,CAAC;IACzF,IAAI,CAACxG,IAAI,CAACyG,QAAQ,EAAEzG,IAAI,CAACyG,QAAQ,GAAG,EAAE;IAEtCzG,IAAI,CAACyG,QAAQ,GAAG,EAAE,CAAC3C,MAAM,CAAC,GAAG9D,IAAI,CAACyG,QAAQ,CAACxC,GAAG,CAAC,UAASiJ,CAAC,EAAE;MACzD,OAAOC,iBAAiB,CAACD,CAAC,KAAK,MAAM,GAAGlN,IAAI,GAAGkN,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IACHlN,IAAI,CAACyG,QAAQ,CAAClH,OAAO,CAAC,UAAS2N,CAAC,EAAE;MAAEd,WAAW,CAAC,iBAAmBc,CAAC,EAAGb,KAAK,CAAC;IAAE,CAAC,CAAC;IAEjF,IAAIrM,IAAI,CAACoN,MAAM,EAAE;MACfhB,WAAW,CAACpM,IAAI,CAACoN,MAAM,EAAEhF,MAAM,CAAC;IAClC;IAEAiE,KAAK,CAACZ,OAAO,GAAGM,cAAc,CAACM,KAAK,CAAC;IACrC,OAAOA,KAAK;EACd;EAEA,IAAI,CAACtC,QAAQ,CAACyC,kBAAkB,EAAEzC,QAAQ,CAACyC,kBAAkB,GAAG,EAAE;;EAElE;EACA,IAAIzC,QAAQ,CAACtD,QAAQ,IAAIsD,QAAQ,CAACtD,QAAQ,CAACoD,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC3D,MAAM,IAAI5K,KAAK,CAAC,2FAA2F,CAAC;EAC9G;;EAEA;EACA8K,QAAQ,CAACsD,gBAAgB,GAAG7M,OAAO,CAACuJ,QAAQ,CAACsD,gBAAgB,IAAI,CAAC,CAAC,CAAC;EAEpE,OAAOjB,WAAW,CAAC,iBAAmBrC,QAAS,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuD,kBAAkBA,CAACtN,IAAI,EAAE;EAChC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;EAEvB,OAAOA,IAAI,CAAC+M,cAAc,IAAIO,kBAAkB,CAACtN,IAAI,CAACoN,MAAM,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,iBAAiBA,CAACnN,IAAI,EAAE;EAC/B,IAAIA,IAAI,CAACuN,QAAQ,IAAI,CAACvN,IAAI,CAACwN,cAAc,EAAE;IACzCxN,IAAI,CAACwN,cAAc,GAAGxN,IAAI,CAACuN,QAAQ,CAACtJ,GAAG,CAAC,UAASwJ,OAAO,EAAE;MACxD,OAAOjN,OAAO,CAACR,IAAI,EAAE;QAAEuN,QAAQ,EAAE;MAAK,CAAC,EAAEE,OAAO,CAAC;IACnD,CAAC,CAAC;EACJ;;EAEA;EACA;EACA;EACA,IAAIzN,IAAI,CAACwN,cAAc,EAAE;IACvB,OAAOxN,IAAI,CAACwN,cAAc;EAC5B;;EAEA;EACA;EACA;EACA;EACA,IAAIF,kBAAkB,CAACtN,IAAI,CAAC,EAAE;IAC5B,OAAOQ,OAAO,CAACR,IAAI,EAAE;MAAEoN,MAAM,EAAEpN,IAAI,CAACoN,MAAM,GAAG5M,OAAO,CAACR,IAAI,CAACoN,MAAM,CAAC,GAAG;IAAK,CAAC,CAAC;EAC7E;EAEA,IAAIhO,MAAM,CAACM,QAAQ,CAACM,IAAI,CAAC,EAAE;IACzB,OAAOQ,OAAO,CAACR,IAAI,CAAC;EACtB;;EAEA;EACA,OAAOA,IAAI;AACb;AAEA,IAAI0N,OAAO,GAAG,QAAQ;;AAEtB;;AAEA,SAASC,wBAAwBA,CAACrN,KAAK,EAAE;EACvC,OAAOsN,OAAO,CAACtN,KAAK,IAAIA,KAAK,KAAK,EAAE,CAAC;AACvC;AAEA,SAASuN,cAAcA,CAACC,IAAI,EAAE;EAC5B,MAAMC,SAAS,GAAG;IAChBC,KAAK,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,YAAY,CAAC;IACzC/N,IAAI,EAAE,SAAAA,CAAA,EAAW;MACf,OAAO;QACLgO,gBAAgB,EAAE,EAAE;QACpBC,eAAe,EAAE;MACnB,CAAC;IACH,CAAC;IACDC,QAAQ,EAAE;MACRxM,SAASA,CAAA,EAAG;QACV,IAAI,IAAI,CAACuM,eAAe,EAAE,OAAO,EAAE;QAEnC,OAAO,OAAO,GAAG,IAAI,CAACD,gBAAgB;MACxC,CAAC;MACDG,WAAWA,CAAA,EAAG;QACZ;QACA,IAAI,CAAC,IAAI,CAACC,UAAU,IAAI,CAACP,IAAI,CAACQ,WAAW,CAAC,IAAI,CAACvE,QAAQ,CAAC,EAAE;UACxDwE,OAAO,CAACC,IAAI,CAAC,iBAAiB,IAAI,CAACzE,QAAQ,qCAAqC,CAAC;UACjF,IAAI,CAACmE,eAAe,GAAG,IAAI;UAC3B,OAAO7N,UAAU,CAAC,IAAI,CAACoO,IAAI,CAAC;QAC9B;QAEA,IAAI9N,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,IAAI,CAAC0N,UAAU,EAAE;UACnB1N,MAAM,GAAGmN,IAAI,CAACY,aAAa,CAAC,IAAI,CAACD,IAAI,CAAC;UACtC,IAAI,CAACR,gBAAgB,GAAGtN,MAAM,CAACoJ,QAAQ;QACzC,CAAC,MAAM;UACLpJ,MAAM,GAAGmN,IAAI,CAACa,SAAS,CAAC,IAAI,CAAC5E,QAAQ,EAAE,IAAI,CAAC0E,IAAI,EAAE,IAAI,CAACG,cAAc,CAAC;UACtE,IAAI,CAACX,gBAAgB,GAAG,IAAI,CAAClE,QAAQ;QACvC;QACA,OAAOpJ,MAAM,CAACL,KAAK;MACrB,CAAC;MACD+N,UAAUA,CAAA,EAAG;QACX,OAAO,CAAC,IAAI,CAACtE,QAAQ,IAAI4D,wBAAwB,CAAC,IAAI,CAACkB,UAAU,CAAC;MACpE,CAAC;MACDD,cAAcA,CAAA,EAAG;QACf,OAAO,IAAI;MACb;IACF,CAAC;IACD;IACA;IACAE,MAAMA,CAACC,aAAa,EAAE;MACpB,OAAOA,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAC9BA,aAAa,CAAC,MAAM,EAAE;QACpBC,KAAK,EAAE,IAAI,CAACrN,SAAS;QACrBsN,QAAQ,EAAE;UAAEC,SAAS,EAAE,IAAI,CAACd;QAAY;MAC1C,CAAC,CAAC,CACH,CAAC;IACJ;IACA;EACF,CAAC;EAED,MAAMe,SAAS,GAAG;IAChBC,OAAOA,CAACC,GAAG,EAAE;MACXA,GAAG,CAACC,SAAS,CAAC,aAAa,EAAEvB,SAAS,CAAC;IACzC;EACF,CAAC;EAED,OAAO;IAAEA,SAAS;IAAEoB;EAAU,CAAC;AACjC;;AAEA;;AAEA;AACA,MAAMI,eAAe,GAAG;EACtB,wBAAwB,EAAEC,CAAC;IAAEvM,EAAE;IAAEtC,MAAM;IAAEc;EAAK,CAAC,KAAK;IAClD,MAAMgO,cAAc,GAAGC,UAAU,CAACzM,EAAE,CAAC;IACrC,IAAI,CAACwM,cAAc,CAACrN,MAAM,EAAE;IAE5B,MAAMuN,UAAU,GAAGC,QAAQ,CAACb,aAAa,CAAC,KAAK,CAAC;IAChDY,UAAU,CAACT,SAAS,GAAGvO,MAAM,CAACL,KAAK;IACnCK,MAAM,CAACL,KAAK,GAAGuP,YAAY,CAACJ,cAAc,EAAEC,UAAU,CAACC,UAAU,CAAC,EAAElO,IAAI,CAAC;EAC3E;AACF,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAASqO,GAAGA,CAAC9O,IAAI,EAAE;EACjB,OAAOA,IAAI,CAAC+O,QAAQ,CAACxG,WAAW,CAAC,CAAC;AACpC;;AAEA;AACA;AACA;AACA,SAASmG,UAAUA,CAAC1O,IAAI,EAAE;EACxB;EACA,MAAML,MAAM,GAAG,EAAE;EACjB,CAAC,SAASqP,WAAWA,CAAChP,IAAI,EAAEgE,MAAM,EAAE;IAClC,KAAK,IAAIlC,KAAK,GAAG9B,IAAI,CAACiP,UAAU,EAAEnN,KAAK,EAAEA,KAAK,GAAGA,KAAK,CAACoN,WAAW,EAAE;MAClE,IAAIpN,KAAK,CAACqN,QAAQ,KAAK,CAAC,EAAE;QACxBnL,MAAM,IAAIlC,KAAK,CAACsN,SAAS,CAAChO,MAAM;MAClC,CAAC,MAAM,IAAIU,KAAK,CAACqN,QAAQ,KAAK,CAAC,EAAE;QAC/BxP,MAAM,CAAC2B,IAAI,CAAC;UACV+N,KAAK,EAAE,OAAO;UACdrL,MAAM,EAAEA,MAAM;UACdhE,IAAI,EAAE8B;QACR,CAAC,CAAC;QACFkC,MAAM,GAAGgL,WAAW,CAAClN,KAAK,EAAEkC,MAAM,CAAC;QACnC;QACA;QACA;QACA,IAAI,CAAC8K,GAAG,CAAChN,KAAK,CAAC,CAAC2B,KAAK,CAAC,iBAAiB,CAAC,EAAE;UACxC9D,MAAM,CAAC2B,IAAI,CAAC;YACV+N,KAAK,EAAE,MAAM;YACbrL,MAAM,EAAEA,MAAM;YACdhE,IAAI,EAAE8B;UACR,CAAC,CAAC;QACJ;MACF;IACF;IACA,OAAOkC,MAAM;EACf,CAAC,EAAEhE,IAAI,EAAE,CAAC,CAAC;EACX,OAAOL,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASkP,YAAYA,CAACpP,QAAQ,EAAE2N,WAAW,EAAE9N,KAAK,EAAE;EAClD,IAAIgQ,SAAS,GAAG,CAAC;EACjB,IAAI3P,MAAM,GAAG,EAAE;EACf,MAAM4P,SAAS,GAAG,EAAE;EAEpB,SAASC,YAAYA,CAAA,EAAG;IACtB,IAAI,CAAC/P,QAAQ,CAAC2B,MAAM,IAAI,CAACgM,WAAW,CAAChM,MAAM,EAAE;MAC3C,OAAO3B,QAAQ,CAAC2B,MAAM,GAAG3B,QAAQ,GAAG2N,WAAW;IACjD;IACA,IAAI3N,QAAQ,CAAC,CAAC,CAAC,CAACuE,MAAM,KAAKoJ,WAAW,CAAC,CAAC,CAAC,CAACpJ,MAAM,EAAE;MAChD,OAAQvE,QAAQ,CAAC,CAAC,CAAC,CAACuE,MAAM,GAAGoJ,WAAW,CAAC,CAAC,CAAC,CAACpJ,MAAM,GAAIvE,QAAQ,GAAG2N,WAAW;IAC9E;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAGI,OAAOA,WAAW,CAAC,CAAC,CAAC,CAACiC,KAAK,KAAK,OAAO,GAAG5P,QAAQ,GAAG2N,WAAW;EAClE;;EAEA;AACF;AACA;EACE,SAASqC,IAAIA,CAACzP,IAAI,EAAE;IAClB;IACA,SAAS0P,eAAeA,CAACC,IAAI,EAAE;MAC7B,OAAO,GAAG,GAAGA,IAAI,CAACZ,QAAQ,GAAG,IAAI,GAAG1P,UAAU,CAACsQ,IAAI,CAACrQ,KAAK,CAAC,GAAG,GAAG;IAClE;IACA;IACAK,MAAM,IAAI,GAAG,GAAGmP,GAAG,CAAC9O,IAAI,CAAC,GAAG,EAAE,CAACiD,GAAG,CAAC2M,IAAI,CAAC5P,IAAI,CAAC6P,UAAU,EAAEH,eAAe,CAAC,CAACxN,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;EAC1F;;EAEA;AACF;AACA;EACE,SAAS4N,KAAKA,CAAC9P,IAAI,EAAE;IACnBL,MAAM,IAAI,IAAI,GAAGmP,GAAG,CAAC9O,IAAI,CAAC,GAAG,GAAG;EAClC;;EAEA;AACF;AACA;EACE,SAAS8N,MAAMA,CAACuB,KAAK,EAAE;IACrB,CAACA,KAAK,CAACA,KAAK,KAAK,OAAO,GAAGI,IAAI,GAAGK,KAAK,EAAET,KAAK,CAACrP,IAAI,CAAC;EACtD;EAEA,OAAOP,QAAQ,CAAC2B,MAAM,IAAIgM,WAAW,CAAChM,MAAM,EAAE;IAC5C,IAAI2O,MAAM,GAAGP,YAAY,CAAC,CAAC;IAC3B7P,MAAM,IAAIN,UAAU,CAACC,KAAK,CAAC4E,SAAS,CAACoL,SAAS,EAAES,MAAM,CAAC,CAAC,CAAC,CAAC/L,MAAM,CAAC,CAAC;IAClEsL,SAAS,GAAGS,MAAM,CAAC,CAAC,CAAC,CAAC/L,MAAM;IAC5B,IAAI+L,MAAM,KAAKtQ,QAAQ,EAAE;MACvB;AACN;AACA;AACA;AACA;AACA;MACM8P,SAAS,CAACS,OAAO,CAAC,CAAC,CAACzR,OAAO,CAACuR,KAAK,CAAC;MAClC,GAAG;QACDhC,MAAM,CAACiC,MAAM,CAAC7F,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B6F,MAAM,GAAGP,YAAY,CAAC,CAAC;MACzB,CAAC,QAAQO,MAAM,KAAKtQ,QAAQ,IAAIsQ,MAAM,CAAC3O,MAAM,IAAI2O,MAAM,CAAC,CAAC,CAAC,CAAC/L,MAAM,KAAKsL,SAAS;MAC/EC,SAAS,CAACS,OAAO,CAAC,CAAC,CAACzR,OAAO,CAACkR,IAAI,CAAC;IACnC,CAAC,MAAM;MACL,IAAIM,MAAM,CAAC,CAAC,CAAC,CAACV,KAAK,KAAK,OAAO,EAAE;QAC/BE,SAAS,CAACjO,IAAI,CAACyO,MAAM,CAAC,CAAC,CAAC,CAAC/P,IAAI,CAAC;MAChC,CAAC,MAAM;QACLuP,SAAS,CAAChO,GAAG,CAAC,CAAC;MACjB;MACAuM,MAAM,CAACiC,MAAM,CAAC7F,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC;EACF;EACA,OAAOvK,MAAM,GAAGN,UAAU,CAACC,KAAK,CAAC2Q,MAAM,CAACX,SAAS,CAAC,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAMY,gBAAgB,GAAG,CAAC,CAAC;;AAE3B;AACA;AACA;AACA,MAAMC,KAAK,GAAIC,OAAO,IAAK;EACzB7C,OAAO,CAAC4C,KAAK,CAACC,OAAO,CAAC;AACxB,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAM5C,IAAI,GAAGA,CAAC4C,OAAO,EAAE,GAAGrN,IAAI,KAAK;EACjCwK,OAAO,CAAC8C,GAAG,CAAC,SAASD,OAAO,EAAE,EAAE,GAAGrN,IAAI,CAAC;AAC1C,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMuN,UAAU,GAAGA,CAAC5D,OAAO,EAAE0D,OAAO,KAAK;EACvC,IAAIF,gBAAgB,CAAC,GAAGxD,OAAO,IAAI0D,OAAO,EAAE,CAAC,EAAE;EAE/C7C,OAAO,CAAC8C,GAAG,CAAC,oBAAoB3D,OAAO,KAAK0D,OAAO,EAAE,CAAC;EACtDF,gBAAgB,CAAC,GAAGxD,OAAO,IAAI0D,OAAO,EAAE,CAAC,GAAG,IAAI;AAClD,CAAC;;AAED;AACA;AACA;AACA;;AAEA,MAAMG,QAAQ,GAAGlR,UAAU;AAC3B,MAAMmR,SAAS,GAAGhR,OAAO;AACzB,MAAMiR,QAAQ,GAAGC,MAAM,CAAC,SAAS,CAAC;;AAElC;AACA;AACA;AACA;AACA,MAAMC,IAAI,GAAG,SAAAA,CAAS7D,IAAI,EAAE;EAC1B;EACA;EACA,MAAM8D,SAAS,GAAGxS,MAAM,CAACwB,MAAM,CAAC,IAAI,CAAC;EACrC;EACA,MAAMiR,OAAO,GAAGzS,MAAM,CAACwB,MAAM,CAAC,IAAI,CAAC;EACnC;EACA,MAAMoJ,OAAO,GAAG,EAAE;;EAElB;EACA;EACA,IAAI8H,SAAS,GAAG,IAAI;EACpB,MAAMC,WAAW,GAAG,wBAAwB;EAC5C,MAAMC,kBAAkB,GAAG,qFAAqF;EAChH;EACA,MAAMC,kBAAkB,GAAG;IAAEC,iBAAiB,EAAE,IAAI;IAAE1S,IAAI,EAAE,YAAY;IAAEiH,QAAQ,EAAE;EAAG,CAAC;;EAExF;EACA;EACA;EACA,IAAIrF,OAAO,GAAG;IACZ+Q,aAAa,EAAE,oBAAoB;IACnCC,gBAAgB,EAAE,6BAA6B;IAC/C9Q,WAAW,EAAE,OAAO;IACpB+Q,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,KAAK;IACZV,SAAS,EAAE,IAAI;IACf;IACA;IACAW,SAAS,EAAEpP;EACb,CAAC;;EAED;;EAEA;AACF;AACA;AACA;EACE,SAASqP,kBAAkBA,CAACC,YAAY,EAAE;IACxC,OAAOrR,OAAO,CAAC+Q,aAAa,CAACO,IAAI,CAACD,YAAY,CAAC;EACjD;;EAEA;AACF;AACA;EACE,SAASE,aAAaA,CAACC,KAAK,EAAE;IAC5B,IAAIC,OAAO,GAAGD,KAAK,CAACjR,SAAS,GAAG,GAAG;IAEnCkR,OAAO,IAAID,KAAK,CAACE,UAAU,GAAGF,KAAK,CAACE,UAAU,CAACnR,SAAS,GAAG,EAAE;;IAE7D;IACA,MAAM8C,KAAK,GAAGrD,OAAO,CAACgR,gBAAgB,CAAC9N,IAAI,CAACuO,OAAO,CAAC;IACpD,IAAIpO,KAAK,EAAE;MACT,MAAMsF,QAAQ,GAAGuE,WAAW,CAAC7J,KAAK,CAAC,CAAC,CAAC,CAAC;MACtC,IAAI,CAACsF,QAAQ,EAAE;QACbyE,IAAI,CAACwD,kBAAkB,CAACzR,OAAO,CAAC,IAAI,EAAEkE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD+J,IAAI,CAAC,mDAAmD,EAAEoE,KAAK,CAAC;MAClE;MACA,OAAO7I,QAAQ,GAAGtF,KAAK,CAAC,CAAC,CAAC,GAAG,cAAc;IAC7C;IAEA,OAAOoO,OAAO,CACXxK,KAAK,CAAC,KAAK,CAAC,CACZ0K,IAAI,CAAEC,MAAM,IAAKR,kBAAkB,CAACQ,MAAM,CAAC,IAAI1E,WAAW,CAAC0E,MAAM,CAAC,CAAC;EACxE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASrE,SAASA,CAACsE,kBAAkB,EAAEC,aAAa,EAAEtE,cAAc,EAAEuE,YAAY,EAAE;IAClF,IAAI1E,IAAI,GAAG,EAAE;IACb,IAAIgE,YAAY,GAAG,EAAE;IACrB,IAAI,OAAOS,aAAa,KAAK,QAAQ,EAAE;MACrCzE,IAAI,GAAGwE,kBAAkB;MACzBrE,cAAc,GAAGsE,aAAa,CAACtE,cAAc;MAC7C6D,YAAY,GAAGS,aAAa,CAACnJ,QAAQ;MACrC;MACA;MACAoJ,YAAY,GAAGjT,SAAS;IAC1B,CAAC,MAAM;MACL;MACAoR,UAAU,CAAC,QAAQ,EAAE,qDAAqD,CAAC;MAC3EA,UAAU,CAAC,QAAQ,EAAE,uGAAuG,CAAC;MAC7HmB,YAAY,GAAGQ,kBAAkB;MACjCxE,IAAI,GAAGyE,aAAa;IACtB;;IAEA;IACA,MAAME,OAAO,GAAG;MACd3E,IAAI;MACJ1E,QAAQ,EAAE0I;IACZ,CAAC;IACD;IACA;IACAY,IAAI,CAAC,kBAAkB,EAAED,OAAO,CAAC;;IAEjC;IACA;IACA,MAAMzS,MAAM,GAAGyS,OAAO,CAACzS,MAAM,GACzByS,OAAO,CAACzS,MAAM,GACd2S,UAAU,CAACF,OAAO,CAACrJ,QAAQ,EAAEqJ,OAAO,CAAC3E,IAAI,EAAEG,cAAc,EAAEuE,YAAY,CAAC;IAE5ExS,MAAM,CAAC8N,IAAI,GAAG2E,OAAO,CAAC3E,IAAI;IAC1B;IACA4E,IAAI,CAAC,iBAAiB,EAAE1S,MAAM,CAAC;IAE/B,OAAOA,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS2S,UAAUA,CAACb,YAAY,EAAEc,eAAe,EAAE3E,cAAc,EAAEuE,YAAY,EAAE;IAC/E;AACJ;AACA;AACA;AACA;AACA;IACI,SAASK,WAAWA,CAACxT,IAAI,EAAEyE,KAAK,EAAE;MAChC,MAAMgP,SAAS,GAAG1J,QAAQ,CAACI,gBAAgB,GAAG1F,KAAK,CAAC,CAAC,CAAC,CAAC8E,WAAW,CAAC,CAAC,GAAG9E,KAAK,CAAC,CAAC,CAAC;MAC/E,OAAOrF,MAAM,CAACsU,SAAS,CAACC,cAAc,CAAC/C,IAAI,CAAC5Q,IAAI,CAACuI,QAAQ,EAAEkL,SAAS,CAAC,IAAIzT,IAAI,CAACuI,QAAQ,CAACkL,SAAS,CAAC;IACnG;IAEA,SAASG,eAAeA,CAAA,EAAG;MACzB,IAAI,CAACzR,GAAG,CAACoG,QAAQ,EAAE;QACjBjF,OAAO,CAAC9B,OAAO,CAACqS,UAAU,CAAC;QAC3B;MACF;MAEA,IAAIhJ,SAAS,GAAG,CAAC;MACjB1I,GAAG,CAACyK,gBAAgB,CAAC/B,SAAS,GAAG,CAAC;MAClC,IAAIpG,KAAK,GAAGtC,GAAG,CAACyK,gBAAgB,CAACtI,IAAI,CAACuP,UAAU,CAAC;MACjD,IAAIC,GAAG,GAAG,EAAE;MAEZ,OAAOrP,KAAK,EAAE;QACZqP,GAAG,IAAID,UAAU,CAAC3O,SAAS,CAAC2F,SAAS,EAAEpG,KAAK,CAACC,KAAK,CAAC;QACnD,MAAMzE,IAAI,GAAGuT,WAAW,CAACrR,GAAG,EAAEsC,KAAK,CAAC;QACpC,IAAIxE,IAAI,EAAE;UACR,MAAM,CAACgB,IAAI,EAAE8S,gBAAgB,CAAC,GAAG9T,IAAI;UACrCqD,OAAO,CAAC9B,OAAO,CAACsS,GAAG,CAAC;UACpBA,GAAG,GAAG,EAAE;UAER5N,SAAS,IAAI6N,gBAAgB;UAC7B,IAAI9S,IAAI,CAACsD,UAAU,CAAC,GAAG,CAAC,EAAE;YACxB;YACA;YACAuP,GAAG,IAAIrP,KAAK,CAAC,CAAC,CAAC;UACjB,CAAC,MAAM;YACL,MAAMuP,QAAQ,GAAGjK,QAAQ,CAACsD,gBAAgB,CAACpM,IAAI,CAAC,IAAIA,IAAI;YACxDqC,OAAO,CAACF,UAAU,CAACqB,KAAK,CAAC,CAAC,CAAC,EAAEuP,QAAQ,CAAC;UACxC;QACF,CAAC,MAAM;UACLF,GAAG,IAAIrP,KAAK,CAAC,CAAC,CAAC;QACjB;QACAoG,SAAS,GAAG1I,GAAG,CAACyK,gBAAgB,CAAC/B,SAAS;QAC1CpG,KAAK,GAAGtC,GAAG,CAACyK,gBAAgB,CAACtI,IAAI,CAACuP,UAAU,CAAC;MAC/C;MACAC,GAAG,IAAID,UAAU,CAAC5C,MAAM,CAACpG,SAAS,CAAC;MACnCvH,OAAO,CAAC9B,OAAO,CAACsS,GAAG,CAAC;IACtB;IAEA,SAASG,kBAAkBA,CAAA,EAAG;MAC5B,IAAIJ,UAAU,KAAK,EAAE,EAAE;MACvB;MACA,IAAIlT,MAAM,GAAG,IAAI;MAEjB,IAAI,OAAOwB,GAAG,CAAC+R,WAAW,KAAK,QAAQ,EAAE;QACvC,IAAI,CAACtC,SAAS,CAACzP,GAAG,CAAC+R,WAAW,CAAC,EAAE;UAC/B5Q,OAAO,CAAC9B,OAAO,CAACqS,UAAU,CAAC;UAC3B;QACF;QACAlT,MAAM,GAAG2S,UAAU,CAACnR,GAAG,CAAC+R,WAAW,EAAEL,UAAU,EAAE,IAAI,EAAEM,aAAa,CAAChS,GAAG,CAAC+R,WAAW,CAAC,CAAC;QACtFC,aAAa,CAAChS,GAAG,CAAC+R,WAAW,CAAC,GAAG,2BAA6BvT,MAAM,CAACwB,GAAI;MAC3E,CAAC,MAAM;QACLxB,MAAM,GAAG+N,aAAa,CAACmF,UAAU,EAAE1R,GAAG,CAAC+R,WAAW,CAAC9R,MAAM,GAAGD,GAAG,CAAC+R,WAAW,GAAG,IAAI,CAAC;MACrF;;MAEA;MACA;MACA;MACA;MACA,IAAI/R,GAAG,CAAC+D,SAAS,GAAG,CAAC,EAAE;QACrBA,SAAS,IAAIvF,MAAM,CAACuF,SAAS;MAC/B;MACA5C,OAAO,CAACD,cAAc,CAAC1C,MAAM,CAAC2C,OAAO,EAAE3C,MAAM,CAACoJ,QAAQ,CAAC;IACzD;IAEA,SAASqK,aAAaA,CAAA,EAAG;MACvB,IAAIjS,GAAG,CAAC+R,WAAW,IAAI,IAAI,EAAE;QAC3BD,kBAAkB,CAAC,CAAC;MACtB,CAAC,MAAM;QACLL,eAAe,CAAC,CAAC;MACnB;MACAC,UAAU,GAAG,EAAE;IACjB;;IAEA;AACJ;AACA;IACI,SAASQ,YAAYA,CAACrU,IAAI,EAAE;MAC1B,IAAIA,IAAI,CAAC2B,SAAS,EAAE;QAClB2B,OAAO,CAAC5B,QAAQ,CAACqI,QAAQ,CAACsD,gBAAgB,CAACrN,IAAI,CAAC2B,SAAS,CAAC,IAAI3B,IAAI,CAAC2B,SAAS,CAAC;MAC/E;MACAQ,GAAG,GAAG/C,MAAM,CAACwB,MAAM,CAACZ,IAAI,EAAE;QAAEoI,MAAM,EAAE;UAAE9H,KAAK,EAAE6B;QAAI;MAAE,CAAC,CAAC;MACrD,OAAOA,GAAG;IACZ;;IAEA;AACJ;AACA;AACA;AACA;AACA;IACI,SAASmS,SAASA,CAACtU,IAAI,EAAEyE,KAAK,EAAE8P,kBAAkB,EAAE;MAClD,IAAIC,OAAO,GAAGjQ,UAAU,CAACvE,IAAI,CAACgN,KAAK,EAAEuH,kBAAkB,CAAC;MAExD,IAAIC,OAAO,EAAE;QACX,IAAIxU,IAAI,CAAC,QAAQ,CAAC,EAAE;UAClB,MAAMqG,IAAI,GAAG,IAAIvG,QAAQ,CAACE,IAAI,CAAC;UAC/BA,IAAI,CAAC,QAAQ,CAAC,CAACyE,KAAK,EAAE4B,IAAI,CAAC;UAC3B,IAAIA,IAAI,CAAClG,cAAc,EAAEqU,OAAO,GAAG,KAAK;QAC1C;QAEA,IAAIA,OAAO,EAAE;UACX,OAAOxU,IAAI,CAACyU,UAAU,IAAIzU,IAAI,CAACoI,MAAM,EAAE;YACrCpI,IAAI,GAAGA,IAAI,CAACoI,MAAM;UACpB;UACA,OAAOpI,IAAI;QACb;MACF;MACA;MACA;MACA,IAAIA,IAAI,CAAC+M,cAAc,EAAE;QACvB,OAAOuH,SAAS,CAACtU,IAAI,CAACoI,MAAM,EAAE3D,KAAK,EAAE8P,kBAAkB,CAAC;MAC1D;IACF;;IAEA;AACJ;AACA;AACA;AACA;IACI,SAASG,QAAQA,CAAClQ,MAAM,EAAE;MACxB,IAAIrC,GAAG,CAACsJ,OAAO,CAACF,UAAU,KAAK,CAAC,EAAE;QAChC;QACA;QACAsI,UAAU,IAAIrP,MAAM,CAAC,CAAC,CAAC;QACvB,OAAO,CAAC;MACV,CAAC,MAAM;QACL;QACA;QACAmQ,wBAAwB,GAAG,IAAI;QAC/B,OAAO,CAAC;MACV;IACF;;IAEA;AACJ;AACA;AACA;AACA;AACA;IACI,SAASC,YAAYA,CAACnQ,KAAK,EAAE;MAC3B,MAAMD,MAAM,GAAGC,KAAK,CAAC,CAAC,CAAC;MACvB,MAAMoQ,OAAO,GAAGpQ,KAAK,CAACyH,IAAI;MAE1B,MAAM7F,IAAI,GAAG,IAAIvG,QAAQ,CAAC+U,OAAO,CAAC;MAClC;MACA,MAAMC,eAAe,GAAG,CAACD,OAAO,CAACvM,aAAa,EAAEuM,OAAO,CAAC,UAAU,CAAC,CAAC;MACpE,KAAK,MAAME,EAAE,IAAID,eAAe,EAAE;QAChC,IAAI,CAACC,EAAE,EAAE;QACTA,EAAE,CAACtQ,KAAK,EAAE4B,IAAI,CAAC;QACf,IAAIA,IAAI,CAAClG,cAAc,EAAE,OAAOuU,QAAQ,CAAClQ,MAAM,CAAC;MAClD;MAEA,IAAIqQ,OAAO,IAAIA,OAAO,CAAC/H,cAAc,EAAE;QACrC+H,OAAO,CAAC7H,KAAK,GAAGtJ,MAAM,CAACc,MAAM,CAAC;MAChC;MAEA,IAAIqQ,OAAO,CAACG,IAAI,EAAE;QAChBnB,UAAU,IAAIrP,MAAM;MACtB,CAAC,MAAM;QACL,IAAIqQ,OAAO,CAACI,YAAY,EAAE;UACxBpB,UAAU,IAAIrP,MAAM;QACtB;QACA4P,aAAa,CAAC,CAAC;QACf,IAAI,CAACS,OAAO,CAACK,WAAW,IAAI,CAACL,OAAO,CAACI,YAAY,EAAE;UACjDpB,UAAU,GAAGrP,MAAM;QACrB;MACF;MACA6P,YAAY,CAACQ,OAAO,CAAC;MACrB;MACA;MACA;MACA;MACA,OAAOA,OAAO,CAACK,WAAW,GAAG,CAAC,GAAG1Q,MAAM,CAACpC,MAAM;IAChD;;IAEA;AACJ;AACA;AACA;AACA;IACI,SAAS+S,UAAUA,CAAC1Q,KAAK,EAAE;MACzB,MAAMD,MAAM,GAAGC,KAAK,CAAC,CAAC,CAAC;MACvB,MAAM8P,kBAAkB,GAAGhB,eAAe,CAACtC,MAAM,CAACxM,KAAK,CAACC,KAAK,CAAC;MAE9D,MAAM0Q,OAAO,GAAGd,SAAS,CAACnS,GAAG,EAAEsC,KAAK,EAAE8P,kBAAkB,CAAC;MACzD,IAAI,CAACa,OAAO,EAAE;QAAE,OAAO3D,QAAQ;MAAE;MAEjC,MAAM4D,MAAM,GAAGlT,GAAG;MAClB,IAAIkT,MAAM,CAACL,IAAI,EAAE;QACfnB,UAAU,IAAIrP,MAAM;MACtB,CAAC,MAAM;QACL,IAAI,EAAE6Q,MAAM,CAACC,SAAS,IAAID,MAAM,CAACE,UAAU,CAAC,EAAE;UAC5C1B,UAAU,IAAIrP,MAAM;QACtB;QACA4P,aAAa,CAAC,CAAC;QACf,IAAIiB,MAAM,CAACE,UAAU,EAAE;UACrB1B,UAAU,GAAGrP,MAAM;QACrB;MACF;MACA,GAAG;QACD,IAAIrC,GAAG,CAACR,SAAS,EAAE;UACjB2B,OAAO,CAACxB,SAAS,CAAC,CAAC;QACrB;QACA,IAAI,CAACK,GAAG,CAAC6S,IAAI,IAAI,CAAC7S,GAAG,CAAC+R,WAAW,EAAE;UACjChO,SAAS,IAAI/D,GAAG,CAAC+D,SAAS;QAC5B;QACA/D,GAAG,GAAGA,GAAG,CAACiG,MAAM;MAClB,CAAC,QAAQjG,GAAG,KAAKiT,OAAO,CAAChN,MAAM;MAC/B,IAAIgN,OAAO,CAAChI,MAAM,EAAE;QAClB,IAAIgI,OAAO,CAACtI,cAAc,EAAE;UAC1BsI,OAAO,CAAChI,MAAM,CAACJ,KAAK,GAAGoI,OAAO,CAACpI,KAAK;QACtC;QACAqH,YAAY,CAACe,OAAO,CAAChI,MAAM,CAAC;MAC9B;MACA,OAAOiI,MAAM,CAACC,SAAS,GAAG,CAAC,GAAG9Q,MAAM,CAACpC,MAAM;IAC7C;IAEA,SAASoT,oBAAoBA,CAAA,EAAG;MAC9B,MAAMC,IAAI,GAAG,EAAE;MACf,KAAK,IAAIC,OAAO,GAAGvT,GAAG,EAAEuT,OAAO,KAAK3L,QAAQ,EAAE2L,OAAO,GAAGA,OAAO,CAACtN,MAAM,EAAE;QACtE,IAAIsN,OAAO,CAAC/T,SAAS,EAAE;UACrB8T,IAAI,CAACE,OAAO,CAACD,OAAO,CAAC/T,SAAS,CAAC;QACjC;MACF;MACA8T,IAAI,CAAClW,OAAO,CAACqW,IAAI,IAAItS,OAAO,CAAC5B,QAAQ,CAACkU,IAAI,CAAC,CAAC;IAC9C;;IAEA;IACA,IAAIC,SAAS,GAAG,CAAC,CAAC;;IAElB;AACJ;AACA;AACA;AACA;AACA;IACI,SAASC,aAAaA,CAACC,eAAe,EAAEtR,KAAK,EAAE;MAC7C,MAAMD,MAAM,GAAGC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;;MAEhC;MACAoP,UAAU,IAAIkC,eAAe;MAE7B,IAAIvR,MAAM,IAAI,IAAI,EAAE;QAClB4P,aAAa,CAAC,CAAC;QACf,OAAO,CAAC;MACV;;MAEA;MACA;MACA;MACA;MACA,IAAIyB,SAAS,CAAChK,IAAI,KAAK,OAAO,IAAIpH,KAAK,CAACoH,IAAI,KAAK,KAAK,IAAIgK,SAAS,CAACnR,KAAK,KAAKD,KAAK,CAACC,KAAK,IAAIF,MAAM,KAAK,EAAE,EAAE;QAC1G;QACAqP,UAAU,IAAIN,eAAe,CAAC7H,KAAK,CAACjH,KAAK,CAACC,KAAK,EAAED,KAAK,CAACC,KAAK,GAAG,CAAC,CAAC;QACjE,IAAI,CAACoN,SAAS,EAAE;UACd;UACA,MAAMkE,GAAG,GAAG,IAAI/W,KAAK,CAAC,qBAAqB,CAAC;UAC5C+W,GAAG,CAACvD,YAAY,GAAGA,YAAY;UAC/BuD,GAAG,CAACC,OAAO,GAAGJ,SAAS,CAAC3J,IAAI;UAC5B,MAAM8J,GAAG;QACX;QACA,OAAO,CAAC;MACV;MACAH,SAAS,GAAGpR,KAAK;MAEjB,IAAIA,KAAK,CAACoH,IAAI,KAAK,OAAO,EAAE;QAC1B,OAAO+I,YAAY,CAACnQ,KAAK,CAAC;MAC5B,CAAC,MAAM,IAAIA,KAAK,CAACoH,IAAI,KAAK,SAAS,IAAI,CAAC+C,cAAc,EAAE;QACtD;QACA;QACA,MAAMoH,GAAG,GAAG,IAAI/W,KAAK,CAAC,kBAAkB,GAAGuF,MAAM,GAAG,cAAc,IAAIrC,GAAG,CAACR,SAAS,IAAI,WAAW,CAAC,GAAG,GAAG,CAAC;QAC1GqU,GAAG,CAAChW,IAAI,GAAGmC,GAAG;QACd,MAAM6T,GAAG;MACX,CAAC,MAAM,IAAIvR,KAAK,CAACoH,IAAI,KAAK,KAAK,EAAE;QAC/B,MAAMyE,SAAS,GAAG6E,UAAU,CAAC1Q,KAAK,CAAC;QACnC,IAAI6L,SAAS,KAAKmB,QAAQ,EAAE;UAC1B,OAAOnB,SAAS;QAClB;MACF;;MAEA;MACA;MACA;MACA,IAAI7L,KAAK,CAACoH,IAAI,KAAK,SAAS,IAAIrH,MAAM,KAAK,EAAE,EAAE;QAC7C;QACA,OAAO,CAAC;MACV;;MAEA;MACA;MACA;MACA;MACA,IAAI0R,UAAU,GAAG,MAAM,IAAIA,UAAU,GAAGzR,KAAK,CAACC,KAAK,GAAG,CAAC,EAAE;QACvD,MAAMsR,GAAG,GAAG,IAAI/W,KAAK,CAAC,2DAA2D,CAAC;QAClF,MAAM+W,GAAG;MACX;;MAEA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAGMnC,UAAU,IAAIrP,MAAM;MACpB,OAAOA,MAAM,CAACpC,MAAM;IACtB;IAEA,MAAM2H,QAAQ,GAAGuE,WAAW,CAACmE,YAAY,CAAC;IAC1C,IAAI,CAAC1I,QAAQ,EAAE;MACboH,KAAK,CAACa,kBAAkB,CAACzR,OAAO,CAAC,IAAI,EAAEkS,YAAY,CAAC,CAAC;MACrD,MAAM,IAAIxT,KAAK,CAAC,qBAAqB,GAAGwT,YAAY,GAAG,GAAG,CAAC;IAC7D;IAEA,MAAM0D,EAAE,GAAGrM,eAAe,CAACC,QAAQ,EAAE;MAAEC;IAAQ,CAAC,CAAC;IACjD,IAAIrJ,MAAM,GAAG,EAAE;IACf;IACA,IAAIwB,GAAG,GAAGgR,YAAY,IAAIgD,EAAE;IAC5B;IACA,MAAMhC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1B,MAAM7Q,OAAO,GAAG,IAAIlC,OAAO,CAACmR,SAAS,CAACnR,OAAO,CAAC;IAC9CoU,oBAAoB,CAAC,CAAC;IACtB,IAAI3B,UAAU,GAAG,EAAE;IACnB,IAAI3N,SAAS,GAAG,CAAC;IACjB,IAAIxB,KAAK,GAAG,CAAC;IACb,IAAIwR,UAAU,GAAG,CAAC;IAClB,IAAIvB,wBAAwB,GAAG,KAAK;IAEpC,IAAI;MACFxS,GAAG,CAACsJ,OAAO,CAACG,WAAW,CAAC,CAAC;MAEzB,SAAS;QACPsK,UAAU,EAAE;QACZ,IAAIvB,wBAAwB,EAAE;UAC5B;UACA;UACAA,wBAAwB,GAAG,KAAK;QAClC,CAAC,MAAM;UACLxS,GAAG,CAACsJ,OAAO,CAACG,WAAW,CAAC,CAAC;QAC3B;QACAzJ,GAAG,CAACsJ,OAAO,CAACZ,SAAS,GAAGnG,KAAK;QAE7B,MAAMD,KAAK,GAAGtC,GAAG,CAACsJ,OAAO,CAACnH,IAAI,CAACiP,eAAe,CAAC;QAC/C;;QAEA,IAAI,CAAC9O,KAAK,EAAE;QAEZ,MAAM2R,WAAW,GAAG7C,eAAe,CAACrO,SAAS,CAACR,KAAK,EAAED,KAAK,CAACC,KAAK,CAAC;QACjE,MAAM2R,cAAc,GAAGP,aAAa,CAACM,WAAW,EAAE3R,KAAK,CAAC;QACxDC,KAAK,GAAGD,KAAK,CAACC,KAAK,GAAG2R,cAAc;MACtC;MACAP,aAAa,CAACvC,eAAe,CAACtC,MAAM,CAACvM,KAAK,CAAC,CAAC;MAC5CpB,OAAO,CAACd,aAAa,CAAC,CAAC;MACvBc,OAAO,CAACG,QAAQ,CAAC,CAAC;MAClB9C,MAAM,GAAG2C,OAAO,CAACC,MAAM,CAAC,CAAC;MAEzB,OAAO;QACL;QACA;QACA2C,SAAS,EAAEoQ,IAAI,CAACC,KAAK,CAACrQ,SAAS,CAAC;QAChC5F,KAAK,EAAEK,MAAM;QACboJ,QAAQ,EAAE0I,YAAY;QACtBjM,OAAO,EAAE,KAAK;QACdlD,OAAO,EAAEA,OAAO;QAChBnB,GAAG,EAAEA;MACP,CAAC;IACH,CAAC,CAAC,OAAO6T,GAAG,EAAE;MACZ,IAAIA,GAAG,CAAC5E,OAAO,IAAI4E,GAAG,CAAC5E,OAAO,CAACvH,QAAQ,CAAC,SAAS,CAAC,EAAE;QAClD,OAAO;UACLrD,OAAO,EAAE,IAAI;UACbgQ,SAAS,EAAE;YACTC,GAAG,EAAET,GAAG,CAAC5E,OAAO;YAChBgC,OAAO,EAAEG,eAAe,CAAC7H,KAAK,CAAChH,KAAK,GAAG,GAAG,EAAEA,KAAK,GAAG,GAAG,CAAC;YACxD1E,IAAI,EAAEgW,GAAG,CAAChW;UACZ,CAAC;UACD0W,KAAK,EAAE/V,MAAM;UACbuF,SAAS,EAAE,CAAC;UACZ5F,KAAK,EAAEiR,QAAQ,CAACgC,eAAe,CAAC;UAChCjQ,OAAO,EAAEA;QACX,CAAC;MACH,CAAC,MAAM,IAAIwO,SAAS,EAAE;QACpB,OAAO;UACLtL,OAAO,EAAE,KAAK;UACdN,SAAS,EAAE,CAAC;UACZ5F,KAAK,EAAEiR,QAAQ,CAACgC,eAAe,CAAC;UAChCjQ,OAAO,EAAEA,OAAO;UAChByG,QAAQ,EAAE0I,YAAY;UACtBtQ,GAAG,EAAEA,GAAG;UACRwU,WAAW,EAAEX;QACf,CAAC;MACH,CAAC,MAAM;QACL,MAAMA,GAAG;MACX;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASY,uBAAuBA,CAACnI,IAAI,EAAE;IACrC,MAAM9N,MAAM,GAAG;MACbuF,SAAS,EAAE,CAAC;MACZ5C,OAAO,EAAE,IAAIlC,OAAO,CAACmR,SAAS,CAACnR,OAAO,CAAC;MACvCd,KAAK,EAAEiR,QAAQ,CAAC9C,IAAI,CAAC;MACrBjI,OAAO,EAAE,KAAK;MACdrE,GAAG,EAAE8P;IACP,CAAC;IACDtR,MAAM,CAAC2C,OAAO,CAAC9B,OAAO,CAACiN,IAAI,CAAC;IAC5B,OAAO9N,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAGE,SAAS+N,aAAaA,CAACD,IAAI,EAAEoI,cAAc,EAAE;IAC3CA,cAAc,GAAGA,cAAc,IAAIzV,OAAO,CAACwQ,SAAS,IAAIxS,MAAM,CAACiK,IAAI,CAACuI,SAAS,CAAC;IAC9E,MAAMkF,SAAS,GAAGF,uBAAuB,CAACnI,IAAI,CAAC;IAE/C,MAAMsI,OAAO,GAAGF,cAAc,CAACG,MAAM,CAAC1I,WAAW,CAAC,CAAC0I,MAAM,CAACC,aAAa,CAAC,CAAChT,GAAG,CAACzE,IAAI,IAC/E8T,UAAU,CAAC9T,IAAI,EAAEiP,IAAI,EAAE,KAAK,CAC9B,CAAC;IACDsI,OAAO,CAACpB,OAAO,CAACmB,SAAS,CAAC,CAAC,CAAC;;IAE5B,MAAMI,MAAM,GAAGH,OAAO,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACpC;MACA,IAAID,CAAC,CAAClR,SAAS,KAAKmR,CAAC,CAACnR,SAAS,EAAE,OAAOmR,CAAC,CAACnR,SAAS,GAAGkR,CAAC,CAAClR,SAAS;;MAEjE;MACA;MACA,IAAIkR,CAAC,CAACrN,QAAQ,IAAIsN,CAAC,CAACtN,QAAQ,EAAE;QAC5B,IAAIuE,WAAW,CAAC8I,CAAC,CAACrN,QAAQ,CAAC,CAACuN,UAAU,KAAKD,CAAC,CAACtN,QAAQ,EAAE;UACrD,OAAO,CAAC;QACV,CAAC,MAAM,IAAIuE,WAAW,CAAC+I,CAAC,CAACtN,QAAQ,CAAC,CAACuN,UAAU,KAAKF,CAAC,CAACrN,QAAQ,EAAE;UAC5D,OAAO,CAAC,CAAC;QACX;MACF;;MAEA;MACA;MACA;MACA;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,MAAM,CAACwN,IAAI,EAAEC,UAAU,CAAC,GAAGN,MAAM;;IAEjC;IACA,MAAMvW,MAAM,GAAG4W,IAAI;IACnB5W,MAAM,CAAC8W,WAAW,GAAGD,UAAU;IAE/B,OAAO7W,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EAGE,SAAS+W,SAASA,CAACC,IAAI,EAAE;IACvB,IAAI,EAAEvW,OAAO,CAACiR,UAAU,IAAIjR,OAAO,CAACkR,KAAK,CAAC,EAAE;MAC1C,OAAOqF,IAAI;IACb;IAEA,OAAOA,IAAI,CAACpX,OAAO,CAACwR,WAAW,EAAEtN,KAAK,IAAI;MACxC,IAAIA,KAAK,KAAK,IAAI,EAAE;QAClB,OAAOrD,OAAO,CAACkR,KAAK,GAAG,MAAM,GAAG7N,KAAK;MACvC,CAAC,MAAM,IAAIrD,OAAO,CAACiR,UAAU,EAAE;QAC7B,OAAO5N,KAAK,CAAClE,OAAO,CAAC,KAAK,EAAEa,OAAO,CAACiR,UAAU,CAAC;MACjD;MACA,OAAO5N,KAAK;IACd,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASmT,eAAeA,CAACC,OAAO,EAAEC,WAAW,EAAEC,UAAU,EAAE;IACzD,MAAMhO,QAAQ,GAAG+N,WAAW,GAAGjG,OAAO,CAACiG,WAAW,CAAC,GAAGC,UAAU;IAEhEF,OAAO,CAACG,SAAS,CAAC7Y,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAI4K,QAAQ,EAAE8N,OAAO,CAACG,SAAS,CAAC7Y,GAAG,CAAC4K,QAAQ,CAAC;EAC/C;;EAEA;EACA,MAAMkO,QAAQ,GAAG;IACf,yBAAyB,EAAEC,CAAC;MAAEjV;IAAG,CAAC,KAAK;MACrC,IAAI7B,OAAO,CAACkR,KAAK,EAAE;QACjBrP,EAAE,CAACiM,SAAS,GAAGjM,EAAE,CAACiM,SAAS,CAAC3O,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC;MAC5E;IACF,CAAC;IACD,wBAAwB,EAAEiP,CAAC;MAAE7O;IAAO,CAAC,KAAK;MACxC,IAAIS,OAAO,CAACkR,KAAK,EAAE;QACjB3R,MAAM,CAACL,KAAK,GAAGK,MAAM,CAACL,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;MACpD;IACF;EACF,CAAC;EAED,MAAM4X,cAAc,GAAG,kBAAkB;EACzC;EACA,MAAMC,gBAAgB,GAAG;IACvB,wBAAwB,EAAE5I,CAAC;MAAE7O;IAAO,CAAC,KAAK;MACxC,IAAIS,OAAO,CAACiR,UAAU,EAAE;QACtB1R,MAAM,CAACL,KAAK,GAAGK,MAAM,CAACL,KAAK,CAACC,OAAO,CAAC4X,cAAc,EAAG/R,CAAC,IACpDA,CAAC,CAAC7F,OAAO,CAAC,KAAK,EAAEa,OAAO,CAACiR,UAAU,CACrC,CAAC;MACH;IACF;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,SAASgG,gBAAgBA,CAACR,OAAO,EAAE;IACjC;IACA,IAAI7W,IAAI,GAAG,IAAI;IACf,MAAM+I,QAAQ,GAAG4I,aAAa,CAACkF,OAAO,CAAC;IAEvC,IAAIrF,kBAAkB,CAACzI,QAAQ,CAAC,EAAE;;IAElC;IACAsJ,IAAI,CAAC,yBAAyB,EAC5B;MAAEpQ,EAAE,EAAE4U,OAAO;MAAE9N,QAAQ,EAAEA;IAAS,CAAC,CAAC;IAEtC/I,IAAI,GAAG6W,OAAO;IACd,MAAMpW,IAAI,GAAGT,IAAI,CAACsX,WAAW;IAC7B,MAAM3X,MAAM,GAAGoJ,QAAQ,GAAG4E,SAAS,CAAClN,IAAI,EAAE;MAAEsI,QAAQ;MAAE6E,cAAc,EAAE;IAAK,CAAC,CAAC,GAAGF,aAAa,CAACjN,IAAI,CAAC;;IAEnG;IACA4R,IAAI,CAAC,wBAAwB,EAAE;MAAEpQ,EAAE,EAAE4U,OAAO;MAAElX,MAAM;MAAEc;IAAK,CAAC,CAAC;IAE7DoW,OAAO,CAAC3I,SAAS,GAAGvO,MAAM,CAACL,KAAK;IAChCsX,eAAe,CAACC,OAAO,EAAE9N,QAAQ,EAAEpJ,MAAM,CAACoJ,QAAQ,CAAC;IACnD8N,OAAO,CAAClX,MAAM,GAAG;MACfoJ,QAAQ,EAAEpJ,MAAM,CAACoJ,QAAQ;MACzB;MACAlG,EAAE,EAAElD,MAAM,CAACuF,SAAS;MACpBqS,SAAS,EAAE5X,MAAM,CAACuF;IACpB,CAAC;IACD,IAAIvF,MAAM,CAAC8W,WAAW,EAAE;MACtBI,OAAO,CAACJ,WAAW,GAAG;QACpB1N,QAAQ,EAAEpJ,MAAM,CAAC8W,WAAW,CAAC1N,QAAQ;QACrC;QACAlG,EAAE,EAAElD,MAAM,CAAC8W,WAAW,CAACvR,SAAS;QAChCqS,SAAS,EAAE5X,MAAM,CAAC8W,WAAW,CAACvR;MAChC,CAAC;IACH;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASsS,SAASA,CAACC,WAAW,EAAE;IAC9B,IAAIA,WAAW,CAACnG,KAAK,EAAE;MACrBhB,UAAU,CAAC,QAAQ,EAAE,2CAA2C,CAAC;MACjEA,UAAU,CAAC,QAAQ,EAAE,oEAAoE,CAAC;IAC5F;IACAlQ,OAAO,GAAGoQ,SAAS,CAACpQ,OAAO,EAAEqX,WAAW,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;EACE;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIA,gBAAgB,CAACC,MAAM,EAAE;IAC7BD,gBAAgB,CAACC,MAAM,GAAG,IAAI;IAE9BrH,UAAU,CAAC,QAAQ,EAAE,gEAAgE,CAAC;IAEtF,MAAMsH,MAAM,GAAGhJ,QAAQ,CAACiJ,gBAAgB,CAAC,UAAU,CAAC;IACpDD,MAAM,CAACrZ,OAAO,CAAC8Y,gBAAgB,CAAC;EAClC,CAAC;;EAED;EACA;EACA,SAASS,sBAAsBA,CAAA,EAAG;IAChCxH,UAAU,CAAC,QAAQ,EAAE,sEAAsE,CAAC;IAC5FyH,cAAc,GAAG,IAAI;EACvB;EAEA,IAAIA,cAAc,GAAG,KAAK;;EAE1B;AACF;AACA;EACE,SAASC,YAAYA,CAAA,EAAG;IACtB;IACA,IAAIpJ,QAAQ,CAACqJ,UAAU,KAAK,SAAS,EAAE;MACrCF,cAAc,GAAG,IAAI;MACrB;IACF;IAEA,MAAMH,MAAM,GAAGhJ,QAAQ,CAACiJ,gBAAgB,CAAC,UAAU,CAAC;IACpDD,MAAM,CAACrZ,OAAO,CAAC8Y,gBAAgB,CAAC;EAClC;EAEA,SAASa,IAAIA,CAAA,EAAG;IACd;IACA,IAAIH,cAAc,EAAEC,YAAY,CAAC,CAAC;EACpC;;EAEA;EACA,IAAI,OAAOG,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,gBAAgB,EAAE;IAC5DD,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEF,IAAI,EAAE,KAAK,CAAC;EAC1D;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASG,gBAAgBA,CAAC5G,YAAY,EAAE6G,kBAAkB,EAAE;IAC1D,IAAIC,IAAI,GAAG,IAAI;IACf,IAAI;MACFA,IAAI,GAAGD,kBAAkB,CAACxL,IAAI,CAAC;IACjC,CAAC,CAAC,OAAO0L,OAAO,EAAE;MAChBrI,KAAK,CAAC,uDAAuD,CAAC5Q,OAAO,CAAC,IAAI,EAAEkS,YAAY,CAAC,CAAC;MAC1F;MACA,IAAI,CAACX,SAAS,EAAE;QAAE,MAAM0H,OAAO;MAAE,CAAC,MAAM;QAAErI,KAAK,CAACqI,OAAO,CAAC;MAAE;MAC1D;MACA;MACA;MACA;MACAD,IAAI,GAAGtH,kBAAkB;IAC3B;IACA;IACA,IAAI,CAACsH,IAAI,CAAC/Z,IAAI,EAAE+Z,IAAI,CAAC/Z,IAAI,GAAGiT,YAAY;IACxCb,SAAS,CAACa,YAAY,CAAC,GAAG8G,IAAI;IAC9BA,IAAI,CAACE,aAAa,GAAGH,kBAAkB,CAACI,IAAI,CAAC,IAAI,EAAE5L,IAAI,CAAC;IAExD,IAAIyL,IAAI,CAAC1H,OAAO,EAAE;MAChB8H,eAAe,CAACJ,IAAI,CAAC1H,OAAO,EAAE;QAAEY;MAAa,CAAC,CAAC;IACjD;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASmH,kBAAkBA,CAACnH,YAAY,EAAE;IACxC,OAAOb,SAAS,CAACa,YAAY,CAAC;IAC9B,KAAK,MAAMoH,KAAK,IAAIza,MAAM,CAACiK,IAAI,CAACwI,OAAO,CAAC,EAAE;MACxC,IAAIA,OAAO,CAACgI,KAAK,CAAC,KAAKpH,YAAY,EAAE;QACnC,OAAOZ,OAAO,CAACgI,KAAK,CAAC;MACvB;IACF;EACF;;EAEA;AACF;AACA;EACE,SAASC,aAAaA,CAAA,EAAG;IACvB,OAAO1a,MAAM,CAACiK,IAAI,CAACuI,SAAS,CAAC;EAC/B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EAGE,SAASmI,eAAeA,CAACva,IAAI,EAAE;IAC7B8R,UAAU,CAAC,QAAQ,EAAE,kDAAkD,CAAC;IACxEA,UAAU,CAAC,QAAQ,EAAE,kEAAkE,CAAC;IAExF,MAAMiI,IAAI,GAAGjL,WAAW,CAAC9O,IAAI,CAAC;IAC9B,IAAI+Z,IAAI,EAAE;MAAE,OAAOA,IAAI;IAAE;IAEzB,MAAMvD,GAAG,GAAG,IAAI/W,KAAK,CAAC,kDAAkD,CAACsB,OAAO,CAAC,IAAI,EAAEf,IAAI,CAAC,CAAC;IAC7F,MAAMwW,GAAG;EACX;;EAEA;AACF;AACA;AACA;EACE,SAAS1H,WAAWA,CAAC9O,IAAI,EAAE;IACzBA,IAAI,GAAG,CAACA,IAAI,IAAI,EAAE,EAAE+J,WAAW,CAAC,CAAC;IACjC,OAAOqI,SAAS,CAACpS,IAAI,CAAC,IAAIoS,SAAS,CAACC,OAAO,CAACrS,IAAI,CAAC,CAAC;EACpD;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASma,eAAeA,CAACK,SAAS,EAAE;IAAEvH;EAAa,CAAC,EAAE;IACpD,IAAI,OAAOuH,SAAS,KAAK,QAAQ,EAAE;MACjCA,SAAS,GAAG,CAACA,SAAS,CAAC;IACzB;IACAA,SAAS,CAACza,OAAO,CAACsa,KAAK,IAAI;MAAEhI,OAAO,CAACgI,KAAK,CAACtQ,WAAW,CAAC,CAAC,CAAC,GAAGkJ,YAAY;IAAE,CAAC,CAAC;EAC9E;;EAEA;AACF;AACA;AACA;EACE,SAASwE,aAAaA,CAACzX,IAAI,EAAE;IAC3B,MAAM+Z,IAAI,GAAGjL,WAAW,CAAC9O,IAAI,CAAC;IAC9B,OAAO+Z,IAAI,IAAI,CAACA,IAAI,CAACrH,iBAAiB;EACxC;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS+H,gBAAgBA,CAACC,MAAM,EAAE;IAChC;IACA,IAAIA,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAACA,MAAM,CAAC,yBAAyB,CAAC,EAAE;MACzEA,MAAM,CAAC,yBAAyB,CAAC,GAAIja,IAAI,IAAK;QAC5Cia,MAAM,CAAC,uBAAuB,CAAC,CAC7B9a,MAAM,CAACsI,MAAM,CAAC;UAAEkL,KAAK,EAAE3S,IAAI,CAACgD;QAAG,CAAC,EAAEhD,IAAI,CACxC,CAAC;MACH,CAAC;IACH;IACA,IAAIia,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAACA,MAAM,CAAC,wBAAwB,CAAC,EAAE;MACvEA,MAAM,CAAC,wBAAwB,CAAC,GAAIja,IAAI,IAAK;QAC3Cia,MAAM,CAAC,sBAAsB,CAAC,CAC5B9a,MAAM,CAACsI,MAAM,CAAC;UAAEkL,KAAK,EAAE3S,IAAI,CAACgD;QAAG,CAAC,EAAEhD,IAAI,CACxC,CAAC;MACH,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,SAASka,SAASA,CAACD,MAAM,EAAE;IACzBD,gBAAgB,CAACC,MAAM,CAAC;IACxBlQ,OAAO,CAAC1H,IAAI,CAAC4X,MAAM,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS7G,IAAIA,CAAChD,KAAK,EAAEtM,IAAI,EAAE;IACzB,MAAMgR,EAAE,GAAG1E,KAAK;IAChBrG,OAAO,CAACzK,OAAO,CAAC,UAAS2a,MAAM,EAAE;MAC/B,IAAIA,MAAM,CAACnF,EAAE,CAAC,EAAE;QACdmF,MAAM,CAACnF,EAAE,CAAC,CAAChR,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EAEE,SAASqW,kBAAkBA,CAACC,GAAG,EAAE;IAC/B/I,UAAU,CAAC,QAAQ,EAAE,6CAA6C,CAAC;IACnEA,UAAU,CAAC,QAAQ,EAAE,oEAAoE,CAAC;IAE1F,OAAOoG,SAAS,CAAC2C,GAAG,CAAC;EACvB;;EAEA;AACF;AACA;AACA;EACE,SAASC,uBAAuBA,CAACrX,EAAE,EAAE;IACnCqO,UAAU,CAAC,QAAQ,EAAE,kDAAkD,CAAC;IACxEA,UAAU,CAAC,QAAQ,EAAE,kCAAkC,CAAC;IAExD,OAAO+G,gBAAgB,CAACpV,EAAE,CAAC;EAC7B;;EAEA;EACA7D,MAAM,CAACsI,MAAM,CAACoG,IAAI,EAAE;IAClBa,SAAS;IACTD,aAAa;IACbsK,YAAY;IACZtB,SAAS,EAAE0C,kBAAkB;IAC7B/B,gBAAgB;IAChB;IACAkC,cAAc,EAAED,uBAAuB;IACvC9B,SAAS;IACTE,gBAAgB;IAChBI,sBAAsB;IACtBO,gBAAgB;IAChBO,kBAAkB;IAClBE,aAAa;IACbxL,WAAW;IACXqL,eAAe;IACfI,eAAe;IACf9C,aAAa;IACbzW,OAAO,EAAEgR,SAAS;IAClB2I,SAAS;IACT;IACAK,SAAS,EAAE3M,cAAc,CAACC,IAAI,CAAC,CAACqB;EAClC,CAAC,CAAC;EAEFrB,IAAI,CAAC2M,SAAS,GAAG,YAAW;IAAE3I,SAAS,GAAG,KAAK;EAAE,CAAC;EAClDhE,IAAI,CAAC4M,QAAQ,GAAG,YAAW;IAAE5I,SAAS,GAAG,IAAI;EAAE,CAAC;EAChDhE,IAAI,CAAC6M,aAAa,GAAGjN,OAAO;EAE5B,KAAK,MAAM7M,GAAG,IAAIgH,KAAK,EAAE;IACvB;IACA,IAAI,OAAOA,KAAK,CAAChH,GAAG,CAAC,KAAK,QAAQ,EAAE;MAClC;MACAlB,aAAa,CAACkI,KAAK,CAAChH,GAAG,CAAC,CAAC;IAC3B;EACF;;EAEA;EACAzB,MAAM,CAACsI,MAAM,CAACoG,IAAI,EAAEjG,KAAK,CAAC;;EAE1B;EACAiG,IAAI,CAACqM,SAAS,CAAClC,QAAQ,CAAC,CAAC,CAAC;EAC1BnK,IAAI,CAACqM,SAAS,CAAC5K,eAAe,CAAC;EAC/BzB,IAAI,CAACqM,SAAS,CAAC/B,gBAAgB,CAAC;EAChC,OAAOtK,IAAI;AACb,CAAC;;AAED;AACA,IAAIa,SAAS,GAAGgD,IAAI,CAAC,CAAC,CAAC,CAAC;AAExBiJ,MAAM,CAACC,OAAO,GAAGlM,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}