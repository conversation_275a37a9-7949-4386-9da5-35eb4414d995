{"ast": null, "code": "'use strict';\n\nmodule.exports = ada;\nada.displayName = 'ada';\nada.aliases = [];\nfunction ada(Prism) {\n  Prism.languages.ada = {\n    comment: /--.*/,\n    string: /\"(?:\"\"|[^\"\\r\\f\\n])*\"/,\n    number: [{\n      pattern: /\\b\\d(?:_?\\d)*#[\\dA-F](?:_?[\\dA-F])*(?:\\.[\\dA-F](?:_?[\\dA-F])*)?#(?:E[+-]?\\d(?:_?\\d)*)?/i\n    }, {\n      pattern: /\\b\\d(?:_?\\d)*(?:\\.\\d(?:_?\\d)*)?(?:E[+-]?\\d(?:_?\\d)*)?\\b/i\n    }],\n    'attr-name': /\\b'\\w+/,\n    keyword: /\\b(?:abort|abs|abstract|accept|access|aliased|all|and|array|at|begin|body|case|constant|declare|delay|delta|digits|do|else|elsif|end|entry|exception|exit|for|function|generic|goto|if|in|interface|is|limited|loop|mod|new|not|null|of|others|out|overriding|package|pragma|private|procedure|protected|raise|range|record|rem|renames|requeue|return|reverse|select|separate|some|subtype|synchronized|tagged|task|terminate|then|type|until|use|when|while|with|xor)\\b/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    operator: /<[=>]?|>=?|=>?|:=|\\/=?|\\*\\*?|[&+-]/,\n    punctuation: /\\.\\.?|[,;():]/,\n    char: /'.'/,\n    variable: /\\b[a-z](?:\\w)*\\b/i\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "ada", "displayName", "aliases", "Prism", "languages", "comment", "string", "number", "pattern", "keyword", "boolean", "operator", "punctuation", "char", "variable"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/refractor/lang/ada.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = ada\nada.displayName = 'ada'\nada.aliases = []\nfunction ada(Prism) {\n  Prism.languages.ada = {\n    comment: /--.*/,\n    string: /\"(?:\"\"|[^\"\\r\\f\\n])*\"/,\n    number: [\n      {\n        pattern:\n          /\\b\\d(?:_?\\d)*#[\\dA-F](?:_?[\\dA-F])*(?:\\.[\\dA-F](?:_?[\\dA-F])*)?#(?:E[+-]?\\d(?:_?\\d)*)?/i\n      },\n      {\n        pattern: /\\b\\d(?:_?\\d)*(?:\\.\\d(?:_?\\d)*)?(?:E[+-]?\\d(?:_?\\d)*)?\\b/i\n      }\n    ],\n    'attr-name': /\\b'\\w+/,\n    keyword:\n      /\\b(?:abort|abs|abstract|accept|access|aliased|all|and|array|at|begin|body|case|constant|declare|delay|delta|digits|do|else|elsif|end|entry|exception|exit|for|function|generic|goto|if|in|interface|is|limited|loop|mod|new|not|null|of|others|out|overriding|package|pragma|private|procedure|protected|raise|range|record|rem|renames|requeue|return|reverse|select|separate|some|subtype|synchronized|tagged|task|terminate|then|type|until|use|when|while|with|xor)\\b/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    operator: /<[=>]?|>=?|=>?|:=|\\/=?|\\*\\*?|[&+-]/,\n    punctuation: /\\.\\.?|[,;():]/,\n    char: /'.'/,\n    variable: /\\b[a-z](?:\\w)*\\b/i\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,sBAAsB;IAC9BC,MAAM,EAAE,CACN;MACEC,OAAO,EACL;IACJ,CAAC,EACD;MACEA,OAAO,EAAE;IACX,CAAC,CACF;IACD,WAAW,EAAE,QAAQ;IACrBC,OAAO,EACL,4cAA4c;IAC9cC,OAAO,EAAE,qBAAqB;IAC9BC,QAAQ,EAAE,oCAAoC;IAC9CC,WAAW,EAAE,eAAe;IAC5BC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}