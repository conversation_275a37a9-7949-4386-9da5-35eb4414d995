const Toggle = (props) => {
	return (
		<svg width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M7.19244 17.2426V0.75H6.99732H2.61647C1.50837 0.75 0.75 1.50837 0.75 2.6091V15.3835C0.75 16.4843 1.51205 17.2426 2.61647 17.2426H6.99732H7.19244ZM4.38722 3.65544C4.24855 3.65503 4.10988 3.65462 3.97122 3.65462C3.8301 3.65462 3.68898 3.65503 3.54786 3.65544C3.26562 3.65625 2.98338 3.65707 2.70114 3.65462C2.46185 3.65462 2.28146 3.51104 2.21888 3.3012C2.16365 3.10609 2.22624 2.89625 2.40295 2.78581C2.4913 2.73059 2.6091 2.69378 2.71218 2.69378C3.55154 2.68641 4.3909 2.68641 5.23025 2.69009C5.52845 2.69009 5.74565 2.9073 5.74197 3.17604C5.73829 3.44478 5.5174 3.65094 5.21921 3.65462C4.94188 3.65707 4.66455 3.65625 4.38722 3.65544ZM3.55468 6.55106C3.6913 6.55147 3.82778 6.55187 3.96386 6.55187C4.17001 6.55187 4.37525 6.55095 4.58049 6.55003C4.78573 6.54911 4.99096 6.54819 5.19712 6.54819C5.52477 6.55187 5.74933 6.75435 5.74565 7.03782C5.73829 7.31392 5.51372 7.5164 5.2008 7.5164C4.37985 7.52008 3.5589 7.52008 2.73795 7.5164C2.42871 7.5164 2.20415 7.31024 2.20047 7.03414C2.19679 6.75803 2.42135 6.55556 2.73427 6.55187C3.00683 6.54942 3.28103 6.55024 3.55468 6.55106ZM4.38722 5.57712C4.24855 5.57671 4.10988 5.57631 3.97122 5.57631C3.7646 5.57631 3.5589 5.57723 3.35367 5.57815C3.14935 5.57907 2.94549 5.57999 2.74163 5.57999C2.42135 5.57999 2.20047 5.38487 2.20047 5.10509C2.19679 4.8253 2.42135 4.61914 2.73427 4.61914C3.56258 4.61546 4.38722 4.61546 5.21553 4.61914C5.52477 4.61914 5.74933 4.82898 5.74565 5.10509C5.74197 5.37751 5.52477 5.57262 5.21921 5.57631C4.94188 5.57876 4.66455 5.57794 4.38722 5.57712ZM8.16449 0.75V17.2353C8.23444 17.239 8.29334 17.2426 8.35592 17.25H15.3874C16.4881 17.25 17.2465 16.4916 17.2465 15.3872V9.68106V9.49331H10.8372C10.935 9.58979 11.0289 9.68176 11.12 9.77106C11.2757 9.92369 11.4235 10.0685 11.5698 10.2149C11.8017 10.4468 11.8275 10.7523 11.6287 10.9511C11.4188 11.161 11.1391 11.1499 10.8961 10.907C10.3844 10.3989 9.87634 9.8909 9.37199 9.38286C9.12533 9.13253 9.12165 8.87115 9.3683 8.6245C9.87634 8.1091 10.388 7.59739 10.9034 7.08936C11.1354 6.86111 11.4115 6.84639 11.6213 7.05254C11.8164 7.24398 11.8017 7.55321 11.5845 7.7741C11.4851 7.87349 11.3848 7.97289 11.2845 8.07229C11.1842 8.17169 11.0838 8.27108 10.9844 8.37048C10.9556 8.3967 10.9305 8.42665 10.9011 8.46167C10.8893 8.47582 10.8767 8.4908 10.8629 8.50669H17.2428V8.32631V2.60174C17.2428 1.50837 16.4844 0.75 15.3911 0.75H8.3596H8.16449Z"
				fill="#2A4454"
			/>
		</svg>
	);
};

export default Toggle;
