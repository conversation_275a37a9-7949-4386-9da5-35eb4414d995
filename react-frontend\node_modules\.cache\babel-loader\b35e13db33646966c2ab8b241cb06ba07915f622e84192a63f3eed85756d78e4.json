{"ast": null, "code": "'use strict';\n\nmodule.exports = abnf;\nabnf.displayName = 'abnf';\nabnf.aliases = [];\nfunction abnf(Prism) {\n  ;\n  (function (Prism) {\n    var coreRules = '(?:ALPHA|BIT|CHAR|CR|CRLF|CTL|DIGIT|DQUOTE|HEXDIG|HTAB|LF|LWSP|OCTET|SP|VCHAR|WSP)';\n    Prism.languages.abnf = {\n      comment: /;.*/,\n      string: {\n        pattern: /(?:%[is])?\"[^\"\\n\\r]*\"/,\n        greedy: true,\n        inside: {\n          punctuation: /^%[is]/\n        }\n      },\n      range: {\n        pattern: /%(?:b[01]+-[01]+|d\\d+-\\d+|x[A-F\\d]+-[A-F\\d]+)/i,\n        alias: 'number'\n      },\n      terminal: {\n        pattern: /%(?:b[01]+(?:\\.[01]+)*|d\\d+(?:\\.\\d+)*|x[A-F\\d]+(?:\\.[A-F\\d]+)*)/i,\n        alias: 'number'\n      },\n      repetition: {\n        pattern: /(^|[^\\w-])(?:\\d*\\*\\d*|\\d+)/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      definition: {\n        pattern: /(^[ \\t]*)(?:[a-z][\\w-]*|<[^<>\\r\\n]*>)(?=\\s*=)/m,\n        lookbehind: true,\n        alias: 'keyword',\n        inside: {\n          punctuation: /<|>/\n        }\n      },\n      'core-rule': {\n        pattern: RegExp('(?:(^|[^<\\\\w-])' + coreRules + '|<' + coreRules + '>)(?![\\\\w-])', 'i'),\n        lookbehind: true,\n        alias: ['rule', 'constant'],\n        inside: {\n          punctuation: /<|>/\n        }\n      },\n      rule: {\n        pattern: /(^|[^<\\w-])[a-z][\\w-]*|<[^<>\\r\\n]*>/i,\n        lookbehind: true,\n        inside: {\n          punctuation: /<|>/\n        }\n      },\n      operator: /=\\/?|\\//,\n      punctuation: /[()\\[\\]]/\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "abnf", "displayName", "aliases", "Prism", "coreRules", "languages", "comment", "string", "pattern", "greedy", "inside", "punctuation", "range", "alias", "terminal", "repetition", "lookbehind", "definition", "RegExp", "rule", "operator"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/refractor/lang/abnf.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = abnf\nabnf.displayName = 'abnf'\nabnf.aliases = []\nfunction abnf(Prism) {\n  ;(function (Prism) {\n    var coreRules =\n      '(?:ALPHA|BIT|CHAR|CR|CRLF|CTL|DIGIT|DQUOTE|HEXDIG|HTAB|LF|LWSP|OCTET|SP|VCHAR|WSP)'\n    Prism.languages.abnf = {\n      comment: /;.*/,\n      string: {\n        pattern: /(?:%[is])?\"[^\"\\n\\r]*\"/,\n        greedy: true,\n        inside: {\n          punctuation: /^%[is]/\n        }\n      },\n      range: {\n        pattern: /%(?:b[01]+-[01]+|d\\d+-\\d+|x[A-F\\d]+-[A-F\\d]+)/i,\n        alias: 'number'\n      },\n      terminal: {\n        pattern:\n          /%(?:b[01]+(?:\\.[01]+)*|d\\d+(?:\\.\\d+)*|x[A-F\\d]+(?:\\.[A-F\\d]+)*)/i,\n        alias: 'number'\n      },\n      repetition: {\n        pattern: /(^|[^\\w-])(?:\\d*\\*\\d*|\\d+)/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      definition: {\n        pattern: /(^[ \\t]*)(?:[a-z][\\w-]*|<[^<>\\r\\n]*>)(?=\\s*=)/m,\n        lookbehind: true,\n        alias: 'keyword',\n        inside: {\n          punctuation: /<|>/\n        }\n      },\n      'core-rule': {\n        pattern: RegExp(\n          '(?:(^|[^<\\\\w-])' + coreRules + '|<' + coreRules + '>)(?![\\\\w-])',\n          'i'\n        ),\n        lookbehind: true,\n        alias: ['rule', 'constant'],\n        inside: {\n          punctuation: /<|>/\n        }\n      },\n      rule: {\n        pattern: /(^|[^<\\w-])[a-z][\\w-]*|<[^<>\\r\\n]*>/i,\n        lookbehind: true,\n        inside: {\n          punctuation: /<|>/\n        }\n      },\n      operator: /=\\/?|\\//,\n      punctuation: /[()\\[\\]]/\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,SAAS,GACX,oFAAoF;IACtFD,KAAK,CAACE,SAAS,CAACL,IAAI,GAAG;MACrBM,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;QACNC,OAAO,EAAE,uBAAuB;QAChCC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF,CAAC;MACDC,KAAK,EAAE;QACLJ,OAAO,EAAE,gDAAgD;QACzDK,KAAK,EAAE;MACT,CAAC;MACDC,QAAQ,EAAE;QACRN,OAAO,EACL,kEAAkE;QACpEK,KAAK,EAAE;MACT,CAAC;MACDE,UAAU,EAAE;QACVP,OAAO,EAAE,4BAA4B;QACrCQ,UAAU,EAAE,IAAI;QAChBH,KAAK,EAAE;MACT,CAAC;MACDI,UAAU,EAAE;QACVT,OAAO,EAAE,gDAAgD;QACzDQ,UAAU,EAAE,IAAI;QAChBH,KAAK,EAAE,SAAS;QAChBH,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF,CAAC;MACD,WAAW,EAAE;QACXH,OAAO,EAAEU,MAAM,CACb,iBAAiB,GAAGd,SAAS,GAAG,IAAI,GAAGA,SAAS,GAAG,cAAc,EACjE,GACF,CAAC;QACDY,UAAU,EAAE,IAAI;QAChBH,KAAK,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;QAC3BH,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF,CAAC;MACDQ,IAAI,EAAE;QACJX,OAAO,EAAE,sCAAsC;QAC/CQ,UAAU,EAAE,IAAI;QAChBN,MAAM,EAAE;UACNC,WAAW,EAAE;QACf;MACF,CAAC;MACDS,QAAQ,EAAE,SAAS;MACnBT,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAER,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}