import React from "react";
import { render, screen } from "@testing-library/react";

import StepsPage from "../StepsPage";
import { MemoryRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import { init } from "@rematch/core";
import { Provider } from "react-redux";
import * as models from "../../../models";

test("renders steps page", async () => {
  const store = init({ models });
  render(
    <Provider store={store}>
      <MemoryRouter>
        <StepsPage />
      </MemoryRouter>
    </Provider>,
  );
  expect(screen.getByRole("steps-datatable")).toBeInTheDocument();
  expect(screen.getByRole("steps-add-button")).toBeInTheDocument();
});
