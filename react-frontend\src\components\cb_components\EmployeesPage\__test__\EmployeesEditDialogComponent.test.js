import React from "react";
import { render, screen } from "@testing-library/react";

import EmployeesEditDialogComponent from "../EmployeesEditDialogComponent";
import { MemoryRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import { init } from "@rematch/core";
import { Provider } from "react-redux";
import * as models from "../../../models";

test("renders employees edit dialog", async () => {
  const store = init({ models });
  render(
    <Provider store={store}>
      <MemoryRouter>
        <EmployeesEditDialogComponent show={true} />
      </MemoryRouter>
    </Provider>,
  );
  expect(
    screen.getByRole("employees-edit-dialog-component"),
  ).toBeInTheDocument();
});
