import * as React from "react";
const Files = (props) => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M6.42776 3.42243C6.42961 3.548 6.4315 3.67582 6.4315 3.80676L6.43043 3.80783C6.69957 3.80783 6.9646 3.80756 7.22684 3.8073C7.795 3.80672 8.35003 3.80616 8.90532 3.80836C9.56743 3.81104 10.0447 4.11531 10.3383 4.70779C10.4071 4.84682 10.4766 4.98561 10.5461 5.12442C10.748 5.52793 10.9501 5.93164 11.137 6.34218C11.2474 6.58431 11.3807 6.67859 11.6604 6.67752C14.4931 6.6656 17.3263 6.66873 20.1591 6.67185L20.4419 6.67216C20.646 6.67216 20.856 6.68127 21.0526 6.72841C21.771 6.90144 22.2017 7.50409 22.2028 8.32959C22.2044 9.66807 22.2042 11.0065 22.2041 12.345C22.204 13.2377 22.2039 14.1304 22.2044 15.023C22.2044 15.0998 22.2092 15.1766 22.2143 15.2578C22.2166 15.2944 22.219 15.3319 22.221 15.3707C22.3322 15.3707 22.4397 15.3708 22.5447 15.371C22.7475 15.3712 22.9411 15.3714 23.1349 15.3707C23.7289 15.368 24 15.097 24 14.5023V5.74756C24 5.12295 23.7504 4.87492 23.1225 4.87492C22.4477 4.87479 21.7728 4.87441 21.0979 4.87403C18.9886 4.87284 16.879 4.87164 14.7695 4.87814C14.0228 4.88082 13.4876 4.58672 13.1646 3.90264C12.8946 3.32999 12.6102 2.7643 12.3161 2.20343C12.2689 2.11344 12.1387 2.01005 12.0461 2.00951C10.4042 1.99826 8.76175 1.99719 7.11986 2.00523C6.75988 2.00684 6.45935 2.26665 6.43739 2.60252C6.41961 2.87104 6.4236 3.1411 6.42776 3.42243ZM3.21687 7.01716C3.21687 6.88509 3.21499 6.75693 3.21316 6.63153C3.20911 6.3546 3.20526 6.09116 3.22222 5.82899C3.2458 5.46205 3.55382 5.22099 3.97541 5.2167C4.43455 5.21156 4.8937 5.21246 5.35284 5.21336H5.3529C5.56335 5.21378 5.7738 5.21419 5.98424 5.21402C6.22382 5.21402 6.46339 5.21431 6.70297 5.21459C7.39175 5.21541 8.08052 5.21623 8.7693 5.21027C8.95679 5.20866 9.05911 5.27134 9.13946 5.44169C9.26009 5.69874 9.3867 5.95309 9.51331 6.20744L9.51332 6.20747C9.65716 6.49641 9.80099 6.78535 9.93603 7.07822C10.2516 7.76284 10.7749 8.08425 11.5249 8.08318C13.7022 8.07963 15.8795 8.08015 18.0567 8.08067C18.8343 8.08085 19.6119 8.08104 20.3895 8.08104C20.6577 8.08104 20.792 8.21032 20.7923 8.46888V17.6817C20.7923 18.3256 20.5491 18.5672 19.9009 18.5709C19.8367 18.5713 19.7723 18.5697 19.7079 18.5681H19.7078C19.5754 18.5648 19.4431 18.5615 19.3122 18.5752C19.0578 18.6015 18.9801 18.519 18.9812 18.2543C18.9886 16.6972 18.9883 15.1402 18.9879 13.5829C18.9878 12.9082 18.9876 12.2335 18.9881 11.5587C18.9881 11.2138 18.9496 10.8816 18.7497 10.587C18.4101 10.0867 17.9334 9.87185 17.335 9.87292C16.2135 9.87456 15.0921 9.87455 13.9707 9.87455C12.155 9.87454 10.3394 9.87454 8.52395 9.88149C8.21968 9.88256 8.04076 9.81667 7.9138 9.51615C7.7796 9.19795 7.62286 8.88929 7.46609 8.58055L7.46608 8.58054C7.36817 8.38771 7.27023 8.19485 7.17776 7.99961C6.85635 7.32143 6.33084 6.99948 5.57819 7.01341C5.0416 7.02343 4.50475 7.02111 3.95589 7.01873C3.71243 7.01768 3.46661 7.01662 3.2174 7.01662L3.21687 7.01716ZM8.75907 21.7888H0.966386C0.234097 21.7888 0 21.5574 0 20.8358V9.32117C0 8.67138 0.253382 8.41961 0.907995 8.41907C1.28145 8.41907 1.6549 8.41936 2.02835 8.41964H2.02855C3.19032 8.42053 4.35209 8.42142 5.51386 8.41371C5.73564 8.41211 5.84867 8.48443 5.94081 8.6821C6.11863 9.06299 6.30641 9.43927 6.49417 9.81551L6.49422 9.8156C6.5853 9.99811 6.67637 10.1806 6.76631 10.3636C7.07808 10.9979 7.58805 11.2957 8.29195 11.2952C10.7237 11.2926 13.1552 11.2932 15.5868 11.2938L17.2091 11.2941C17.4537 11.2941 17.5762 11.4164 17.5765 11.6611L17.5767 14.7407V14.7653C17.5768 16.81 17.5769 18.8547 17.5765 20.8996C17.5765 21.5387 17.3264 21.7888 16.6846 21.7894C14.9233 21.7897 13.162 21.7896 11.4005 21.7895L8.758 21.7894L8.75907 21.7888ZM11.8934 19.255L11.8991 19.2551C11.9015 19.2551 11.9039 19.2553 11.9063 19.2556C11.9063 19.2864 11.9054 19.3174 11.9045 19.3483V19.3483C11.9025 19.4158 11.9005 19.4832 11.9079 19.5496C11.947 19.9107 12.2609 20.1962 12.6112 20.1968C12.9669 20.1973 13.276 19.9198 13.3087 19.5502C13.3258 19.3557 13.3253 19.1565 13.3071 18.962C13.2739 18.609 12.9985 18.3535 12.6525 18.331C12.3182 18.309 12.0043 18.542 11.9266 18.8827C11.9079 18.9651 11.904 19.0509 11.9001 19.1369C11.8984 19.1762 11.8966 19.2155 11.8934 19.2545V19.255ZM15.6292 19.1773V19.1773C15.6304 19.1262 15.6317 19.0699 15.6272 19.014C15.5945 18.6138 15.3149 18.3385 14.9377 18.331C14.5676 18.324 14.2595 18.5972 14.2269 18.9909C14.2092 19.2025 14.2081 19.4222 14.2456 19.6295C14.3313 20.0998 14.8456 20.3318 15.2575 20.1057C15.6009 19.9171 15.6475 19.6 15.6277 19.2395H15.6282C15.6282 19.22 15.6287 19.1991 15.6292 19.1773Z"
      fill="#2A4454"
    />
  </svg>
);
export default Files;
