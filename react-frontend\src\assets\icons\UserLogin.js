import * as React from 'react';
const UserLogin = (props) => (
	<svg width="18" height="18" viewBox="0 0 24 24"  xmlns="http://www.w3.org/2000/svg" fill="currentColor" {...props}>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.3433 2.01491C15.3706 2.01743 15.3983 2.01999 15.4267 2.02188C15.4213 2.05403 15.4213 2.06474 15.4213 2.0701C15.4051 2.08227 15.3858 2.09137 15.3681 2.09973C15.3624 2.1024 15.3569 2.105 15.3517 2.1076C14.4355 2.48263 14.0016 3.16303 14.0016 4.14882C14.0016 8.99736 14.0016 13.8459 14.0069 18.6945C14.0069 18.9355 13.948 19.0159 13.6962 19.0106C13.1755 19.0009 12.6568 19.0028 12.1376 19.0048C11.7914 19.0061 11.445 19.0073 11.0978 19.0052C10.396 19.0052 10.0049 18.6087 9.99951 17.9069C9.99722 17.6429 9.99787 17.3788 9.99853 17.1147C9.9994 16.7627 10.0003 16.4106 9.99415 16.0586C9.9888 15.4425 9.57091 15.0138 9.00837 15.0085C8.43512 15.0031 8.00652 15.4424 8.00116 16.0639C7.99044 16.7229 7.98509 17.3872 8.00116 18.0462C8.03866 19.7285 9.3459 21.0035 11.0335 21.0089C11.6228 21.0125 12.2122 21.0113 12.8015 21.0101C13.0962 21.0095 13.3908 21.0089 13.6855 21.0089H13.9801C13.9825 21.0529 13.9858 21.0909 13.9888 21.1255C13.9928 21.1709 13.9962 21.2105 13.9962 21.25C14.0016 21.3768 14.0016 21.5048 14.0016 21.6329C14.0016 21.8893 14.0016 22.1465 14.0444 22.3965C14.2427 23.5805 15.4642 24.2823 16.6696 23.8912C18.6733 23.243 20.6717 22.5733 22.6647 21.8983C23.4683 21.625 23.9987 20.8696 23.9987 20.0124C24.004 14.012 24.004 8.01158 23.9987 2.01117C23.9987 0.891449 23.1201 0.0128168 22.0057 0.0128168C18.3144 0.00210178 14.6284 -0.00325573 10.9424 0.00210178C9.41554 0.00210178 8.1351 1.20754 8.01723 2.73443C7.98509 3.15232 7.99044 3.57556 8.00116 3.99345C8.01723 4.58277 8.44583 5.00602 9.00302 5.00066C9.55484 5.00066 9.97272 4.57742 9.99415 3.98809C10.0022 3.77932 10.0012 3.57356 10.0002 3.36629C9.99985 3.29698 9.99951 3.22749 9.99951 3.15768C9.99951 2.38619 10.3799 2.00581 11.1514 2.00581H15.1963C15.2448 2.00581 15.2933 2.01029 15.3433 2.01491ZM5.00161 7.69013V9.00272H4.66409C4.2585 9.00272 3.85351 9.00152 3.44872 9.00033C2.63974 8.99795 1.83155 8.99558 1.02098 9.00272C0.260211 9.00807 -0.21125 9.73134 0.0941279 10.4064C0.286998 10.8296 0.629879 11.0064 1.08527 11.0064C1.42644 11.0064 1.76805 11.0073 2.10984 11.0082C2.96496 11.0104 3.82125 11.0126 4.6748 11.0011C4.95875 10.9957 5.01768 11.0868 5.01232 11.3493C5.00084 11.9616 5.00303 12.5739 5.00522 13.1862C5.00609 13.4311 5.00697 13.676 5.00697 13.9209C5.00697 14.3817 5.18376 14.7406 5.62308 14.9228C6.0624 15.1049 6.44278 14.9817 6.76959 14.6549C8.07146 13.353 9.37334 12.0511 10.6699 10.7493C11.136 10.2832 11.136 9.72062 10.6752 9.25452C10.0269 8.60357 9.376 7.95397 8.72505 7.30436C8.07412 6.65477 7.42319 6.00518 6.77495 5.35425C6.43742 5.01137 6.04097 4.90422 5.60165 5.09173C5.16233 5.27925 4.99625 5.64892 5.00161 6.10966C5.00518 6.45988 5.00399 6.81249 5.0028 7.1643C5.0022 7.33991 5.00161 7.51531 5.00161 7.69013Z"/>
</svg>

);
export default UserLogin;
