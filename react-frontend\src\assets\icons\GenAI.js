import * as React from 'react';
const GenAI = (props) => (
	<svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} fill="currenColor" {...props}>
		<path
			fill="currentColor"
			fillRule="evenodd"
			d="M13.824.06a.12.12 0 0 0-.208 0c-.004.009-.008.017-.021.017l-.252.46c-.39.712-.783 1.426-1.18 2.137a.123.123 0 0 1-.046.047c-.55.309-1.103.61-1.656.91l-.73.399c-.046.024-.091.05-.138.077l-.103.059c-.076.046-.076.165.004.207l.703.383c.596.324 1.19.649 1.784.977.097.055.19.144.246.242.215.378.423.76.632 1.143l.236.433c.11.203.222.404.337.61l.176.317a.12.12 0 0 0 .207 0l.02-.034a942.607 942.607 0 0 0 .398-.725c.321-.587.642-1.173.968-1.757a.54.54 0 0 1 .199-.204c.486-.268.973-.533 1.46-.797l1.048-.57a.127.127 0 0 0 .034-.018.12.12 0 0 0-.005-.207c-.864-.47-1.698-.928-2.529-1.386a.525.525 0 0 1-.19-.182 51.052 51.052 0 0 1-.437-.792l-.245-.45c-.23-.423-.462-.847-.712-1.296Zm-6.63 17.891h-2.39v-3.656h-2.39v3.652H0V9.266c0-2 1.568-3.588 3.571-3.622 1.95-.03 3.618 1.576 3.627 3.52.009 2.052.007 4.104.005 6.156v2.462c0 .042-.003.08-.007.126l-.002.043Zm-2.428-6.07a.298.298 0 0 1 .014-.025c.007-.01.012-.019.012-.027l.003-.807c.005-.648.009-1.296-.012-1.942-.017-.58-.585-1.038-1.195-1.034-.605.004-1.152.475-1.165 1.06-.014.63-.01 1.258-.007 1.887l.003.84c0 .*************.033l.007.01c.772.004 1.551.004 2.322.004Zm3.516-5.623h2.377v11.689H8.282V6.258Z"
			clipRule="evenodd"
		/>
	</svg>
);
export default GenAI;
