module.exports = function (app) {
  const modelName = "tests";
  const mongooseClient = app.get("mongooseClient");
  const { Schema } = mongooseClient;
  const schema = new Schema(
    {
      stack: {
        type: String,
        required: false,
        unique: false,
        lowercase: false,
        uppercase: false,
        minLength: 3,
        maxLength: 1000000,
        index: true,
        trim: true,
      },
      service: {
        type: String,
        required: false,
        unique: false,
        lowercase: false,
        uppercase: false,
        minLength: 3,
        maxLength: 100000000,
        index: true,
        trim: true,
      },
      passed: { type: Number, required: false, min: 0, max: 100000000 },
      failed: { type: Number, required: false, min: 0, max: 100000000 },
      notes: {
        type: String,
        required: false,
        unique: false,
        lowercase: false,
        uppercase: false,
        minLength: 3,
        maxLength: 100000000,
        index: true,
        trim: true,
      },

      createdBy: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true,
      },
      updatedBy: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true,
      },
    },
    {
      timestamps: true,
    },
  );

  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName);
  }
  return mongooseClient.model(modelName, schema);
};
