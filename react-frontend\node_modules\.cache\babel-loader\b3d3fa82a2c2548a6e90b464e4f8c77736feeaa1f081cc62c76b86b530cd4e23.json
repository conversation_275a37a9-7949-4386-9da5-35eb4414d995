{"ast": null, "code": "'use strict';\n\nmodule.exports = asm6502;\nasm6502.displayName = 'asm6502';\nasm6502.aliases = [];\nfunction asm6502(Prism) {\n  Prism.languages.asm6502 = {\n    comment: /;.*/,\n    directive: {\n      pattern: /\\.\\w+(?= )/,\n      alias: 'property'\n    },\n    string: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    'op-code': {\n      pattern: /\\b(?:ADC|AND|ASL|BCC|BCS|BEQ|BIT|BMI|BNE|BPL|BRK|BVC|BVS|CLC|CLD|CLI|CLV|CMP|CPX|CPY|DEC|DEX|DEY|EOR|INC|INX|INY|JMP|JSR|LDA|LDX|LDY|LSR|NOP|ORA|PHA|PHP|PLA|PLP|ROL|ROR|RTI|RTS|SBC|SEC|SED|SEI|STA|STX|STY|TAX|TAY|TSX|TXA|TXS|TYA|adc|and|asl|bcc|bcs|beq|bit|bmi|bne|bpl|brk|bvc|bvs|clc|cld|cli|clv|cmp|cpx|cpy|dec|dex|dey|eor|inc|inx|iny|jmp|jsr|lda|ldx|ldy|lsr|nop|ora|pha|php|pla|plp|rol|ror|rti|rts|sbc|sec|sed|sei|sta|stx|sty|tax|tay|tsx|txa|txs|tya)\\b/,\n      alias: 'keyword'\n    },\n    'hex-number': {\n      pattern: /#?\\$[\\da-f]{1,4}\\b/i,\n      alias: 'number'\n    },\n    'binary-number': {\n      pattern: /#?%[01]+\\b/,\n      alias: 'number'\n    },\n    'decimal-number': {\n      pattern: /#?\\b\\d+\\b/,\n      alias: 'number'\n    },\n    register: {\n      pattern: /\\b[xya]\\b/i,\n      alias: 'variable'\n    },\n    punctuation: /[(),:]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "asm6502", "displayName", "aliases", "Prism", "languages", "comment", "directive", "pattern", "alias", "string", "register", "punctuation"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/refractor/lang/asm6502.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = asm6502\nasm6502.displayName = 'asm6502'\nasm6502.aliases = []\nfunction asm6502(Prism) {\n  Prism.languages.asm6502 = {\n    comment: /;.*/,\n    directive: {\n      pattern: /\\.\\w+(?= )/,\n      alias: 'property'\n    },\n    string: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    'op-code': {\n      pattern:\n        /\\b(?:ADC|AND|ASL|BCC|BCS|BEQ|BIT|BMI|BNE|BPL|BRK|BVC|BVS|CLC|CLD|CLI|CLV|CMP|CPX|CPY|DEC|DEX|DEY|EOR|INC|INX|INY|JMP|JSR|LDA|LDX|LDY|LSR|NOP|ORA|PHA|PHP|PLA|PLP|ROL|ROR|RTI|RTS|SBC|SEC|SED|SEI|STA|STX|STY|TAX|TAY|TSX|TXA|TXS|TYA|adc|and|asl|bcc|bcs|beq|bit|bmi|bne|bpl|brk|bvc|bvs|clc|cld|cli|clv|cmp|cpx|cpy|dec|dex|dey|eor|inc|inx|iny|jmp|jsr|lda|ldx|ldy|lsr|nop|ora|pha|php|pla|plp|rol|ror|rti|rts|sbc|sec|sed|sei|sta|stx|sty|tax|tay|tsx|txa|txs|tya)\\b/,\n      alias: 'keyword'\n    },\n    'hex-number': {\n      pattern: /#?\\$[\\da-f]{1,4}\\b/i,\n      alias: 'number'\n    },\n    'binary-number': {\n      pattern: /#?%[01]+\\b/,\n      alias: 'number'\n    },\n    'decimal-number': {\n      pattern: /#?\\b\\d+\\b/,\n      alias: 'number'\n    },\n    register: {\n      pattern: /\\b[xya]\\b/i,\n      alias: 'variable'\n    },\n    punctuation: /[(),:]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,WAAW,GAAG,SAAS;AAC/BD,OAAO,CAACE,OAAO,GAAG,EAAE;AACpB,SAASF,OAAOA,CAACG,KAAK,EAAE;EACtBA,KAAK,CAACC,SAAS,CAACJ,OAAO,GAAG;IACxBK,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE;MACTC,OAAO,EAAE,YAAY;MACrBC,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE,mCAAmC;IAC3C,SAAS,EAAE;MACTF,OAAO,EACL,ycAAyc;MAC3cC,KAAK,EAAE;IACT,CAAC;IACD,YAAY,EAAE;MACZD,OAAO,EAAE,qBAAqB;MAC9BC,KAAK,EAAE;IACT,CAAC;IACD,eAAe,EAAE;MACfD,OAAO,EAAE,YAAY;MACrBC,KAAK,EAAE;IACT,CAAC;IACD,gBAAgB,EAAE;MAChBD,OAAO,EAAE,WAAW;MACpBC,KAAK,EAAE;IACT,CAAC;IACDE,QAAQ,EAAE;MACRH,OAAO,EAAE,YAAY;MACrBC,KAAK,EAAE;IACT,CAAC;IACDG,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}