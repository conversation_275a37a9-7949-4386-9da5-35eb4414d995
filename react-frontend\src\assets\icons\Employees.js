import * as React from 'react';
const Employees = (props) => (
	<svg width="18" height="18" viewBox="0 0 24 24"  xmlns="http://www.w3.org/2000/svg"fill="currentColor" {...props}>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.7254 6.54804C13.5248 6.56678 15.0119 5.09848 15.0181 3.28652C15.0306 1.48082 13.5623 1.9867e-05 11.7504 1.9867e-05C9.94465 -0.00622824 8.4826 1.46208 8.4826 3.27403C8.4826 5.07973 9.91966 6.52929 11.7254 6.54804ZM8.3319 19.1848C8.33229 19.4504 8.33268 19.7159 8.33268 19.9815C8.33268 20.3397 8.33199 20.6972 8.33129 21.0548C8.3299 21.7698 8.32852 22.4849 8.33268 23.2055C8.33893 23.7116 8.6076 23.999 9.09495 23.999C9.45 23.999 9.8048 23.9993 10.1594 23.9995H10.1595H10.1596H10.1597H10.1598C11.5773 24.0005 12.9915 24.0015 14.4058 23.9865C14.6058 23.9865 14.8682 23.8678 14.9932 23.7178C15.1244 23.5616 15.1619 23.2992 15.1619 23.0805C15.1712 21.5153 15.1701 19.9535 15.1689 18.3926V18.3923V18.392V18.3917V18.3914C15.1685 17.8717 15.1681 17.3521 15.1681 16.8324L15.1681 16.8161C15.1679 16.6544 15.1677 16.5164 15.3431 16.4076C16.0054 15.9952 16.3303 15.3766 16.3303 14.6018C16.3313 14.3519 16.3325 14.102 16.3337 13.8521C16.3398 12.6025 16.3459 11.3528 16.3303 10.1032C16.3115 8.84734 15.4618 7.9726 14.2059 7.95386C12.5689 7.92887 10.9381 7.92887 9.30114 7.95386C8.07651 7.9726 7.21427 8.80985 7.18928 10.0095C7.15804 11.6027 7.17678 13.196 7.19553 14.7892V14.7893C7.20177 15.4703 7.54542 15.9952 8.10775 16.3701C8.2827 16.4825 8.33893 16.595 8.33893 16.7949C8.32956 17.5916 8.33073 18.3882 8.3319 19.1848ZM16.5676 21.4685V22.1496H16.955H20.7289C21.4537 22.1496 21.6786 21.9246 21.6786 21.1998C21.6786 20.8034 21.6782 20.4066 21.6778 20.0095V20.0091C21.6766 18.8166 21.6755 17.6214 21.6848 16.4263C21.6848 16.3013 21.7348 16.1264 21.8286 16.0577C22.3721 15.6453 22.6096 15.1079 22.6096 14.4394C22.6096 14.0645 22.6103 13.6896 22.611 13.3147C22.6123 12.565 22.6137 11.8152 22.6096 11.0654C22.6033 9.84704 21.8535 9.10351 20.6352 9.09726C20.0312 9.0931 19.4272 9.09449 18.8232 9.09587C18.5212 9.09657 18.2192 9.09726 17.9172 9.09726C17.863 9.09726 17.8064 9.10206 17.7459 9.10719L17.7458 9.1072L17.7457 9.10721L17.7456 9.10722L17.7456 9.10723C17.7086 9.11037 17.6701 9.11364 17.6298 9.11601C17.6391 9.19226 17.6517 9.2651 17.664 9.33579C17.685 9.45622 17.7048 9.5704 17.7048 9.68458C17.7065 10.1583 17.7096 10.632 17.7127 11.1057C17.721 12.369 17.7292 13.6322 17.7111 14.8955C17.6986 15.739 17.3112 16.4513 16.7488 17.0761C16.6489 17.1886 16.5739 17.3635 16.5739 17.5072C16.5645 18.4962 16.5657 19.4886 16.5669 20.4794V20.48V20.4805V20.4811V20.4817C16.5673 20.8109 16.5676 21.1399 16.5676 21.4685ZM5.85218 9.10351C5.72911 10.4309 5.74518 11.7397 5.76118 13.0431V13.0431V13.0431V13.0431C5.76793 13.5925 5.77466 14.1409 5.77096 14.6893C5.76471 15.6078 6.13335 16.4076 6.76441 17.0823C6.84563 17.1698 6.93311 17.3073 6.93311 17.426C6.94247 18.5644 6.94131 19.7028 6.94014 20.8439C6.93974 21.2246 6.93935 21.6057 6.93935 21.9871C6.93935 22.0077 6.93558 22.0302 6.93115 22.0566C6.92751 22.0783 6.92343 22.1027 6.92061 22.1308C6.88341 22.133 6.84697 22.136 6.81103 22.1389L6.81085 22.1389L6.81079 22.1389C6.74421 22.1443 6.67933 22.1496 6.61445 22.1496H2.83435C2.1658 22.1496 1.93462 21.9184 1.93462 21.2498V16.4388C1.93462 16.2451 1.89088 16.1201 1.72218 15.9952C1.27232 15.664 1.01615 15.2079 1.0099 14.6393C1.00885 14.4282 1.00763 14.2172 1.00641 14.0063C1.00034 12.9568 0.994292 11.9102 1.0099 10.8592C1.0224 9.89702 1.73468 9.14725 2.69689 9.10976C3.41813 9.084 4.13937 9.09069 4.86263 9.0974C5.192 9.10045 5.52178 9.10351 5.85218 9.10351ZM18.8107 8.26002C17.2987 8.26002 16.0678 7.02914 16.0615 5.52334C16.0615 4.00505 17.3237 2.74918 18.8295 2.76168C20.329 2.77418 21.5661 4.0238 21.5599 5.52959C21.5599 7.03539 20.3227 8.26002 18.8107 8.26002ZM4.8025 8.26002C6.32079 8.24752 7.53917 7.0104 7.52667 5.49835C7.51418 3.98631 6.2708 2.74919 4.77126 2.76168C3.27796 2.76793 2.02209 4.0238 2.02209 5.5171C2.02209 7.02914 3.27796 8.27251 4.8025 8.26002Z" />
</svg>

);
export default Employees;
