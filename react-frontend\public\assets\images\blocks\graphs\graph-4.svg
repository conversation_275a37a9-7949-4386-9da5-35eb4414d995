<svg width="270" height="74" viewBox="0 0 270 74" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="270" height="74">
<rect width="270" height="74" fill="#CACACA"/>
</mask>
<g mask="url(#mask0)">
<path d="M270 74.4996H270.5V73.9996V6.94922V5.95236L269.701 6.54846L264.301 10.5772L264.176 10.6703L264.126 10.8178L258.776 26.6416L253.933 30.2551L248.822 22.2204L248.348 21.4754L247.953 22.265L242.829 32.5037L238.031 28.6683L232.677 11.6195L232.513 11.0959L232.006 11.3083L226.284 13.711L221.422 13.5L221.323 13.4958L221.231 13.5291L216.089 15.3787L210.906 11.3737L210.89 11.3616L210.873 11.3507L205.473 7.8255L205.25 7.67948L205.004 7.78424L199.794 10.0054L194.586 7.92378L194.358 7.83294L194.147 7.95672L188.747 11.1222L188.633 11.189L188.567 11.3035L183.49 20.0959L178.551 15.2263L178.164 14.8447L177.816 15.2621L172.654 21.4514L167.596 19.295L167.239 19.1428L167.003 19.4509L161.845 26.186L156.806 23.9038L156.445 23.74L156.203 24.0553L151.283 30.4784L146.245 20.6774L145.954 20.1115L145.473 20.5281L140.167 25.1223L134.89 26.3177L134.692 26.3624L134.581 26.532L129.477 34.352L124.506 30.5104L124.082 30.1833L123.785 30.6277L118.385 38.6853L118.358 38.7247L118.34 38.7684L113.21 50.8646L108.33 46.5737L107.787 46.0964L107.532 46.7728L102.348 60.5179L97.5013 57.4832L92.2426 52.7892L86.8819 33.4345L85.9128 33.4554L80.6471 56.2552L75.8184 54.1966L70.4397 51.2586L70.2331 51.1457L70.0144 51.2331L64.853 53.2961L59.7824 50.0535L54.444 39.7409L54.1478 39.1687L53.667 39.5978L48.4622 44.2437L43.3166 43.0098L42.8864 42.9066L42.7316 43.3211L37.5397 57.2242L32.6529 54.3596L32.197 54.0923L31.9553 54.5623L27.0536 64.0967L22.0633 51.7972L22.0181 51.6859L21.9273 51.6072L16.5273 46.931L16.4592 46.872L16.3748 46.8405L10.9748 44.8261L10.6673 44.7114L10.4397 44.9478L5.04544 50.5534L-0.348601 55.7994L-0.5 55.9467V56.1579V73.9996V74.4996H0H270Z" fill="url(#paint0_linear)" stroke="#3F51B5"/>
</g>
<defs>
<linearGradient id="paint0_linear" x1="135" y1="14" x2="135" y2="74" gradientUnits="userSpaceOnUse">
<stop stop-color="#3F51B5" stop-opacity="0.5"/>
<stop offset="1" stop-color="#3F51B5" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
