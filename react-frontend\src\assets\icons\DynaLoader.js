import * as React from 'react';
const DynaLoader = (props) => (
	<svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} fill="currentColor" {...props}>
		<path
			fill="currentColor"
			d="M2.252 8.982c0-1.983-.005-3.97 0-5.954.01-1.155.824-2.098 1.937-2.25.152-.023.304-.023.46-.023 2.03 0 4.064.004 6.093-.005.672-.005 1.242.203 1.735.663.727.68 1.467 1.348 2.195 2.034.492.464.736 1.044.736 1.725V14.9c0 1.38-.966 2.342-2.347 2.347-2.82.004-5.641.004-8.462 0-1.38 0-2.342-.967-2.342-2.347-.005-1.979-.005-3.948-.005-5.918Zm5.876-2.209c.005.157.014.226.014.3v1.886c0 .621.235.934.695.93.46-.005.68-.313.68-.944v-1.91c0-.064.01-.128.015-.188.023-.014.041-.028.064-.037.166.18.318.368.492.539.295.29.718.285.985.009.262-.272.262-.686-.018-.971A119.183 119.183 0 0 0 9.352 4.68c-.345-.34-.704-.336-1.049.004-.561.553-1.118 1.114-1.675 1.675-.308.313-.322.746-.037 1.022.281.271.695.253 1.003-.05.17-.166.327-.337.534-.558Zm.677 6.788h2.618c.446 0 .76-.29.755-.69 0-.396-.318-.686-.764-.686a967.515 967.515 0 0 0-5.168 0c-.446 0-.764.29-.768.68-.005.406.313.691.773.696h2.554Z"
		/>
	</svg>
);
export default DynaLoader;
