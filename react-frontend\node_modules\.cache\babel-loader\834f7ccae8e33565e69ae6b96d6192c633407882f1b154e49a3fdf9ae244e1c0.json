{"ast": null, "code": "'use strict';\n\nmodule.exports = alphabetical;\n\n// Check if the given character code, or the character code at the first\n// character, is alphabetical.\nfunction alphabetical(character) {\n  var code = typeof character === 'string' ? character.charCodeAt(0) : character;\n  return code >= 97 && code <= 122 /* a-z */ || code >= 65 && code <= 90 /* A-Z */;\n}", "map": {"version": 3, "names": ["module", "exports", "alphabetical", "character", "code", "charCodeAt"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/is-alphabetical/index.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = alphabetical\n\n// Check if the given character code, or the character code at the first\n// character, is alphabetical.\nfunction alphabetical(character) {\n  var code = typeof character === 'string' ? character.charCodeAt(0) : character\n\n  return (\n    (code >= 97 && code <= 122) /* a-z */ ||\n    (code >= 65 && code <= 90) /* A-Z */\n  )\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,YAAY;;AAE7B;AACA;AACA,SAASA,YAAYA,CAACC,SAAS,EAAE;EAC/B,IAAIC,IAAI,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAGA,SAAS,CAACE,UAAU,CAAC,CAAC,CAAC,GAAGF,SAAS;EAE9E,OACGC,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,GAAG,CAAE,aAC3BA,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAG,CAAC;AAE/B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}