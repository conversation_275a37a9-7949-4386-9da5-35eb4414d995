{"ast": null, "code": "'use strict';\n\nvar schema = require('property-information/html');\nvar factory = require('./factory');\nvar html = factory(schema, 'div');\nhtml.displayName = 'html';\nmodule.exports = html;", "map": {"version": 3, "names": ["schema", "require", "factory", "html", "displayName", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/hastscript/html.js"], "sourcesContent": ["'use strict'\n\nvar schema = require('property-information/html')\nvar factory = require('./factory')\n\nvar html = factory(schema, 'div')\nhtml.displayName = 'html'\n\nmodule.exports = html\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACjD,IAAIC,OAAO,GAAGD,OAAO,CAAC,WAAW,CAAC;AAElC,IAAIE,IAAI,GAAGD,OAAO,CAACF,MAAM,EAAE,KAAK,CAAC;AACjCG,IAAI,CAACC,WAAW,GAAG,MAAM;AAEzBC,MAAM,CAACC,OAAO,GAAGH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}