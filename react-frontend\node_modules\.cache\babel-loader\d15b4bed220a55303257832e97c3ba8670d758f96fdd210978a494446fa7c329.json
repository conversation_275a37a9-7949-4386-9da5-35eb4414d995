{"ast": null, "code": "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n}\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        nextArgs[_key3] = arguments[_key3];\n      }\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\nfunction isEmpty(obj) {\n  return !Object.keys(obj).length;\n}\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\nfunction hasOwnProperty(object, property) {\n  return Object.prototype.hasOwnProperty.call(object, property);\n}\nfunction validateChanges(initial, changes) {\n  if (!isObject(changes)) errorHandler('changeType');\n  if (Object.keys(changes).some(function (field) {\n    return !hasOwnProperty(initial, field);\n  })) errorHandler('changeField');\n  return changes;\n}\nfunction validateSelector(selector) {\n  if (!isFunction(selector)) errorHandler('selectorType');\n}\nfunction validateHandler(handler) {\n  if (!(isFunction(handler) || isObject(handler))) errorHandler('handlerType');\n  if (isObject(handler) && Object.values(handler).some(function (_handler) {\n    return !isFunction(_handler);\n  })) errorHandler('handlersType');\n}\nfunction validateInitial(initial) {\n  if (!initial) errorHandler('initialIsRequired');\n  if (!isObject(initial)) errorHandler('initialType');\n  if (isEmpty(initial)) errorHandler('initialContent');\n}\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\nvar errorMessages = {\n  initialIsRequired: 'initial state is required',\n  initialType: 'initial state should be an object',\n  initialContent: 'initial state shouldn\\'t be an empty object',\n  handlerType: 'handler should be an object or a function',\n  handlersType: 'all handlers should be a functions',\n  selectorType: 'selector should be a function',\n  changeType: 'provided value of changes should be an object',\n  changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n  \"default\": 'an unknown error accured in `state-local` package'\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  changes: validateChanges,\n  selector: validateSelector,\n  handler: validateHandler,\n  initial: validateInitial\n};\nfunction create(initial) {\n  var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  validators.initial(initial);\n  validators.handler(handler);\n  var state = {\n    current: initial\n  };\n  var didUpdate = curry(didStateUpdate)(state, handler);\n  var update = curry(updateState)(state);\n  var validate = curry(validators.changes)(initial);\n  var getChanges = curry(extractChanges)(state);\n  function getState() {\n    var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function (state) {\n      return state;\n    };\n    validators.selector(selector);\n    return selector(state.current);\n  }\n  function setState(causedChanges) {\n    compose(didUpdate, update, validate, getChanges)(causedChanges);\n  }\n  return [getState, setState];\n}\nfunction extractChanges(state, causedChanges) {\n  return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\nfunction updateState(state, changes) {\n  state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n  return changes;\n}\nfunction didStateUpdate(state, handler, changes) {\n  isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function (field) {\n    var _handler$field;\n    return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n  });\n  return changes;\n}\nvar index = {\n  create: create\n};\nexport default index;", "map": {"version": 3, "names": ["_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread2", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "compose", "_len", "fns", "Array", "_key", "x", "reduceRight", "y", "f", "curry", "fn", "curried", "_this", "_len2", "args", "_key2", "_len3", "nextArgs", "_key3", "concat", "isObject", "toString", "call", "includes", "isEmpty", "isFunction", "hasOwnProperty", "property", "prototype", "validateChanges", "initial", "changes", "<PERSON><PERSON><PERSON><PERSON>", "some", "field", "validateSelector", "selector", "validate<PERSON><PERSON><PERSON>", "handler", "values", "_handler", "validateInitial", "throwError", "errorMessages", "type", "Error", "initialIsRequired", "initialType", "initialContent", "handlerType", "handlersType", "selectorType", "changeType", "changeField", "validators", "create", "undefined", "state", "current", "didUpdate", "didStateUpdate", "update", "updateState", "validate", "getChanges", "extractChanges", "getState", "setState", "<PERSON><PERSON><PERSON><PERSON>", "_handler$field", "index"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/state-local/lib/es/state-local.js"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n}\n\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        nextArgs[_key3] = arguments[_key3];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nfunction isEmpty(obj) {\n  return !Object.keys(obj).length;\n}\n\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\nfunction hasOwnProperty(object, property) {\n  return Object.prototype.hasOwnProperty.call(object, property);\n}\n\nfunction validateChanges(initial, changes) {\n  if (!isObject(changes)) errorHandler('changeType');\n  if (Object.keys(changes).some(function (field) {\n    return !hasOwnProperty(initial, field);\n  })) errorHandler('changeField');\n  return changes;\n}\n\nfunction validateSelector(selector) {\n  if (!isFunction(selector)) errorHandler('selectorType');\n}\n\nfunction validateHandler(handler) {\n  if (!(isFunction(handler) || isObject(handler))) errorHandler('handlerType');\n  if (isObject(handler) && Object.values(handler).some(function (_handler) {\n    return !isFunction(_handler);\n  })) errorHandler('handlersType');\n}\n\nfunction validateInitial(initial) {\n  if (!initial) errorHandler('initialIsRequired');\n  if (!isObject(initial)) errorHandler('initialType');\n  if (isEmpty(initial)) errorHandler('initialContent');\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  initialIsRequired: 'initial state is required',\n  initialType: 'initial state should be an object',\n  initialContent: 'initial state shouldn\\'t be an empty object',\n  handlerType: 'handler should be an object or a function',\n  handlersType: 'all handlers should be a functions',\n  selectorType: 'selector should be a function',\n  changeType: 'provided value of changes should be an object',\n  changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n  \"default\": 'an unknown error accured in `state-local` package'\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  changes: validateChanges,\n  selector: validateSelector,\n  handler: validateHandler,\n  initial: validateInitial\n};\n\nfunction create(initial) {\n  var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  validators.initial(initial);\n  validators.handler(handler);\n  var state = {\n    current: initial\n  };\n  var didUpdate = curry(didStateUpdate)(state, handler);\n  var update = curry(updateState)(state);\n  var validate = curry(validators.changes)(initial);\n  var getChanges = curry(extractChanges)(state);\n\n  function getState() {\n    var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function (state) {\n      return state;\n    };\n    validators.selector(selector);\n    return selector(state.current);\n  }\n\n  function setState(causedChanges) {\n    compose(didUpdate, update, validate, getChanges)(causedChanges);\n  }\n\n  return [getState, setState];\n}\n\nfunction extractChanges(state, causedChanges) {\n  return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\n\nfunction updateState(state, changes) {\n  state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n  return changes;\n}\n\nfunction didStateUpdate(state, handler, changes) {\n  isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function (field) {\n    var _handler$field;\n\n    return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n  });\n  return changes;\n}\n\nvar index = {\n  create: create\n};\n\nexport default index;\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EACxC,IAAID,GAAG,IAAID,GAAG,EAAE;IACdG,MAAM,CAACC,cAAc,CAACJ,GAAG,EAAEC,GAAG,EAAE;MAC9BC,KAAK,EAAEA,KAAK;MACZG,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLP,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAClB;EAEA,OAAOF,GAAG;AACZ;AAEA,SAASQ,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EACvC,IAAIC,IAAI,GAAGR,MAAM,CAACQ,IAAI,CAACF,MAAM,CAAC;EAE9B,IAAIN,MAAM,CAACS,qBAAqB,EAAE;IAChC,IAAIC,OAAO,GAAGV,MAAM,CAACS,qBAAqB,CAACH,MAAM,CAAC;IAClD,IAAIC,cAAc,EAAEG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAC1D,OAAOZ,MAAM,CAACa,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACV,UAAU;IAChE,CAAC,CAAC;IACFM,IAAI,CAACM,IAAI,CAACC,KAAK,CAACP,IAAI,EAAEE,OAAO,CAAC;EAChC;EAEA,OAAOF,IAAI;AACb;AAEA,SAASQ,cAAcA,CAACC,MAAM,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAErD,IAAIA,CAAC,GAAG,CAAC,EAAE;MACTb,OAAO,CAACL,MAAM,CAACqB,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUxB,GAAG,EAAE;QACnDF,eAAe,CAACqB,MAAM,EAAEnB,GAAG,EAAEuB,MAAM,CAACvB,GAAG,CAAC,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIE,MAAM,CAACuB,yBAAyB,EAAE;MAC3CvB,MAAM,CAACwB,gBAAgB,CAACP,MAAM,EAAEjB,MAAM,CAACuB,yBAAyB,CAACF,MAAM,CAAC,CAAC;IAC3E,CAAC,MAAM;MACLhB,OAAO,CAACL,MAAM,CAACqB,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUxB,GAAG,EAAE;QAC7CE,MAAM,CAACC,cAAc,CAACgB,MAAM,EAAEnB,GAAG,EAAEE,MAAM,CAACa,wBAAwB,CAACQ,MAAM,EAAEvB,GAAG,CAAC,CAAC;MAClF,CAAC,CAAC;IACJ;EACF;EAEA,OAAOmB,MAAM;AACf;AAEA,SAASQ,OAAOA,CAAA,EAAG;EACjB,KAAK,IAAIC,IAAI,GAAGP,SAAS,CAACC,MAAM,EAAEO,GAAG,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;IACtFF,GAAG,CAACE,IAAI,CAAC,GAAGV,SAAS,CAACU,IAAI,CAAC;EAC7B;EAEA,OAAO,UAAUC,CAAC,EAAE;IAClB,OAAOH,GAAG,CAACI,WAAW,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACrC,OAAOA,CAAC,CAACD,CAAC,CAAC;IACb,CAAC,EAAEF,CAAC,CAAC;EACP,CAAC;AACH;AAEA,SAASI,KAAKA,CAACC,EAAE,EAAE;EACjB,OAAO,SAASC,OAAOA,CAAA,EAAG;IACxB,IAAIC,KAAK,GAAG,IAAI;IAEhB,KAAK,IAAIC,KAAK,GAAGnB,SAAS,CAACC,MAAM,EAAEmB,IAAI,GAAG,IAAIX,KAAK,CAACU,KAAK,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;MAC7FD,IAAI,CAACC,KAAK,CAAC,GAAGrB,SAAS,CAACqB,KAAK,CAAC;IAChC;IAEA,OAAOD,IAAI,CAACnB,MAAM,IAAIe,EAAE,CAACf,MAAM,GAAGe,EAAE,CAACpB,KAAK,CAAC,IAAI,EAAEwB,IAAI,CAAC,GAAG,YAAY;MACnE,KAAK,IAAIE,KAAK,GAAGtB,SAAS,CAACC,MAAM,EAAEsB,QAAQ,GAAG,IAAId,KAAK,CAACa,KAAK,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;QACjGD,QAAQ,CAACC,KAAK,CAAC,GAAGxB,SAAS,CAACwB,KAAK,CAAC;MACpC;MAEA,OAAOP,OAAO,CAACrB,KAAK,CAACsB,KAAK,EAAE,EAAE,CAACO,MAAM,CAACL,IAAI,EAAEG,QAAQ,CAAC,CAAC;IACxD,CAAC;EACH,CAAC;AACH;AAEA,SAASG,QAAQA,CAAC9C,KAAK,EAAE;EACvB,OAAO,CAAC,CAAC,CAAC+C,QAAQ,CAACC,IAAI,CAAChD,KAAK,CAAC,CAACiD,QAAQ,CAAC,QAAQ,CAAC;AACnD;AAEA,SAASC,OAAOA,CAACpD,GAAG,EAAE;EACpB,OAAO,CAACG,MAAM,CAACQ,IAAI,CAACX,GAAG,CAAC,CAACuB,MAAM;AACjC;AAEA,SAAS8B,UAAUA,CAACnD,KAAK,EAAE;EACzB,OAAO,OAAOA,KAAK,KAAK,UAAU;AACpC;AAEA,SAASoD,cAAcA,CAAC7C,MAAM,EAAE8C,QAAQ,EAAE;EACxC,OAAOpD,MAAM,CAACqD,SAAS,CAACF,cAAc,CAACJ,IAAI,CAACzC,MAAM,EAAE8C,QAAQ,CAAC;AAC/D;AAEA,SAASE,eAAeA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACzC,IAAI,CAACX,QAAQ,CAACW,OAAO,CAAC,EAAEC,YAAY,CAAC,YAAY,CAAC;EAClD,IAAIzD,MAAM,CAACQ,IAAI,CAACgD,OAAO,CAAC,CAACE,IAAI,CAAC,UAAUC,KAAK,EAAE;IAC7C,OAAO,CAACR,cAAc,CAACI,OAAO,EAAEI,KAAK,CAAC;EACxC,CAAC,CAAC,EAAEF,YAAY,CAAC,aAAa,CAAC;EAC/B,OAAOD,OAAO;AAChB;AAEA,SAASI,gBAAgBA,CAACC,QAAQ,EAAE;EAClC,IAAI,CAACX,UAAU,CAACW,QAAQ,CAAC,EAAEJ,YAAY,CAAC,cAAc,CAAC;AACzD;AAEA,SAASK,eAAeA,CAACC,OAAO,EAAE;EAChC,IAAI,EAAEb,UAAU,CAACa,OAAO,CAAC,IAAIlB,QAAQ,CAACkB,OAAO,CAAC,CAAC,EAAEN,YAAY,CAAC,aAAa,CAAC;EAC5E,IAAIZ,QAAQ,CAACkB,OAAO,CAAC,IAAI/D,MAAM,CAACgE,MAAM,CAACD,OAAO,CAAC,CAACL,IAAI,CAAC,UAAUO,QAAQ,EAAE;IACvE,OAAO,CAACf,UAAU,CAACe,QAAQ,CAAC;EAC9B,CAAC,CAAC,EAAER,YAAY,CAAC,cAAc,CAAC;AAClC;AAEA,SAASS,eAAeA,CAACX,OAAO,EAAE;EAChC,IAAI,CAACA,OAAO,EAAEE,YAAY,CAAC,mBAAmB,CAAC;EAC/C,IAAI,CAACZ,QAAQ,CAACU,OAAO,CAAC,EAAEE,YAAY,CAAC,aAAa,CAAC;EACnD,IAAIR,OAAO,CAACM,OAAO,CAAC,EAAEE,YAAY,CAAC,gBAAgB,CAAC;AACtD;AAEA,SAASU,UAAUA,CAACC,aAAa,EAAEC,IAAI,EAAE;EACvC,MAAM,IAAIC,KAAK,CAACF,aAAa,CAACC,IAAI,CAAC,IAAID,aAAa,CAAC,SAAS,CAAC,CAAC;AAClE;AAEA,IAAIA,aAAa,GAAG;EAClBG,iBAAiB,EAAE,2BAA2B;EAC9CC,WAAW,EAAE,mCAAmC;EAChDC,cAAc,EAAE,6CAA6C;EAC7DC,WAAW,EAAE,2CAA2C;EACxDC,YAAY,EAAE,oCAAoC;EAClDC,YAAY,EAAE,+BAA+B;EAC7CC,UAAU,EAAE,+CAA+C;EAC3DC,WAAW,EAAE,gGAAgG;EAC7G,SAAS,EAAE;AACb,CAAC;AACD,IAAIrB,YAAY,GAAGvB,KAAK,CAACiC,UAAU,CAAC,CAACC,aAAa,CAAC;AACnD,IAAIW,UAAU,GAAG;EACfvB,OAAO,EAAEF,eAAe;EACxBO,QAAQ,EAAED,gBAAgB;EAC1BG,OAAO,EAAED,eAAe;EACxBP,OAAO,EAAEW;AACX,CAAC;AAED,SAASc,MAAMA,CAACzB,OAAO,EAAE;EACvB,IAAIQ,OAAO,GAAG5C,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK8D,SAAS,GAAG9D,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF4D,UAAU,CAACxB,OAAO,CAACA,OAAO,CAAC;EAC3BwB,UAAU,CAAChB,OAAO,CAACA,OAAO,CAAC;EAC3B,IAAImB,KAAK,GAAG;IACVC,OAAO,EAAE5B;EACX,CAAC;EACD,IAAI6B,SAAS,GAAGlD,KAAK,CAACmD,cAAc,CAAC,CAACH,KAAK,EAAEnB,OAAO,CAAC;EACrD,IAAIuB,MAAM,GAAGpD,KAAK,CAACqD,WAAW,CAAC,CAACL,KAAK,CAAC;EACtC,IAAIM,QAAQ,GAAGtD,KAAK,CAAC6C,UAAU,CAACvB,OAAO,CAAC,CAACD,OAAO,CAAC;EACjD,IAAIkC,UAAU,GAAGvD,KAAK,CAACwD,cAAc,CAAC,CAACR,KAAK,CAAC;EAE7C,SAASS,QAAQA,CAAA,EAAG;IAClB,IAAI9B,QAAQ,GAAG1C,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK8D,SAAS,GAAG9D,SAAS,CAAC,CAAC,CAAC,GAAG,UAAU+D,KAAK,EAAE;MAClG,OAAOA,KAAK;IACd,CAAC;IACDH,UAAU,CAAClB,QAAQ,CAACA,QAAQ,CAAC;IAC7B,OAAOA,QAAQ,CAACqB,KAAK,CAACC,OAAO,CAAC;EAChC;EAEA,SAASS,QAAQA,CAACC,aAAa,EAAE;IAC/BpE,OAAO,CAAC2D,SAAS,EAAEE,MAAM,EAAEE,QAAQ,EAAEC,UAAU,CAAC,CAACI,aAAa,CAAC;EACjE;EAEA,OAAO,CAACF,QAAQ,EAAEC,QAAQ,CAAC;AAC7B;AAEA,SAASF,cAAcA,CAACR,KAAK,EAAEW,aAAa,EAAE;EAC5C,OAAO3C,UAAU,CAAC2C,aAAa,CAAC,GAAGA,aAAa,CAACX,KAAK,CAACC,OAAO,CAAC,GAAGU,aAAa;AACjF;AAEA,SAASN,WAAWA,CAACL,KAAK,EAAE1B,OAAO,EAAE;EACnC0B,KAAK,CAACC,OAAO,GAAGnE,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkE,KAAK,CAACC,OAAO,CAAC,EAAE3B,OAAO,CAAC;EAC1E,OAAOA,OAAO;AAChB;AAEA,SAAS6B,cAAcA,CAACH,KAAK,EAAEnB,OAAO,EAAEP,OAAO,EAAE;EAC/CN,UAAU,CAACa,OAAO,CAAC,GAAGA,OAAO,CAACmB,KAAK,CAACC,OAAO,CAAC,GAAGnF,MAAM,CAACQ,IAAI,CAACgD,OAAO,CAAC,CAAClC,OAAO,CAAC,UAAUqC,KAAK,EAAE;IAC3F,IAAImC,cAAc;IAElB,OAAO,CAACA,cAAc,GAAG/B,OAAO,CAACJ,KAAK,CAAC,MAAM,IAAI,IAAImC,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC/C,IAAI,CAACgB,OAAO,EAAEmB,KAAK,CAACC,OAAO,CAACxB,KAAK,CAAC,CAAC;EAC9I,CAAC,CAAC;EACF,OAAOH,OAAO;AAChB;AAEA,IAAIuC,KAAK,GAAG;EACVf,MAAM,EAAEA;AACV,CAAC;AAED,eAAee,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}