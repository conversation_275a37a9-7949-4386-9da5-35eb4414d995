{"ast": null, "code": "'use strict';\n\nvar legacy = require('character-entities-legacy');\nvar invalid = require('character-reference-invalid');\nvar decimal = require('is-decimal');\nvar hexadecimal = require('is-hexadecimal');\nvar alphanumerical = require('is-alphanumerical');\nvar decodeEntity = require('./decode-entity');\nmodule.exports = parseEntities;\nvar own = {}.hasOwnProperty;\nvar fromCharCode = String.fromCharCode;\nvar noop = Function.prototype;\n\n// Default settings.\nvar defaults = {\n  warning: null,\n  reference: null,\n  text: null,\n  warningContext: null,\n  referenceContext: null,\n  textContext: null,\n  position: {},\n  additional: null,\n  attribute: false,\n  nonTerminated: true\n};\n\n// Characters.\nvar tab = 9; // '\\t'\nvar lineFeed = 10; // '\\n'\nvar formFeed = 12; // '\\f'\nvar space = 32; // ' '\nvar ampersand = 38; // '&'\nvar semicolon = 59; // ';'\nvar lessThan = 60; // '<'\nvar equalsTo = 61; // '='\nvar numberSign = 35; // '#'\nvar uppercaseX = 88; // 'X'\nvar lowercaseX = 120; // 'x'\nvar replacementCharacter = 65533; // '�'\n\n// Reference types.\nvar name = 'named';\nvar hexa = 'hexadecimal';\nvar deci = 'decimal';\n\n// Map of bases.\nvar bases = {};\nbases[hexa] = 16;\nbases[deci] = 10;\n\n// Map of types to tests.\n// Each type of character reference accepts different characters.\n// This test is used to detect whether a reference has ended (as the semicolon\n// is not strictly needed).\nvar tests = {};\ntests[name] = alphanumerical;\ntests[deci] = decimal;\ntests[hexa] = hexadecimal;\n\n// Warning types.\nvar namedNotTerminated = 1;\nvar numericNotTerminated = 2;\nvar namedEmpty = 3;\nvar numericEmpty = 4;\nvar namedUnknown = 5;\nvar numericDisallowed = 6;\nvar numericProhibited = 7;\n\n// Warning messages.\nvar messages = {};\nmessages[namedNotTerminated] = 'Named character references must be terminated by a semicolon';\nmessages[numericNotTerminated] = 'Numeric character references must be terminated by a semicolon';\nmessages[namedEmpty] = 'Named character references cannot be empty';\nmessages[numericEmpty] = 'Numeric character references cannot be empty';\nmessages[namedUnknown] = 'Named character references must be known';\nmessages[numericDisallowed] = 'Numeric character references cannot be disallowed';\nmessages[numericProhibited] = 'Numeric character references cannot be outside the permissible Unicode range';\n\n// Wrap to ensure clean parameters are given to `parse`.\nfunction parseEntities(value, options) {\n  var settings = {};\n  var option;\n  var key;\n  if (!options) {\n    options = {};\n  }\n  for (key in defaults) {\n    option = options[key];\n    settings[key] = option === null || option === undefined ? defaults[key] : option;\n  }\n  if (settings.position.indent || settings.position.start) {\n    settings.indent = settings.position.indent || [];\n    settings.position = settings.position.start;\n  }\n  return parse(value, settings);\n}\n\n// Parse entities.\n// eslint-disable-next-line complexity\nfunction parse(value, settings) {\n  var additional = settings.additional;\n  var nonTerminated = settings.nonTerminated;\n  var handleText = settings.text;\n  var handleReference = settings.reference;\n  var handleWarning = settings.warning;\n  var textContext = settings.textContext;\n  var referenceContext = settings.referenceContext;\n  var warningContext = settings.warningContext;\n  var pos = settings.position;\n  var indent = settings.indent || [];\n  var length = value.length;\n  var index = 0;\n  var lines = -1;\n  var column = pos.column || 1;\n  var line = pos.line || 1;\n  var queue = '';\n  var result = [];\n  var entityCharacters;\n  var namedEntity;\n  var terminated;\n  var characters;\n  var character;\n  var reference;\n  var following;\n  var warning;\n  var reason;\n  var output;\n  var entity;\n  var begin;\n  var start;\n  var type;\n  var test;\n  var prev;\n  var next;\n  var diff;\n  var end;\n  if (typeof additional === 'string') {\n    additional = additional.charCodeAt(0);\n  }\n\n  // Cache the current point.\n  prev = now();\n\n  // Wrap `handleWarning`.\n  warning = handleWarning ? parseError : noop;\n\n  // Ensure the algorithm walks over the first character and the end\n  // (inclusive).\n  index--;\n  length++;\n  while (++index < length) {\n    // If the previous character was a newline.\n    if (character === lineFeed) {\n      column = indent[lines] || 1;\n    }\n    character = value.charCodeAt(index);\n    if (character === ampersand) {\n      following = value.charCodeAt(index + 1);\n\n      // The behaviour depends on the identity of the next character.\n      if (following === tab || following === lineFeed || following === formFeed || following === space || following === ampersand || following === lessThan || following !== following || additional && following === additional) {\n        // Not a character reference.\n        // No characters are consumed, and nothing is returned.\n        // This is not an error, either.\n        queue += fromCharCode(character);\n        column++;\n        continue;\n      }\n      start = index + 1;\n      begin = start;\n      end = start;\n      if (following === numberSign) {\n        // Numerical entity.\n        end = ++begin;\n\n        // The behaviour further depends on the next character.\n        following = value.charCodeAt(end);\n        if (following === uppercaseX || following === lowercaseX) {\n          // ASCII hex digits.\n          type = hexa;\n          end = ++begin;\n        } else {\n          // ASCII digits.\n          type = deci;\n        }\n      } else {\n        // Named entity.\n        type = name;\n      }\n      entityCharacters = '';\n      entity = '';\n      characters = '';\n      test = tests[type];\n      end--;\n      while (++end < length) {\n        following = value.charCodeAt(end);\n        if (!test(following)) {\n          break;\n        }\n        characters += fromCharCode(following);\n\n        // Check if we can match a legacy named reference.\n        // If so, we cache that as the last viable named reference.\n        // This ensures we do not need to walk backwards later.\n        if (type === name && own.call(legacy, characters)) {\n          entityCharacters = characters;\n          entity = legacy[characters];\n        }\n      }\n      terminated = value.charCodeAt(end) === semicolon;\n      if (terminated) {\n        end++;\n        namedEntity = type === name ? decodeEntity(characters) : false;\n        if (namedEntity) {\n          entityCharacters = characters;\n          entity = namedEntity;\n        }\n      }\n      diff = 1 + end - start;\n      if (!terminated && !nonTerminated) {\n        // Empty.\n      } else if (!characters) {\n        // An empty (possible) entity is valid, unless it’s numeric (thus an\n        // ampersand followed by an octothorp).\n        if (type !== name) {\n          warning(numericEmpty, diff);\n        }\n      } else if (type === name) {\n        // An ampersand followed by anything unknown, and not terminated, is\n        // invalid.\n        if (terminated && !entity) {\n          warning(namedUnknown, 1);\n        } else {\n          // If theres something after an entity name which is not known, cap\n          // the reference.\n          if (entityCharacters !== characters) {\n            end = begin + entityCharacters.length;\n            diff = 1 + end - begin;\n            terminated = false;\n          }\n\n          // If the reference is not terminated, warn.\n          if (!terminated) {\n            reason = entityCharacters ? namedNotTerminated : namedEmpty;\n            if (settings.attribute) {\n              following = value.charCodeAt(end);\n              if (following === equalsTo) {\n                warning(reason, diff);\n                entity = null;\n              } else if (alphanumerical(following)) {\n                entity = null;\n              } else {\n                warning(reason, diff);\n              }\n            } else {\n              warning(reason, diff);\n            }\n          }\n        }\n        reference = entity;\n      } else {\n        if (!terminated) {\n          // All non-terminated numeric entities are not rendered, and trigger a\n          // warning.\n          warning(numericNotTerminated, diff);\n        }\n\n        // When terminated and number, parse as either hexadecimal or decimal.\n        reference = parseInt(characters, bases[type]);\n\n        // Trigger a warning when the parsed number is prohibited, and replace\n        // with replacement character.\n        if (prohibited(reference)) {\n          warning(numericProhibited, diff);\n          reference = fromCharCode(replacementCharacter);\n        } else if (reference in invalid) {\n          // Trigger a warning when the parsed number is disallowed, and replace\n          // by an alternative.\n          warning(numericDisallowed, diff);\n          reference = invalid[reference];\n        } else {\n          // Parse the number.\n          output = '';\n\n          // Trigger a warning when the parsed number should not be used.\n          if (disallowed(reference)) {\n            warning(numericDisallowed, diff);\n          }\n\n          // Stringify the number.\n          if (reference > 0xffff) {\n            reference -= 0x10000;\n            output += fromCharCode(reference >>> (10 & 0x3ff) | 0xd800);\n            reference = 0xdc00 | reference & 0x3ff;\n          }\n          reference = output + fromCharCode(reference);\n        }\n      }\n\n      // Found it!\n      // First eat the queued characters as normal text, then eat an entity.\n      if (reference) {\n        flush();\n        prev = now();\n        index = end - 1;\n        column += end - start + 1;\n        result.push(reference);\n        next = now();\n        next.offset++;\n        if (handleReference) {\n          handleReference.call(referenceContext, reference, {\n            start: prev,\n            end: next\n          }, value.slice(start - 1, end));\n        }\n        prev = next;\n      } else {\n        // If we could not find a reference, queue the checked characters (as\n        // normal characters), and move the pointer to their end.\n        // This is possible because we can be certain neither newlines nor\n        // ampersands are included.\n        characters = value.slice(start - 1, end);\n        queue += characters;\n        column += characters.length;\n        index = end - 1;\n      }\n    } else {\n      // Handle anything other than an ampersand, including newlines and EOF.\n      if (character === 10 // Line feed\n      ) {\n        line++;\n        lines++;\n        column = 0;\n      }\n      if (character === character) {\n        queue += fromCharCode(character);\n        column++;\n      } else {\n        flush();\n      }\n    }\n  }\n\n  // Return the reduced nodes.\n  return result.join('');\n\n  // Get current position.\n  function now() {\n    return {\n      line: line,\n      column: column,\n      offset: index + (pos.offset || 0)\n    };\n  }\n\n  // “Throw” a parse-error: a warning.\n  function parseError(code, offset) {\n    var position = now();\n    position.column += offset;\n    position.offset += offset;\n    handleWarning.call(warningContext, messages[code], position, code);\n  }\n\n  // Flush `queue` (normal text).\n  // Macro invoked before each entity and at the end of `value`.\n  // Does nothing when `queue` is empty.\n  function flush() {\n    if (queue) {\n      result.push(queue);\n      if (handleText) {\n        handleText.call(textContext, queue, {\n          start: prev,\n          end: now()\n        });\n      }\n      queue = '';\n    }\n  }\n}\n\n// Check if `character` is outside the permissible unicode range.\nfunction prohibited(code) {\n  return code >= 0xd800 && code <= 0xdfff || code > 0x10ffff;\n}\n\n// Check if `character` is disallowed.\nfunction disallowed(code) {\n  return code >= 0x0001 && code <= 0x0008 || code === 0x000b || code >= 0x000d && code <= 0x001f || code >= 0x007f && code <= 0x009f || code >= 0xfdd0 && code <= 0xfdef || (code & 0xffff) === 0xffff || (code & 0xffff) === 0xfffe;\n}", "map": {"version": 3, "names": ["legacy", "require", "invalid", "decimal", "hexadecimal", "alphanumerical", "decodeEntity", "module", "exports", "parseEntities", "own", "hasOwnProperty", "fromCharCode", "String", "noop", "Function", "prototype", "defaults", "warning", "reference", "text", "warningContext", "referenceContext", "textContext", "position", "additional", "attribute", "nonTerminated", "tab", "lineFeed", "formFeed", "space", "ampersand", "semicolon", "lessThan", "equalsTo", "numberSign", "uppercaseX", "lowercaseX", "replacementCharacter", "name", "hexa", "deci", "bases", "tests", "namedNotTerminated", "numericNotTerminated", "namedEmpty", "numericEmpty", "namedUnknown", "numericDisallowed", "numericProhibited", "messages", "value", "options", "settings", "option", "key", "undefined", "indent", "start", "parse", "handleText", "handleReference", "handleWarning", "pos", "length", "index", "lines", "column", "line", "queue", "result", "entityCharacters", "namedEntity", "terminated", "characters", "character", "following", "reason", "output", "entity", "begin", "type", "test", "prev", "next", "diff", "end", "charCodeAt", "now", "parseError", "call", "parseInt", "prohibited", "disallowed", "flush", "push", "offset", "slice", "join", "code"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/parse-entities/index.js"], "sourcesContent": ["'use strict'\n\nvar legacy = require('character-entities-legacy')\nvar invalid = require('character-reference-invalid')\nvar decimal = require('is-decimal')\nvar hexadecimal = require('is-hexadecimal')\nvar alphanumerical = require('is-alphanumerical')\nvar decodeEntity = require('./decode-entity')\n\nmodule.exports = parseEntities\n\nvar own = {}.hasOwnProperty\nvar fromCharCode = String.fromCharCode\nvar noop = Function.prototype\n\n// Default settings.\nvar defaults = {\n  warning: null,\n  reference: null,\n  text: null,\n  warningContext: null,\n  referenceContext: null,\n  textContext: null,\n  position: {},\n  additional: null,\n  attribute: false,\n  nonTerminated: true\n}\n\n// Characters.\nvar tab = 9 // '\\t'\nvar lineFeed = 10 // '\\n'\nvar formFeed = 12 // '\\f'\nvar space = 32 // ' '\nvar ampersand = 38 // '&'\nvar semicolon = 59 // ';'\nvar lessThan = 60 // '<'\nvar equalsTo = 61 // '='\nvar numberSign = 35 // '#'\nvar uppercaseX = 88 // 'X'\nvar lowercaseX = 120 // 'x'\nvar replacementCharacter = 65533 // '�'\n\n// Reference types.\nvar name = 'named'\nvar hexa = 'hexadecimal'\nvar deci = 'decimal'\n\n// Map of bases.\nvar bases = {}\n\nbases[hexa] = 16\nbases[deci] = 10\n\n// Map of types to tests.\n// Each type of character reference accepts different characters.\n// This test is used to detect whether a reference has ended (as the semicolon\n// is not strictly needed).\nvar tests = {}\n\ntests[name] = alphanumerical\ntests[deci] = decimal\ntests[hexa] = hexadecimal\n\n// Warning types.\nvar namedNotTerminated = 1\nvar numericNotTerminated = 2\nvar namedEmpty = 3\nvar numericEmpty = 4\nvar namedUnknown = 5\nvar numericDisallowed = 6\nvar numericProhibited = 7\n\n// Warning messages.\nvar messages = {}\n\nmessages[namedNotTerminated] =\n  'Named character references must be terminated by a semicolon'\nmessages[numericNotTerminated] =\n  'Numeric character references must be terminated by a semicolon'\nmessages[namedEmpty] = 'Named character references cannot be empty'\nmessages[numericEmpty] = 'Numeric character references cannot be empty'\nmessages[namedUnknown] = 'Named character references must be known'\nmessages[numericDisallowed] =\n  'Numeric character references cannot be disallowed'\nmessages[numericProhibited] =\n  'Numeric character references cannot be outside the permissible Unicode range'\n\n// Wrap to ensure clean parameters are given to `parse`.\nfunction parseEntities(value, options) {\n  var settings = {}\n  var option\n  var key\n\n  if (!options) {\n    options = {}\n  }\n\n  for (key in defaults) {\n    option = options[key]\n    settings[key] =\n      option === null || option === undefined ? defaults[key] : option\n  }\n\n  if (settings.position.indent || settings.position.start) {\n    settings.indent = settings.position.indent || []\n    settings.position = settings.position.start\n  }\n\n  return parse(value, settings)\n}\n\n// Parse entities.\n// eslint-disable-next-line complexity\nfunction parse(value, settings) {\n  var additional = settings.additional\n  var nonTerminated = settings.nonTerminated\n  var handleText = settings.text\n  var handleReference = settings.reference\n  var handleWarning = settings.warning\n  var textContext = settings.textContext\n  var referenceContext = settings.referenceContext\n  var warningContext = settings.warningContext\n  var pos = settings.position\n  var indent = settings.indent || []\n  var length = value.length\n  var index = 0\n  var lines = -1\n  var column = pos.column || 1\n  var line = pos.line || 1\n  var queue = ''\n  var result = []\n  var entityCharacters\n  var namedEntity\n  var terminated\n  var characters\n  var character\n  var reference\n  var following\n  var warning\n  var reason\n  var output\n  var entity\n  var begin\n  var start\n  var type\n  var test\n  var prev\n  var next\n  var diff\n  var end\n\n  if (typeof additional === 'string') {\n    additional = additional.charCodeAt(0)\n  }\n\n  // Cache the current point.\n  prev = now()\n\n  // Wrap `handleWarning`.\n  warning = handleWarning ? parseError : noop\n\n  // Ensure the algorithm walks over the first character and the end\n  // (inclusive).\n  index--\n  length++\n\n  while (++index < length) {\n    // If the previous character was a newline.\n    if (character === lineFeed) {\n      column = indent[lines] || 1\n    }\n\n    character = value.charCodeAt(index)\n\n    if (character === ampersand) {\n      following = value.charCodeAt(index + 1)\n\n      // The behaviour depends on the identity of the next character.\n      if (\n        following === tab ||\n        following === lineFeed ||\n        following === formFeed ||\n        following === space ||\n        following === ampersand ||\n        following === lessThan ||\n        following !== following ||\n        (additional && following === additional)\n      ) {\n        // Not a character reference.\n        // No characters are consumed, and nothing is returned.\n        // This is not an error, either.\n        queue += fromCharCode(character)\n        column++\n\n        continue\n      }\n\n      start = index + 1\n      begin = start\n      end = start\n\n      if (following === numberSign) {\n        // Numerical entity.\n        end = ++begin\n\n        // The behaviour further depends on the next character.\n        following = value.charCodeAt(end)\n\n        if (following === uppercaseX || following === lowercaseX) {\n          // ASCII hex digits.\n          type = hexa\n          end = ++begin\n        } else {\n          // ASCII digits.\n          type = deci\n        }\n      } else {\n        // Named entity.\n        type = name\n      }\n\n      entityCharacters = ''\n      entity = ''\n      characters = ''\n      test = tests[type]\n      end--\n\n      while (++end < length) {\n        following = value.charCodeAt(end)\n\n        if (!test(following)) {\n          break\n        }\n\n        characters += fromCharCode(following)\n\n        // Check if we can match a legacy named reference.\n        // If so, we cache that as the last viable named reference.\n        // This ensures we do not need to walk backwards later.\n        if (type === name && own.call(legacy, characters)) {\n          entityCharacters = characters\n          entity = legacy[characters]\n        }\n      }\n\n      terminated = value.charCodeAt(end) === semicolon\n\n      if (terminated) {\n        end++\n\n        namedEntity = type === name ? decodeEntity(characters) : false\n\n        if (namedEntity) {\n          entityCharacters = characters\n          entity = namedEntity\n        }\n      }\n\n      diff = 1 + end - start\n\n      if (!terminated && !nonTerminated) {\n        // Empty.\n      } else if (!characters) {\n        // An empty (possible) entity is valid, unless it’s numeric (thus an\n        // ampersand followed by an octothorp).\n        if (type !== name) {\n          warning(numericEmpty, diff)\n        }\n      } else if (type === name) {\n        // An ampersand followed by anything unknown, and not terminated, is\n        // invalid.\n        if (terminated && !entity) {\n          warning(namedUnknown, 1)\n        } else {\n          // If theres something after an entity name which is not known, cap\n          // the reference.\n          if (entityCharacters !== characters) {\n            end = begin + entityCharacters.length\n            diff = 1 + end - begin\n            terminated = false\n          }\n\n          // If the reference is not terminated, warn.\n          if (!terminated) {\n            reason = entityCharacters ? namedNotTerminated : namedEmpty\n\n            if (settings.attribute) {\n              following = value.charCodeAt(end)\n\n              if (following === equalsTo) {\n                warning(reason, diff)\n                entity = null\n              } else if (alphanumerical(following)) {\n                entity = null\n              } else {\n                warning(reason, diff)\n              }\n            } else {\n              warning(reason, diff)\n            }\n          }\n        }\n\n        reference = entity\n      } else {\n        if (!terminated) {\n          // All non-terminated numeric entities are not rendered, and trigger a\n          // warning.\n          warning(numericNotTerminated, diff)\n        }\n\n        // When terminated and number, parse as either hexadecimal or decimal.\n        reference = parseInt(characters, bases[type])\n\n        // Trigger a warning when the parsed number is prohibited, and replace\n        // with replacement character.\n        if (prohibited(reference)) {\n          warning(numericProhibited, diff)\n          reference = fromCharCode(replacementCharacter)\n        } else if (reference in invalid) {\n          // Trigger a warning when the parsed number is disallowed, and replace\n          // by an alternative.\n          warning(numericDisallowed, diff)\n          reference = invalid[reference]\n        } else {\n          // Parse the number.\n          output = ''\n\n          // Trigger a warning when the parsed number should not be used.\n          if (disallowed(reference)) {\n            warning(numericDisallowed, diff)\n          }\n\n          // Stringify the number.\n          if (reference > 0xffff) {\n            reference -= 0x10000\n            output += fromCharCode((reference >>> (10 & 0x3ff)) | 0xd800)\n            reference = 0xdc00 | (reference & 0x3ff)\n          }\n\n          reference = output + fromCharCode(reference)\n        }\n      }\n\n      // Found it!\n      // First eat the queued characters as normal text, then eat an entity.\n      if (reference) {\n        flush()\n\n        prev = now()\n        index = end - 1\n        column += end - start + 1\n        result.push(reference)\n        next = now()\n        next.offset++\n\n        if (handleReference) {\n          handleReference.call(\n            referenceContext,\n            reference,\n            {start: prev, end: next},\n            value.slice(start - 1, end)\n          )\n        }\n\n        prev = next\n      } else {\n        // If we could not find a reference, queue the checked characters (as\n        // normal characters), and move the pointer to their end.\n        // This is possible because we can be certain neither newlines nor\n        // ampersands are included.\n        characters = value.slice(start - 1, end)\n        queue += characters\n        column += characters.length\n        index = end - 1\n      }\n    } else {\n      // Handle anything other than an ampersand, including newlines and EOF.\n      if (\n        character === 10 // Line feed\n      ) {\n        line++\n        lines++\n        column = 0\n      }\n\n      if (character === character) {\n        queue += fromCharCode(character)\n        column++\n      } else {\n        flush()\n      }\n    }\n  }\n\n  // Return the reduced nodes.\n  return result.join('')\n\n  // Get current position.\n  function now() {\n    return {\n      line: line,\n      column: column,\n      offset: index + (pos.offset || 0)\n    }\n  }\n\n  // “Throw” a parse-error: a warning.\n  function parseError(code, offset) {\n    var position = now()\n\n    position.column += offset\n    position.offset += offset\n\n    handleWarning.call(warningContext, messages[code], position, code)\n  }\n\n  // Flush `queue` (normal text).\n  // Macro invoked before each entity and at the end of `value`.\n  // Does nothing when `queue` is empty.\n  function flush() {\n    if (queue) {\n      result.push(queue)\n\n      if (handleText) {\n        handleText.call(textContext, queue, {start: prev, end: now()})\n      }\n\n      queue = ''\n    }\n  }\n}\n\n// Check if `character` is outside the permissible unicode range.\nfunction prohibited(code) {\n  return (code >= 0xd800 && code <= 0xdfff) || code > 0x10ffff\n}\n\n// Check if `character` is disallowed.\nfunction disallowed(code) {\n  return (\n    (code >= 0x0001 && code <= 0x0008) ||\n    code === 0x000b ||\n    (code >= 0x000d && code <= 0x001f) ||\n    (code >= 0x007f && code <= 0x009f) ||\n    (code >= 0xfdd0 && code <= 0xfdef) ||\n    (code & 0xffff) === 0xffff ||\n    (code & 0xffff) === 0xfffe\n  )\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACjD,IAAIC,OAAO,GAAGD,OAAO,CAAC,6BAA6B,CAAC;AACpD,IAAIE,OAAO,GAAGF,OAAO,CAAC,YAAY,CAAC;AACnC,IAAIG,WAAW,GAAGH,OAAO,CAAC,gBAAgB,CAAC;AAC3C,IAAII,cAAc,GAAGJ,OAAO,CAAC,mBAAmB,CAAC;AACjD,IAAIK,YAAY,GAAGL,OAAO,CAAC,iBAAiB,CAAC;AAE7CM,MAAM,CAACC,OAAO,GAAGC,aAAa;AAE9B,IAAIC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;AAC3B,IAAIC,YAAY,GAAGC,MAAM,CAACD,YAAY;AACtC,IAAIE,IAAI,GAAGC,QAAQ,CAACC,SAAS;;AAE7B;AACA,IAAIC,QAAQ,GAAG;EACbC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE,IAAI;EACfC,IAAI,EAAE,IAAI;EACVC,cAAc,EAAE,IAAI;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,CAAC,CAAC;EACZC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,KAAK;EAChBC,aAAa,EAAE;AACjB,CAAC;;AAED;AACA,IAAIC,GAAG,GAAG,CAAC,EAAC;AACZ,IAAIC,QAAQ,GAAG,EAAE,EAAC;AAClB,IAAIC,QAAQ,GAAG,EAAE,EAAC;AAClB,IAAIC,KAAK,GAAG,EAAE,EAAC;AACf,IAAIC,SAAS,GAAG,EAAE,EAAC;AACnB,IAAIC,SAAS,GAAG,EAAE,EAAC;AACnB,IAAIC,QAAQ,GAAG,EAAE,EAAC;AAClB,IAAIC,QAAQ,GAAG,EAAE,EAAC;AAClB,IAAIC,UAAU,GAAG,EAAE,EAAC;AACpB,IAAIC,UAAU,GAAG,EAAE,EAAC;AACpB,IAAIC,UAAU,GAAG,GAAG,EAAC;AACrB,IAAIC,oBAAoB,GAAG,KAAK,EAAC;;AAEjC;AACA,IAAIC,IAAI,GAAG,OAAO;AAClB,IAAIC,IAAI,GAAG,aAAa;AACxB,IAAIC,IAAI,GAAG,SAAS;;AAEpB;AACA,IAAIC,KAAK,GAAG,CAAC,CAAC;AAEdA,KAAK,CAACF,IAAI,CAAC,GAAG,EAAE;AAChBE,KAAK,CAACD,IAAI,CAAC,GAAG,EAAE;;AAEhB;AACA;AACA;AACA;AACA,IAAIE,KAAK,GAAG,CAAC,CAAC;AAEdA,KAAK,CAACJ,IAAI,CAAC,GAAGnC,cAAc;AAC5BuC,KAAK,CAACF,IAAI,CAAC,GAAGvC,OAAO;AACrByC,KAAK,CAACH,IAAI,CAAC,GAAGrC,WAAW;;AAEzB;AACA,IAAIyC,kBAAkB,GAAG,CAAC;AAC1B,IAAIC,oBAAoB,GAAG,CAAC;AAC5B,IAAIC,UAAU,GAAG,CAAC;AAClB,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,iBAAiB,GAAG,CAAC;;AAEzB;AACA,IAAIC,QAAQ,GAAG,CAAC,CAAC;AAEjBA,QAAQ,CAACP,kBAAkB,CAAC,GAC1B,8DAA8D;AAChEO,QAAQ,CAACN,oBAAoB,CAAC,GAC5B,gEAAgE;AAClEM,QAAQ,CAACL,UAAU,CAAC,GAAG,4CAA4C;AACnEK,QAAQ,CAACJ,YAAY,CAAC,GAAG,8CAA8C;AACvEI,QAAQ,CAACH,YAAY,CAAC,GAAG,0CAA0C;AACnEG,QAAQ,CAACF,iBAAiB,CAAC,GACzB,mDAAmD;AACrDE,QAAQ,CAACD,iBAAiB,CAAC,GACzB,8EAA8E;;AAEhF;AACA,SAAS1C,aAAaA,CAAC4C,KAAK,EAAEC,OAAO,EAAE;EACrC,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIC,MAAM;EACV,IAAIC,GAAG;EAEP,IAAI,CAACH,OAAO,EAAE;IACZA,OAAO,GAAG,CAAC,CAAC;EACd;EAEA,KAAKG,GAAG,IAAIxC,QAAQ,EAAE;IACpBuC,MAAM,GAAGF,OAAO,CAACG,GAAG,CAAC;IACrBF,QAAQ,CAACE,GAAG,CAAC,GACXD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKE,SAAS,GAAGzC,QAAQ,CAACwC,GAAG,CAAC,GAAGD,MAAM;EACpE;EAEA,IAAID,QAAQ,CAAC/B,QAAQ,CAACmC,MAAM,IAAIJ,QAAQ,CAAC/B,QAAQ,CAACoC,KAAK,EAAE;IACvDL,QAAQ,CAACI,MAAM,GAAGJ,QAAQ,CAAC/B,QAAQ,CAACmC,MAAM,IAAI,EAAE;IAChDJ,QAAQ,CAAC/B,QAAQ,GAAG+B,QAAQ,CAAC/B,QAAQ,CAACoC,KAAK;EAC7C;EAEA,OAAOC,KAAK,CAACR,KAAK,EAAEE,QAAQ,CAAC;AAC/B;;AAEA;AACA;AACA,SAASM,KAAKA,CAACR,KAAK,EAAEE,QAAQ,EAAE;EAC9B,IAAI9B,UAAU,GAAG8B,QAAQ,CAAC9B,UAAU;EACpC,IAAIE,aAAa,GAAG4B,QAAQ,CAAC5B,aAAa;EAC1C,IAAImC,UAAU,GAAGP,QAAQ,CAACnC,IAAI;EAC9B,IAAI2C,eAAe,GAAGR,QAAQ,CAACpC,SAAS;EACxC,IAAI6C,aAAa,GAAGT,QAAQ,CAACrC,OAAO;EACpC,IAAIK,WAAW,GAAGgC,QAAQ,CAAChC,WAAW;EACtC,IAAID,gBAAgB,GAAGiC,QAAQ,CAACjC,gBAAgB;EAChD,IAAID,cAAc,GAAGkC,QAAQ,CAAClC,cAAc;EAC5C,IAAI4C,GAAG,GAAGV,QAAQ,CAAC/B,QAAQ;EAC3B,IAAImC,MAAM,GAAGJ,QAAQ,CAACI,MAAM,IAAI,EAAE;EAClC,IAAIO,MAAM,GAAGb,KAAK,CAACa,MAAM;EACzB,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,MAAM,GAAGJ,GAAG,CAACI,MAAM,IAAI,CAAC;EAC5B,IAAIC,IAAI,GAAGL,GAAG,CAACK,IAAI,IAAI,CAAC;EACxB,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,gBAAgB;EACpB,IAAIC,WAAW;EACf,IAAIC,UAAU;EACd,IAAIC,UAAU;EACd,IAAIC,SAAS;EACb,IAAI1D,SAAS;EACb,IAAI2D,SAAS;EACb,IAAI5D,OAAO;EACX,IAAI6D,MAAM;EACV,IAAIC,MAAM;EACV,IAAIC,MAAM;EACV,IAAIC,KAAK;EACT,IAAItB,KAAK;EACT,IAAIuB,IAAI;EACR,IAAIC,IAAI;EACR,IAAIC,IAAI;EACR,IAAIC,IAAI;EACR,IAAIC,IAAI;EACR,IAAIC,GAAG;EAEP,IAAI,OAAO/D,UAAU,KAAK,QAAQ,EAAE;IAClCA,UAAU,GAAGA,UAAU,CAACgE,UAAU,CAAC,CAAC,CAAC;EACvC;;EAEA;EACAJ,IAAI,GAAGK,GAAG,CAAC,CAAC;;EAEZ;EACAxE,OAAO,GAAG8C,aAAa,GAAG2B,UAAU,GAAG7E,IAAI;;EAE3C;EACA;EACAqD,KAAK,EAAE;EACPD,MAAM,EAAE;EAER,OAAO,EAAEC,KAAK,GAAGD,MAAM,EAAE;IACvB;IACA,IAAIW,SAAS,KAAKhD,QAAQ,EAAE;MAC1BwC,MAAM,GAAGV,MAAM,CAACS,KAAK,CAAC,IAAI,CAAC;IAC7B;IAEAS,SAAS,GAAGxB,KAAK,CAACoC,UAAU,CAACtB,KAAK,CAAC;IAEnC,IAAIU,SAAS,KAAK7C,SAAS,EAAE;MAC3B8C,SAAS,GAAGzB,KAAK,CAACoC,UAAU,CAACtB,KAAK,GAAG,CAAC,CAAC;;MAEvC;MACA,IACEW,SAAS,KAAKlD,GAAG,IACjBkD,SAAS,KAAKjD,QAAQ,IACtBiD,SAAS,KAAKhD,QAAQ,IACtBgD,SAAS,KAAK/C,KAAK,IACnB+C,SAAS,KAAK9C,SAAS,IACvB8C,SAAS,KAAK5C,QAAQ,IACtB4C,SAAS,KAAKA,SAAS,IACtBrD,UAAU,IAAIqD,SAAS,KAAKrD,UAAW,EACxC;QACA;QACA;QACA;QACA8C,KAAK,IAAI3D,YAAY,CAACiE,SAAS,CAAC;QAChCR,MAAM,EAAE;QAER;MACF;MAEAT,KAAK,GAAGO,KAAK,GAAG,CAAC;MACjBe,KAAK,GAAGtB,KAAK;MACb4B,GAAG,GAAG5B,KAAK;MAEX,IAAIkB,SAAS,KAAK1C,UAAU,EAAE;QAC5B;QACAoD,GAAG,GAAG,EAAEN,KAAK;;QAEb;QACAJ,SAAS,GAAGzB,KAAK,CAACoC,UAAU,CAACD,GAAG,CAAC;QAEjC,IAAIV,SAAS,KAAKzC,UAAU,IAAIyC,SAAS,KAAKxC,UAAU,EAAE;UACxD;UACA6C,IAAI,GAAG1C,IAAI;UACX+C,GAAG,GAAG,EAAEN,KAAK;QACf,CAAC,MAAM;UACL;UACAC,IAAI,GAAGzC,IAAI;QACb;MACF,CAAC,MAAM;QACL;QACAyC,IAAI,GAAG3C,IAAI;MACb;MAEAiC,gBAAgB,GAAG,EAAE;MACrBQ,MAAM,GAAG,EAAE;MACXL,UAAU,GAAG,EAAE;MACfQ,IAAI,GAAGxC,KAAK,CAACuC,IAAI,CAAC;MAClBK,GAAG,EAAE;MAEL,OAAO,EAAEA,GAAG,GAAGtB,MAAM,EAAE;QACrBY,SAAS,GAAGzB,KAAK,CAACoC,UAAU,CAACD,GAAG,CAAC;QAEjC,IAAI,CAACJ,IAAI,CAACN,SAAS,CAAC,EAAE;UACpB;QACF;QAEAF,UAAU,IAAIhE,YAAY,CAACkE,SAAS,CAAC;;QAErC;QACA;QACA;QACA,IAAIK,IAAI,KAAK3C,IAAI,IAAI9B,GAAG,CAACkF,IAAI,CAAC5F,MAAM,EAAE4E,UAAU,CAAC,EAAE;UACjDH,gBAAgB,GAAGG,UAAU;UAC7BK,MAAM,GAAGjF,MAAM,CAAC4E,UAAU,CAAC;QAC7B;MACF;MAEAD,UAAU,GAAGtB,KAAK,CAACoC,UAAU,CAACD,GAAG,CAAC,KAAKvD,SAAS;MAEhD,IAAI0C,UAAU,EAAE;QACda,GAAG,EAAE;QAELd,WAAW,GAAGS,IAAI,KAAK3C,IAAI,GAAGlC,YAAY,CAACsE,UAAU,CAAC,GAAG,KAAK;QAE9D,IAAIF,WAAW,EAAE;UACfD,gBAAgB,GAAGG,UAAU;UAC7BK,MAAM,GAAGP,WAAW;QACtB;MACF;MAEAa,IAAI,GAAG,CAAC,GAAGC,GAAG,GAAG5B,KAAK;MAEtB,IAAI,CAACe,UAAU,IAAI,CAAChD,aAAa,EAAE;QACjC;MAAA,CACD,MAAM,IAAI,CAACiD,UAAU,EAAE;QACtB;QACA;QACA,IAAIO,IAAI,KAAK3C,IAAI,EAAE;UACjBtB,OAAO,CAAC8B,YAAY,EAAEuC,IAAI,CAAC;QAC7B;MACF,CAAC,MAAM,IAAIJ,IAAI,KAAK3C,IAAI,EAAE;QACxB;QACA;QACA,IAAImC,UAAU,IAAI,CAACM,MAAM,EAAE;UACzB/D,OAAO,CAAC+B,YAAY,EAAE,CAAC,CAAC;QAC1B,CAAC,MAAM;UACL;UACA;UACA,IAAIwB,gBAAgB,KAAKG,UAAU,EAAE;YACnCY,GAAG,GAAGN,KAAK,GAAGT,gBAAgB,CAACP,MAAM;YACrCqB,IAAI,GAAG,CAAC,GAAGC,GAAG,GAAGN,KAAK;YACtBP,UAAU,GAAG,KAAK;UACpB;;UAEA;UACA,IAAI,CAACA,UAAU,EAAE;YACfI,MAAM,GAAGN,gBAAgB,GAAG5B,kBAAkB,GAAGE,UAAU;YAE3D,IAAIQ,QAAQ,CAAC7B,SAAS,EAAE;cACtBoD,SAAS,GAAGzB,KAAK,CAACoC,UAAU,CAACD,GAAG,CAAC;cAEjC,IAAIV,SAAS,KAAK3C,QAAQ,EAAE;gBAC1BjB,OAAO,CAAC6D,MAAM,EAAEQ,IAAI,CAAC;gBACrBN,MAAM,GAAG,IAAI;cACf,CAAC,MAAM,IAAI5E,cAAc,CAACyE,SAAS,CAAC,EAAE;gBACpCG,MAAM,GAAG,IAAI;cACf,CAAC,MAAM;gBACL/D,OAAO,CAAC6D,MAAM,EAAEQ,IAAI,CAAC;cACvB;YACF,CAAC,MAAM;cACLrE,OAAO,CAAC6D,MAAM,EAAEQ,IAAI,CAAC;YACvB;UACF;QACF;QAEApE,SAAS,GAAG8D,MAAM;MACpB,CAAC,MAAM;QACL,IAAI,CAACN,UAAU,EAAE;UACf;UACA;UACAzD,OAAO,CAAC4B,oBAAoB,EAAEyC,IAAI,CAAC;QACrC;;QAEA;QACApE,SAAS,GAAG0E,QAAQ,CAACjB,UAAU,EAAEjC,KAAK,CAACwC,IAAI,CAAC,CAAC;;QAE7C;QACA;QACA,IAAIW,UAAU,CAAC3E,SAAS,CAAC,EAAE;UACzBD,OAAO,CAACiC,iBAAiB,EAAEoC,IAAI,CAAC;UAChCpE,SAAS,GAAGP,YAAY,CAAC2B,oBAAoB,CAAC;QAChD,CAAC,MAAM,IAAIpB,SAAS,IAAIjB,OAAO,EAAE;UAC/B;UACA;UACAgB,OAAO,CAACgC,iBAAiB,EAAEqC,IAAI,CAAC;UAChCpE,SAAS,GAAGjB,OAAO,CAACiB,SAAS,CAAC;QAChC,CAAC,MAAM;UACL;UACA6D,MAAM,GAAG,EAAE;;UAEX;UACA,IAAIe,UAAU,CAAC5E,SAAS,CAAC,EAAE;YACzBD,OAAO,CAACgC,iBAAiB,EAAEqC,IAAI,CAAC;UAClC;;UAEA;UACA,IAAIpE,SAAS,GAAG,MAAM,EAAE;YACtBA,SAAS,IAAI,OAAO;YACpB6D,MAAM,IAAIpE,YAAY,CAAEO,SAAS,MAAM,EAAE,GAAG,KAAK,CAAC,GAAI,MAAM,CAAC;YAC7DA,SAAS,GAAG,MAAM,GAAIA,SAAS,GAAG,KAAM;UAC1C;UAEAA,SAAS,GAAG6D,MAAM,GAAGpE,YAAY,CAACO,SAAS,CAAC;QAC9C;MACF;;MAEA;MACA;MACA,IAAIA,SAAS,EAAE;QACb6E,KAAK,CAAC,CAAC;QAEPX,IAAI,GAAGK,GAAG,CAAC,CAAC;QACZvB,KAAK,GAAGqB,GAAG,GAAG,CAAC;QACfnB,MAAM,IAAImB,GAAG,GAAG5B,KAAK,GAAG,CAAC;QACzBY,MAAM,CAACyB,IAAI,CAAC9E,SAAS,CAAC;QACtBmE,IAAI,GAAGI,GAAG,CAAC,CAAC;QACZJ,IAAI,CAACY,MAAM,EAAE;QAEb,IAAInC,eAAe,EAAE;UACnBA,eAAe,CAAC6B,IAAI,CAClBtE,gBAAgB,EAChBH,SAAS,EACT;YAACyC,KAAK,EAAEyB,IAAI;YAAEG,GAAG,EAAEF;UAAI,CAAC,EACxBjC,KAAK,CAAC8C,KAAK,CAACvC,KAAK,GAAG,CAAC,EAAE4B,GAAG,CAC5B,CAAC;QACH;QAEAH,IAAI,GAAGC,IAAI;MACb,CAAC,MAAM;QACL;QACA;QACA;QACA;QACAV,UAAU,GAAGvB,KAAK,CAAC8C,KAAK,CAACvC,KAAK,GAAG,CAAC,EAAE4B,GAAG,CAAC;QACxCjB,KAAK,IAAIK,UAAU;QACnBP,MAAM,IAAIO,UAAU,CAACV,MAAM;QAC3BC,KAAK,GAAGqB,GAAG,GAAG,CAAC;MACjB;IACF,CAAC,MAAM;MACL;MACA,IACEX,SAAS,KAAK,EAAE,CAAC;MAAA,EACjB;QACAP,IAAI,EAAE;QACNF,KAAK,EAAE;QACPC,MAAM,GAAG,CAAC;MACZ;MAEA,IAAIQ,SAAS,KAAKA,SAAS,EAAE;QAC3BN,KAAK,IAAI3D,YAAY,CAACiE,SAAS,CAAC;QAChCR,MAAM,EAAE;MACV,CAAC,MAAM;QACL2B,KAAK,CAAC,CAAC;MACT;IACF;EACF;;EAEA;EACA,OAAOxB,MAAM,CAAC4B,IAAI,CAAC,EAAE,CAAC;;EAEtB;EACA,SAASV,GAAGA,CAAA,EAAG;IACb,OAAO;MACLpB,IAAI,EAAEA,IAAI;MACVD,MAAM,EAAEA,MAAM;MACd6B,MAAM,EAAE/B,KAAK,IAAIF,GAAG,CAACiC,MAAM,IAAI,CAAC;IAClC,CAAC;EACH;;EAEA;EACA,SAASP,UAAUA,CAACU,IAAI,EAAEH,MAAM,EAAE;IAChC,IAAI1E,QAAQ,GAAGkE,GAAG,CAAC,CAAC;IAEpBlE,QAAQ,CAAC6C,MAAM,IAAI6B,MAAM;IACzB1E,QAAQ,CAAC0E,MAAM,IAAIA,MAAM;IAEzBlC,aAAa,CAAC4B,IAAI,CAACvE,cAAc,EAAE+B,QAAQ,CAACiD,IAAI,CAAC,EAAE7E,QAAQ,EAAE6E,IAAI,CAAC;EACpE;;EAEA;EACA;EACA;EACA,SAASL,KAAKA,CAAA,EAAG;IACf,IAAIzB,KAAK,EAAE;MACTC,MAAM,CAACyB,IAAI,CAAC1B,KAAK,CAAC;MAElB,IAAIT,UAAU,EAAE;QACdA,UAAU,CAAC8B,IAAI,CAACrE,WAAW,EAAEgD,KAAK,EAAE;UAACX,KAAK,EAAEyB,IAAI;UAAEG,GAAG,EAAEE,GAAG,CAAC;QAAC,CAAC,CAAC;MAChE;MAEAnB,KAAK,GAAG,EAAE;IACZ;EACF;AACF;;AAEA;AACA,SAASuB,UAAUA,CAACO,IAAI,EAAE;EACxB,OAAQA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,IAAKA,IAAI,GAAG,QAAQ;AAC9D;;AAEA;AACA,SAASN,UAAUA,CAACM,IAAI,EAAE;EACxB,OACGA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,IACjCA,IAAI,KAAK,MAAM,IACdA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAO,IACjCA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAO,IACjCA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAO,IAClC,CAACA,IAAI,GAAG,MAAM,MAAM,MAAM,IAC1B,CAACA,IAAI,GAAG,MAAM,MAAM,MAAM;AAE9B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}