{"ast": null, "code": "'use strict';\n\nmodule.exports = normalize;\nfunction normalize(value) {\n  return value.toLowerCase();\n}", "map": {"version": 3, "names": ["module", "exports", "normalize", "value", "toLowerCase"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/property-information/normalize.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = normalize\n\nfunction normalize(value) {\n  return value.toLowerCase()\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,SAAS;AAE1B,SAASA,SAASA,CAACC,KAAK,EAAE;EACxB,OAAOA,KAAK,CAACC,WAAW,CAAC,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}