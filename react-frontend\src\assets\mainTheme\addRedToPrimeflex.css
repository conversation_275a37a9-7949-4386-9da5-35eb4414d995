.text-red-50 {
    color: var(--red-50) !important;
}
.text-red-100 {
    color: var(--red-100) !important;
}
.text-red-200 {
    color: var(--red-200) !important;
}
.text-red-300 {
    color: var(--red-300) !important;
}
.text-red-400 {
    color: var(--red-400) !important;
}
.text-red-500 {
    color: var(--red-500) !important;
}
.text-red-600 {
    color: var(--red-600) !important;
}
.text-red-700 {
    color: var(--red-700) !important;
}
.text-red-800 {
    color: var(--red-800) !important;
}
.text-red-900 {
    color: var(--red-900) !important;
}

.focus\:text-red-50:focus {
    color: var(--red-50) !important;
}
.focus\:text-red-100:focus {
    color: var(--red-100) !important;
}
.focus\:text-red-200:focus {
    color: var(--red-200) !important;
}
.focus\:text-red-300:focus {
    color: var(--red-300) !important;
}
.focus\:text-red-400:focus {
    color: var(--red-400) !important;
}
.focus\:text-red-500:focus {
    color: var(--red-500) !important;
}
.focus\:text-red-600:focus {
    color: var(--red-600) !important;
}
.focus\:text-red-700:focus {
    color: var(--red-700) !important;
}
.focus\:text-red-800:focus {
    color: var(--red-800) !important;
}
.focus\:text-red-900:focus {
    color: var(--red-900) !important;
}

.hover\:text-red-50:hover {
    color: var(--red-50) !important;
}
.hover\:text-red-100:hover {
    color: var(--red-100) !important;
}
.hover\:text-red-200:hover {
    color: var(--red-200) !important;
}
.hover\:text-red-300:hover {
    color: var(--red-300) !important;
}
.hover\:text-red-400:hover {
    color: var(--red-400) !important;
}
.hover\:text-red-500:hover {
    color: var(--red-500) !important;
}
.hover\:text-red-600:hover {
    color: var(--red-600) !important;
}
.hover\:text-red-700:hover {
    color: var(--red-700) !important;
}
.hover\:text-red-800:hover {
    color: var(--red-800) !important;
}
.hover\:text-red-900:hover {
    color: var(--red-900) !important;
}

.active\:text-red-50:active {
    color: var(--red-50) !important;
}
.active\:text-red-100:active {
    color: var(--red-100) !important;
}
.active\:text-red-200:active {
    color: var(--red-200) !important;
}
.active\:text-red-300:active {
    color: var(--red-300) !important;
}
.active\:text-red-400:active {
    color: var(--red-400) !important;
}
.active\:text-red-500:active {
    color: var(--red-500) !important;
}
.active\:text-red-600:active {
    color: var(--red-600) !important;
}
.active\:text-red-700:active {
    color: var(--red-700) !important;
}
.active\:text-red-800:active {
    color: var(--red-800) !important;
}
.active\:text-red-900:active {
    color: var(--red-900) !important;
}

.bg-red-50 {
    background-color: var(--red-50) !important;
}
.bg-red-100 {
    background-color: var(--red-100) !important;
}
.bg-red-200 {
    background-color: var(--red-200) !important;
}
.bg-red-300 {
    background-color: var(--red-300) !important;
}
.bg-red-400 {
    background-color: var(--red-400) !important;
}
.bg-red-500 {
    background-color: var(--red-500) !important;
}
.bg-red-600 {
    background-color: var(--red-600) !important;
}
.bg-red-700 {
    background-color: var(--red-700) !important;
}
.bg-red-800 {
    background-color: var(--red-800) !important;
}
.bg-red-900 {
    background-color: var(--red-900) !important;
}

.focus\:bg-red-50:focus {
    background-color: var(--red-50) !important;
}
.focus\:bg-red-100:focus {
    background-color: var(--red-100) !important;
}
.focus\:bg-red-200:focus {
    background-color: var(--red-200) !important;
}
.focus\:bg-red-300:focus {
    background-color: var(--red-300) !important;
}
.focus\:bg-red-400:focus {
    background-color: var(--red-400) !important;
}
.focus\:bg-red-500:focus {
    background-color: var(--red-500) !important;
}
.focus\:bg-red-600:focus {
    background-color: var(--red-600) !important;
}
.focus\:bg-red-700:focus {
    background-color: var(--red-700) !important;
}
.focus\:bg-red-800:focus {
    background-color: var(--red-800) !important;
}
.focus\:bg-red-900:focus {
    background-color: var(--red-900) !important;
}

.hover\:bg-red-50:hover {
    background-color: var(--red-50) !important;
}
.hover\:bg-red-100:hover {
    background-color: var(--red-100) !important;
}
.hover\:bg-red-200:hover {
    background-color: var(--red-200) !important;
}
.hover\:bg-red-300:hover {
    background-color: var(--red-300) !important;
}
.hover\:bg-red-400:hover {
    background-color: var(--red-400) !important;
}
.hover\:bg-red-500:hover {
    background-color: var(--red-500) !important;
}
.hover\:bg-red-600:hover {
    background-color: var(--red-600) !important;
}
.hover\:bg-red-700:hover {
    background-color: var(--red-700) !important;
}
.hover\:bg-red-800:hover {
    background-color: var(--red-800) !important;
}
.hover\:bg-red-900:hover {
    background-color: var(--red-900) !important;
}

.active\:bg-red-50:active {
    background-color: var(--red-50) !important;
}
.active\:bg-red-100:active {
    background-color: var(--red-100) !important;
}
.active\:bg-red-200:active {
    background-color: var(--red-200) !important;
}
.active\:bg-red-300:active {
    background-color: var(--red-300) !important;
}
.active\:bg-red-400:active {
    background-color: var(--red-400) !important;
}
.active\:bg-red-500:active {
    background-color: var(--red-500) !important;
}
.active\:bg-red-600:active {
    background-color: var(--red-600) !important;
}
.active\:bg-red-700:active {
    background-color: var(--red-700) !important;
}
.active\:bg-red-800:active {
    background-color: var(--red-800) !important;
}
.active\:bg-red-900:active {
    background-color: var(--red-900) !important;
}

.border-red-50 {
    border-color: var(--red-50) !important;
}
.border-red-100 {
    border-color: var(--red-100) !important;
}
.border-red-200 {
    border-color: var(--red-200) !important;
}
.border-red-300 {
    border-color: var(--red-300) !important;
}
.border-red-400 {
    border-color: var(--red-400) !important;
}
.border-red-500 {
    border-color: var(--red-500) !important;
}
.border-red-600 {
    border-color: var(--red-600) !important;
}
.border-red-700 {
    border-color: var(--red-700) !important;
}
.border-red-800 {
    border-color: var(--red-800) !important;
}
.border-red-900 {
    border-color: var(--red-900) !important;
}

.focus\:border-red-50:focus {
    border-color: var(--red-50) !important;
}
.focus\:border-red-100:focus {
    border-color: var(--red-100) !important;
}
.focus\:border-red-200:focus {
    border-color: var(--red-200) !important;
}
.focus\:border-red-300:focus {
    border-color: var(--red-300) !important;
}
.focus\:border-red-400:focus {
    border-color: var(--red-400) !important;
}
.focus\:border-red-500:focus {
    border-color: var(--red-500) !important;
}
.focus\:border-red-600:focus {
    border-color: var(--red-600) !important;
}
.focus\:border-red-700:focus {
    border-color: var(--red-700) !important;
}
.focus\:border-red-800:focus {
    border-color: var(--red-800) !important;
}
.focus\:border-red-900:focus {
    border-color: var(--red-900) !important;
}

.hover\:border-red-50:hover {
    border-color: var(--red-50) !important;
}
.hover\:border-red-100:hover {
    border-color: var(--red-100) !important;
}
.hover\:border-red-200:hover {
    border-color: var(--red-200) !important;
}
.hover\:border-red-300:hover {
    border-color: var(--red-300) !important;
}
.hover\:border-red-400:hover {
    border-color: var(--red-400) !important;
}
.hover\:border-red-500:hover {
    border-color: var(--red-500) !important;
}
.hover\:border-red-600:hover {
    border-color: var(--red-600) !important;
}
.hover\:border-red-700:hover {
    border-color: var(--red-700) !important;
}
.hover\:border-red-800:hover {
    border-color: var(--red-800) !important;
}
.hover\:border-red-900:hover {
    border-color: var(--red-900) !important;
}

.active\:border-red-50:active {
    border-color: var(--red-50) !important;
}
.active\:border-red-100:active {
    border-color: var(--red-100) !important;
}
.active\:border-red-200:active {
    border-color: var(--red-200) !important;
}
.active\:border-red-300:active {
    border-color: var(--red-300) !important;
}
.active\:border-red-400:active {
    border-color: var(--red-400) !important;
}
.active\:border-red-500:active {
    border-color: var(--red-500) !important;
}
.active\:border-red-600:active {
    border-color: var(--red-600) !important;
}
.active\:border-red-700:active {
    border-color: var(--red-700) !important;
}
.active\:border-red-800:active {
    border-color: var(--red-800) !important;
}
.active\:border-red-900:active {
    border-color: var(--red-900) !important;
}
