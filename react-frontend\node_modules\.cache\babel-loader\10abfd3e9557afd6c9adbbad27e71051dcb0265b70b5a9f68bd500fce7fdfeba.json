{"ast": null, "code": "'use strict';\n\nvar Info = require('./info');\nvar types = require('./types');\nmodule.exports = DefinedInfo;\nDefinedInfo.prototype = new Info();\nDefinedInfo.prototype.defined = true;\nvar checks = ['boolean', 'booleanish', 'overloadedBoolean', 'number', 'commaSeparated', 'spaceSeparated', 'commaOrSpaceSeparated'];\nvar checksLength = checks.length;\nfunction DefinedInfo(property, attribute, mask, space) {\n  var index = -1;\n  var check;\n  mark(this, 'space', space);\n  Info.call(this, property, attribute);\n  while (++index < checksLength) {\n    check = checks[index];\n    mark(this, check, (mask & types[check]) === types[check]);\n  }\n}\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value;\n  }\n}", "map": {"version": 3, "names": ["Info", "require", "types", "module", "exports", "DefinedInfo", "prototype", "defined", "checks", "<PERSON><PERSON><PERSON><PERSON>", "length", "property", "attribute", "mask", "space", "index", "check", "mark", "call", "values", "key", "value"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/property-information/lib/util/defined-info.js"], "sourcesContent": ["'use strict'\n\nvar Info = require('./info')\nvar types = require('./types')\n\nmodule.exports = DefinedInfo\n\nDefinedInfo.prototype = new Info()\nDefinedInfo.prototype.defined = true\n\nvar checks = [\n  'boolean',\n  'booleanish',\n  'overloadedBoolean',\n  'number',\n  'commaSeparated',\n  'spaceSeparated',\n  'commaOrSpaceSeparated'\n]\nvar checksLength = checks.length\n\nfunction DefinedInfo(property, attribute, mask, space) {\n  var index = -1\n  var check\n\n  mark(this, 'space', space)\n\n  Info.call(this, property, attribute)\n\n  while (++index < checksLength) {\n    check = checks[index]\n    mark(this, check, (mask & types[check]) === types[check])\n  }\n}\n\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAC5B,IAAIC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;AAE9BE,MAAM,CAACC,OAAO,GAAGC,WAAW;AAE5BA,WAAW,CAACC,SAAS,GAAG,IAAIN,IAAI,CAAC,CAAC;AAClCK,WAAW,CAACC,SAAS,CAACC,OAAO,GAAG,IAAI;AAEpC,IAAIC,MAAM,GAAG,CACX,SAAS,EACT,YAAY,EACZ,mBAAmB,EACnB,QAAQ,EACR,gBAAgB,EAChB,gBAAgB,EAChB,uBAAuB,CACxB;AACD,IAAIC,YAAY,GAAGD,MAAM,CAACE,MAAM;AAEhC,SAASL,WAAWA,CAACM,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACrD,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,KAAK;EAETC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAEH,KAAK,CAAC;EAE1Bd,IAAI,CAACkB,IAAI,CAAC,IAAI,EAAEP,QAAQ,EAAEC,SAAS,CAAC;EAEpC,OAAO,EAAEG,KAAK,GAAGN,YAAY,EAAE;IAC7BO,KAAK,GAAGR,MAAM,CAACO,KAAK,CAAC;IACrBE,IAAI,CAAC,IAAI,EAAED,KAAK,EAAE,CAACH,IAAI,GAAGX,KAAK,CAACc,KAAK,CAAC,MAAMd,KAAK,CAACc,KAAK,CAAC,CAAC;EAC3D;AACF;AAEA,SAASC,IAAIA,CAACE,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAChC,IAAIA,KAAK,EAAE;IACTF,MAAM,CAACC,GAAG,CAAC,GAAGC,KAAK;EACrB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}