import React from "react";
import { render, screen } from "@testing-library/react";

import MessagePage from "../MessagePage";
import { MemoryRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import { init } from "@rematch/core";
import { Provider } from "react-redux";
import * as models from "../../../models";

test("renders message page", async () => {
    const store = init({ models });
    render(
        <Provider store={store}>
            <MemoryRouter>
                <MessagePage />
            </MemoryRouter>
        </Provider>
    );
    expect(screen.getByRole("message-datatable")).toBeInTheDocument();
    expect(screen.getByRole("message-add-button")).toBeInTheDocument();
});
