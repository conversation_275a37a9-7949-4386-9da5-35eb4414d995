{"ast": null, "code": "/// <reference lib=\"WebWorker\"/>\n\nvar _self = typeof window !== 'undefined' ? window // if in browser\n: typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope ? self // if in worker\n: {} // if in node js\n;\n\n/**\n * Prism: Lightweight, robust, elegant syntax highlighting\n *\n * @license MIT <https://opensource.org/licenses/MIT>\n * <AUTHOR> <https://lea.verou.me>\n * @namespace\n * @public\n */\nvar Prism = function (_self) {\n  // Private helper vars\n  var lang = /(?:^|\\s)lang(?:uage)?-([\\w-]+)(?=\\s|$)/i;\n  var uniqueId = 0;\n\n  // The grammar object for plaintext\n  var plainTextGrammar = {};\n  var _ = {\n    /**\n     * By default, Prism will attempt to highlight all code elements (by calling {@link Prism.highlightAll}) on the\n     * current page after the page finished loading. This might be a problem if e.g. you wanted to asynchronously load\n     * additional languages or plugins yourself.\n     *\n     * By setting this value to `true`, Prism will not automatically highlight all code elements on the page.\n     *\n     * You obviously have to change this value before the automatic highlighting started. To do this, you can add an\n     * empty Prism object into the global scope before loading the Prism script like this:\n     *\n     * ```js\n     * window.Prism = window.Prism || {};\n     * Prism.manual = true;\n     * // add a new <script> to load Prism's script\n     * ```\n     *\n     * @default false\n     * @type {boolean}\n     * @memberof Prism\n     * @public\n     */\n    manual: _self.Prism && _self.Prism.manual,\n    /**\n     * By default, if Prism is in a web worker, it assumes that it is in a worker it created itself, so it uses\n     * `addEventListener` to communicate with its parent instance. However, if you're using Prism manually in your\n     * own worker, you don't want it to do this.\n     *\n     * By setting this value to `true`, Prism will not add its own listeners to the worker.\n     *\n     * You obviously have to change this value before Prism executes. To do this, you can add an\n     * empty Prism object into the global scope before loading the Prism script like this:\n     *\n     * ```js\n     * window.Prism = window.Prism || {};\n     * Prism.disableWorkerMessageHandler = true;\n     * // Load Prism's script\n     * ```\n     *\n     * @default false\n     * @type {boolean}\n     * @memberof Prism\n     * @public\n     */\n    disableWorkerMessageHandler: _self.Prism && _self.Prism.disableWorkerMessageHandler,\n    /**\n     * A namespace for utility methods.\n     *\n     * All function in this namespace that are not explicitly marked as _public_ are for __internal use only__ and may\n     * change or disappear at any time.\n     *\n     * @namespace\n     * @memberof Prism\n     */\n    util: {\n      encode: function encode(tokens) {\n        if (tokens instanceof Token) {\n          return new Token(tokens.type, encode(tokens.content), tokens.alias);\n        } else if (Array.isArray(tokens)) {\n          return tokens.map(encode);\n        } else {\n          return tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\\u00a0/g, ' ');\n        }\n      },\n      /**\n       * Returns the name of the type of the given value.\n       *\n       * @param {any} o\n       * @returns {string}\n       * @example\n       * type(null)      === 'Null'\n       * type(undefined) === 'Undefined'\n       * type(123)       === 'Number'\n       * type('foo')     === 'String'\n       * type(true)      === 'Boolean'\n       * type([1, 2])    === 'Array'\n       * type({})        === 'Object'\n       * type(String)    === 'Function'\n       * type(/abc+/)    === 'RegExp'\n       */\n      type: function (o) {\n        return Object.prototype.toString.call(o).slice(8, -1);\n      },\n      /**\n       * Returns a unique number for the given object. Later calls will still return the same number.\n       *\n       * @param {Object} obj\n       * @returns {number}\n       */\n      objId: function (obj) {\n        if (!obj['__id']) {\n          Object.defineProperty(obj, '__id', {\n            value: ++uniqueId\n          });\n        }\n        return obj['__id'];\n      },\n      /**\n       * Creates a deep clone of the given object.\n       *\n       * The main intended use of this function is to clone language definitions.\n       *\n       * @param {T} o\n       * @param {Record<number, any>} [visited]\n       * @returns {T}\n       * @template T\n       */\n      clone: function deepClone(o, visited) {\n        visited = visited || {};\n        var clone;\n        var id;\n        switch (_.util.type(o)) {\n          case 'Object':\n            id = _.util.objId(o);\n            if (visited[id]) {\n              return visited[id];\n            }\n            clone = /** @type {Record<string, any>} */{};\n            visited[id] = clone;\n            for (var key in o) {\n              if (o.hasOwnProperty(key)) {\n                clone[key] = deepClone(o[key], visited);\n              }\n            }\n            return /** @type {any} */clone;\n          case 'Array':\n            id = _.util.objId(o);\n            if (visited[id]) {\n              return visited[id];\n            }\n            clone = [];\n            visited[id] = clone;\n            (/** @type {Array} */ /** @type {any} */o).forEach(function (v, i) {\n              clone[i] = deepClone(v, visited);\n            });\n            return /** @type {any} */clone;\n          default:\n            return o;\n        }\n      },\n      /**\n       * Returns the Prism language of the given element set by a `language-xxxx` or `lang-xxxx` class.\n       *\n       * If no language is set for the element or the element is `null` or `undefined`, `none` will be returned.\n       *\n       * @param {Element} element\n       * @returns {string}\n       */\n      getLanguage: function (element) {\n        while (element) {\n          var m = lang.exec(element.className);\n          if (m) {\n            return m[1].toLowerCase();\n          }\n          element = element.parentElement;\n        }\n        return 'none';\n      },\n      /**\n       * Sets the Prism `language-xxxx` class of the given element.\n       *\n       * @param {Element} element\n       * @param {string} language\n       * @returns {void}\n       */\n      setLanguage: function (element, language) {\n        // remove all `language-xxxx` classes\n        // (this might leave behind a leading space)\n        element.className = element.className.replace(RegExp(lang, 'gi'), '');\n\n        // add the new `language-xxxx` class\n        // (using `classList` will automatically clean up spaces for us)\n        element.classList.add('language-' + language);\n      },\n      /**\n       * Returns the script element that is currently executing.\n       *\n       * This does __not__ work for line script element.\n       *\n       * @returns {HTMLScriptElement | null}\n       */\n      currentScript: function () {\n        if (typeof document === 'undefined') {\n          return null;\n        }\n        if ('currentScript' in document && 1 < 2 /* hack to trip TS' flow analysis */) {\n          return /** @type {any} */document.currentScript;\n        }\n\n        // IE11 workaround\n        // we'll get the src of the current script by parsing IE11's error stack trace\n        // this will not work for inline scripts\n\n        try {\n          throw new Error();\n        } catch (err) {\n          // Get file src url from stack. Specifically works with the format of stack traces in IE.\n          // A stack will look like this:\n          //\n          // Error\n          //    at _.util.currentScript (http://localhost/components/prism-core.js:119:5)\n          //    at Global code (http://localhost/components/prism-core.js:606:1)\n\n          var src = (/at [^(\\r\\n]*\\((.*):[^:]+:[^:]+\\)$/i.exec(err.stack) || [])[1];\n          if (src) {\n            var scripts = document.getElementsByTagName('script');\n            for (var i in scripts) {\n              if (scripts[i].src == src) {\n                return scripts[i];\n              }\n            }\n          }\n          return null;\n        }\n      },\n      /**\n       * Returns whether a given class is active for `element`.\n       *\n       * The class can be activated if `element` or one of its ancestors has the given class and it can be deactivated\n       * if `element` or one of its ancestors has the negated version of the given class. The _negated version_ of the\n       * given class is just the given class with a `no-` prefix.\n       *\n       * Whether the class is active is determined by the closest ancestor of `element` (where `element` itself is\n       * closest ancestor) that has the given class or the negated version of it. If neither `element` nor any of its\n       * ancestors have the given class or the negated version of it, then the default activation will be returned.\n       *\n       * In the paradoxical situation where the closest ancestor contains __both__ the given class and the negated\n       * version of it, the class is considered active.\n       *\n       * @param {Element} element\n       * @param {string} className\n       * @param {boolean} [defaultActivation=false]\n       * @returns {boolean}\n       */\n      isActive: function (element, className, defaultActivation) {\n        var no = 'no-' + className;\n        while (element) {\n          var classList = element.classList;\n          if (classList.contains(className)) {\n            return true;\n          }\n          if (classList.contains(no)) {\n            return false;\n          }\n          element = element.parentElement;\n        }\n        return !!defaultActivation;\n      }\n    },\n    /**\n     * This namespace contains all currently loaded languages and the some helper functions to create and modify languages.\n     *\n     * @namespace\n     * @memberof Prism\n     * @public\n     */\n    languages: {\n      /**\n       * The grammar for plain, unformatted text.\n       */\n      plain: plainTextGrammar,\n      plaintext: plainTextGrammar,\n      text: plainTextGrammar,\n      txt: plainTextGrammar,\n      /**\n       * Creates a deep copy of the language with the given id and appends the given tokens.\n       *\n       * If a token in `redef` also appears in the copied language, then the existing token in the copied language\n       * will be overwritten at its original position.\n       *\n       * ## Best practices\n       *\n       * Since the position of overwriting tokens (token in `redef` that overwrite tokens in the copied language)\n       * doesn't matter, they can technically be in any order. However, this can be confusing to others that trying to\n       * understand the language definition because, normally, the order of tokens matters in Prism grammars.\n       *\n       * Therefore, it is encouraged to order overwriting tokens according to the positions of the overwritten tokens.\n       * Furthermore, all non-overwriting tokens should be placed after the overwriting ones.\n       *\n       * @param {string} id The id of the language to extend. This has to be a key in `Prism.languages`.\n       * @param {Grammar} redef The new tokens to append.\n       * @returns {Grammar} The new language created.\n       * @public\n       * @example\n       * Prism.languages['css-with-colors'] = Prism.languages.extend('css', {\n       *     // Prism.languages.css already has a 'comment' token, so this token will overwrite CSS' 'comment' token\n       *     // at its original position\n       *     'comment': { ... },\n       *     // CSS doesn't have a 'color' token, so this token will be appended\n       *     'color': /\\b(?:red|green|blue)\\b/\n       * });\n       */\n      extend: function (id, redef) {\n        var lang = _.util.clone(_.languages[id]);\n        for (var key in redef) {\n          lang[key] = redef[key];\n        }\n        return lang;\n      },\n      /**\n       * Inserts tokens _before_ another token in a language definition or any other grammar.\n       *\n       * ## Usage\n       *\n       * This helper method makes it easy to modify existing languages. For example, the CSS language definition\n       * not only defines CSS highlighting for CSS documents, but also needs to define highlighting for CSS embedded\n       * in HTML through `<style>` elements. To do this, it needs to modify `Prism.languages.markup` and add the\n       * appropriate tokens. However, `Prism.languages.markup` is a regular JavaScript object literal, so if you do\n       * this:\n       *\n       * ```js\n       * Prism.languages.markup.style = {\n       *     // token\n       * };\n       * ```\n       *\n       * then the `style` token will be added (and processed) at the end. `insertBefore` allows you to insert tokens\n       * before existing tokens. For the CSS example above, you would use it like this:\n       *\n       * ```js\n       * Prism.languages.insertBefore('markup', 'cdata', {\n       *     'style': {\n       *         // token\n       *     }\n       * });\n       * ```\n       *\n       * ## Special cases\n       *\n       * If the grammars of `inside` and `insert` have tokens with the same name, the tokens in `inside`'s grammar\n       * will be ignored.\n       *\n       * This behavior can be used to insert tokens after `before`:\n       *\n       * ```js\n       * Prism.languages.insertBefore('markup', 'comment', {\n       *     'comment': Prism.languages.markup.comment,\n       *     // tokens after 'comment'\n       * });\n       * ```\n       *\n       * ## Limitations\n       *\n       * The main problem `insertBefore` has to solve is iteration order. Since ES2015, the iteration order for object\n       * properties is guaranteed to be the insertion order (except for integer keys) but some browsers behave\n       * differently when keys are deleted and re-inserted. So `insertBefore` can't be implemented by temporarily\n       * deleting properties which is necessary to insert at arbitrary positions.\n       *\n       * To solve this problem, `insertBefore` doesn't actually insert the given tokens into the target object.\n       * Instead, it will create a new object and replace all references to the target object with the new one. This\n       * can be done without temporarily deleting properties, so the iteration order is well-defined.\n       *\n       * However, only references that can be reached from `Prism.languages` or `insert` will be replaced. I.e. if\n       * you hold the target object in a variable, then the value of the variable will not change.\n       *\n       * ```js\n       * var oldMarkup = Prism.languages.markup;\n       * var newMarkup = Prism.languages.insertBefore('markup', 'comment', { ... });\n       *\n       * assert(oldMarkup !== Prism.languages.markup);\n       * assert(newMarkup === Prism.languages.markup);\n       * ```\n       *\n       * @param {string} inside The property of `root` (e.g. a language id in `Prism.languages`) that contains the\n       * object to be modified.\n       * @param {string} before The key to insert before.\n       * @param {Grammar} insert An object containing the key-value pairs to be inserted.\n       * @param {Object<string, any>} [root] The object containing `inside`, i.e. the object that contains the\n       * object to be modified.\n       *\n       * Defaults to `Prism.languages`.\n       * @returns {Grammar} The new grammar object.\n       * @public\n       */\n      insertBefore: function (inside, before, insert, root) {\n        root = root || (/** @type {any} */_.languages);\n        var grammar = root[inside];\n        /** @type {Grammar} */\n        var ret = {};\n        for (var token in grammar) {\n          if (grammar.hasOwnProperty(token)) {\n            if (token == before) {\n              for (var newToken in insert) {\n                if (insert.hasOwnProperty(newToken)) {\n                  ret[newToken] = insert[newToken];\n                }\n              }\n            }\n\n            // Do not insert token which also occur in insert. See #1525\n            if (!insert.hasOwnProperty(token)) {\n              ret[token] = grammar[token];\n            }\n          }\n        }\n        var old = root[inside];\n        root[inside] = ret;\n\n        // Update references in other language definitions\n        _.languages.DFS(_.languages, function (key, value) {\n          if (value === old && key != inside) {\n            this[key] = ret;\n          }\n        });\n        return ret;\n      },\n      // Traverse a language definition with Depth First Search\n      DFS: function DFS(o, callback, type, visited) {\n        visited = visited || {};\n        var objId = _.util.objId;\n        for (var i in o) {\n          if (o.hasOwnProperty(i)) {\n            callback.call(o, i, o[i], type || i);\n            var property = o[i];\n            var propertyType = _.util.type(property);\n            if (propertyType === 'Object' && !visited[objId(property)]) {\n              visited[objId(property)] = true;\n              DFS(property, callback, null, visited);\n            } else if (propertyType === 'Array' && !visited[objId(property)]) {\n              visited[objId(property)] = true;\n              DFS(property, callback, i, visited);\n            }\n          }\n        }\n      }\n    },\n    plugins: {},\n    /**\n     * This is the most high-level function in Prism’s API.\n     * It fetches all the elements that have a `.language-xxxx` class and then calls {@link Prism.highlightElement} on\n     * each one of them.\n     *\n     * This is equivalent to `Prism.highlightAllUnder(document, async, callback)`.\n     *\n     * @param {boolean} [async=false] Same as in {@link Prism.highlightAllUnder}.\n     * @param {HighlightCallback} [callback] Same as in {@link Prism.highlightAllUnder}.\n     * @memberof Prism\n     * @public\n     */\n    highlightAll: function (async, callback) {\n      _.highlightAllUnder(document, async, callback);\n    },\n    /**\n     * Fetches all the descendants of `container` that have a `.language-xxxx` class and then calls\n     * {@link Prism.highlightElement} on each one of them.\n     *\n     * The following hooks will be run:\n     * 1. `before-highlightall`\n     * 2. `before-all-elements-highlight`\n     * 3. All hooks of {@link Prism.highlightElement} for each element.\n     *\n     * @param {ParentNode} container The root element, whose descendants that have a `.language-xxxx` class will be highlighted.\n     * @param {boolean} [async=false] Whether each element is to be highlighted asynchronously using Web Workers.\n     * @param {HighlightCallback} [callback] An optional callback to be invoked on each element after its highlighting is done.\n     * @memberof Prism\n     * @public\n     */\n    highlightAllUnder: function (container, async, callback) {\n      var env = {\n        callback: callback,\n        container: container,\n        selector: 'code[class*=\"language-\"], [class*=\"language-\"] code, code[class*=\"lang-\"], [class*=\"lang-\"] code'\n      };\n      _.hooks.run('before-highlightall', env);\n      env.elements = Array.prototype.slice.apply(env.container.querySelectorAll(env.selector));\n      _.hooks.run('before-all-elements-highlight', env);\n      for (var i = 0, element; element = env.elements[i++];) {\n        _.highlightElement(element, async === true, env.callback);\n      }\n    },\n    /**\n     * Highlights the code inside a single element.\n     *\n     * The following hooks will be run:\n     * 1. `before-sanity-check`\n     * 2. `before-highlight`\n     * 3. All hooks of {@link Prism.highlight}. These hooks will be run by an asynchronous worker if `async` is `true`.\n     * 4. `before-insert`\n     * 5. `after-highlight`\n     * 6. `complete`\n     *\n     * Some the above hooks will be skipped if the element doesn't contain any text or there is no grammar loaded for\n     * the element's language.\n     *\n     * @param {Element} element The element containing the code.\n     * It must have a class of `language-xxxx` to be processed, where `xxxx` is a valid language identifier.\n     * @param {boolean} [async=false] Whether the element is to be highlighted asynchronously using Web Workers\n     * to improve performance and avoid blocking the UI when highlighting very large chunks of code. This option is\n     * [disabled by default](https://prismjs.com/faq.html#why-is-asynchronous-highlighting-disabled-by-default).\n     *\n     * Note: All language definitions required to highlight the code must be included in the main `prism.js` file for\n     * asynchronous highlighting to work. You can build your own bundle on the\n     * [Download page](https://prismjs.com/download.html).\n     * @param {HighlightCallback} [callback] An optional callback to be invoked after the highlighting is done.\n     * Mostly useful when `async` is `true`, since in that case, the highlighting is done asynchronously.\n     * @memberof Prism\n     * @public\n     */\n    highlightElement: function (element, async, callback) {\n      // Find language\n      var language = _.util.getLanguage(element);\n      var grammar = _.languages[language];\n\n      // Set language on the element, if not present\n      _.util.setLanguage(element, language);\n\n      // Set language on the parent, for styling\n      var parent = element.parentElement;\n      if (parent && parent.nodeName.toLowerCase() === 'pre') {\n        _.util.setLanguage(parent, language);\n      }\n      var code = element.textContent;\n      var env = {\n        element: element,\n        language: language,\n        grammar: grammar,\n        code: code\n      };\n      function insertHighlightedCode(highlightedCode) {\n        env.highlightedCode = highlightedCode;\n        _.hooks.run('before-insert', env);\n        env.element.innerHTML = env.highlightedCode;\n        _.hooks.run('after-highlight', env);\n        _.hooks.run('complete', env);\n        callback && callback.call(env.element);\n      }\n      _.hooks.run('before-sanity-check', env);\n\n      // plugins may change/add the parent/element\n      parent = env.element.parentElement;\n      if (parent && parent.nodeName.toLowerCase() === 'pre' && !parent.hasAttribute('tabindex')) {\n        parent.setAttribute('tabindex', '0');\n      }\n      if (!env.code) {\n        _.hooks.run('complete', env);\n        callback && callback.call(env.element);\n        return;\n      }\n      _.hooks.run('before-highlight', env);\n      if (!env.grammar) {\n        insertHighlightedCode(_.util.encode(env.code));\n        return;\n      }\n      if (async && _self.Worker) {\n        var worker = new Worker(_.filename);\n        worker.onmessage = function (evt) {\n          insertHighlightedCode(evt.data);\n        };\n        worker.postMessage(JSON.stringify({\n          language: env.language,\n          code: env.code,\n          immediateClose: true\n        }));\n      } else {\n        insertHighlightedCode(_.highlight(env.code, env.grammar, env.language));\n      }\n    },\n    /**\n     * Low-level function, only use if you know what you’re doing. It accepts a string of text as input\n     * and the language definitions to use, and returns a string with the HTML produced.\n     *\n     * The following hooks will be run:\n     * 1. `before-tokenize`\n     * 2. `after-tokenize`\n     * 3. `wrap`: On each {@link Token}.\n     *\n     * @param {string} text A string with the code to be highlighted.\n     * @param {Grammar} grammar An object containing the tokens to use.\n     *\n     * Usually a language definition like `Prism.languages.markup`.\n     * @param {string} language The name of the language definition passed to `grammar`.\n     * @returns {string} The highlighted HTML.\n     * @memberof Prism\n     * @public\n     * @example\n     * Prism.highlight('var foo = true;', Prism.languages.javascript, 'javascript');\n     */\n    highlight: function (text, grammar, language) {\n      var env = {\n        code: text,\n        grammar: grammar,\n        language: language\n      };\n      _.hooks.run('before-tokenize', env);\n      if (!env.grammar) {\n        throw new Error('The language \"' + env.language + '\" has no grammar.');\n      }\n      env.tokens = _.tokenize(env.code, env.grammar);\n      _.hooks.run('after-tokenize', env);\n      return Token.stringify(_.util.encode(env.tokens), env.language);\n    },\n    /**\n     * This is the heart of Prism, and the most low-level function you can use. It accepts a string of text as input\n     * and the language definitions to use, and returns an array with the tokenized code.\n     *\n     * When the language definition includes nested tokens, the function is called recursively on each of these tokens.\n     *\n     * This method could be useful in other contexts as well, as a very crude parser.\n     *\n     * @param {string} text A string with the code to be highlighted.\n     * @param {Grammar} grammar An object containing the tokens to use.\n     *\n     * Usually a language definition like `Prism.languages.markup`.\n     * @returns {TokenStream} An array of strings and tokens, a token stream.\n     * @memberof Prism\n     * @public\n     * @example\n     * let code = `var foo = 0;`;\n     * let tokens = Prism.tokenize(code, Prism.languages.javascript);\n     * tokens.forEach(token => {\n     *     if (token instanceof Prism.Token && token.type === 'number') {\n     *         console.log(`Found numeric literal: ${token.content}`);\n     *     }\n     * });\n     */\n    tokenize: function (text, grammar) {\n      var rest = grammar.rest;\n      if (rest) {\n        for (var token in rest) {\n          grammar[token] = rest[token];\n        }\n        delete grammar.rest;\n      }\n      var tokenList = new LinkedList();\n      addAfter(tokenList, tokenList.head, text);\n      matchGrammar(text, tokenList, grammar, tokenList.head, 0);\n      return toArray(tokenList);\n    },\n    /**\n     * @namespace\n     * @memberof Prism\n     * @public\n     */\n    hooks: {\n      all: {},\n      /**\n       * Adds the given callback to the list of callbacks for the given hook.\n       *\n       * The callback will be invoked when the hook it is registered for is run.\n       * Hooks are usually directly run by a highlight function but you can also run hooks yourself.\n       *\n       * One callback function can be registered to multiple hooks and the same hook multiple times.\n       *\n       * @param {string} name The name of the hook.\n       * @param {HookCallback} callback The callback function which is given environment variables.\n       * @public\n       */\n      add: function (name, callback) {\n        var hooks = _.hooks.all;\n        hooks[name] = hooks[name] || [];\n        hooks[name].push(callback);\n      },\n      /**\n       * Runs a hook invoking all registered callbacks with the given environment variables.\n       *\n       * Callbacks will be invoked synchronously and in the order in which they were registered.\n       *\n       * @param {string} name The name of the hook.\n       * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n       * @public\n       */\n      run: function (name, env) {\n        var callbacks = _.hooks.all[name];\n        if (!callbacks || !callbacks.length) {\n          return;\n        }\n        for (var i = 0, callback; callback = callbacks[i++];) {\n          callback(env);\n        }\n      }\n    },\n    Token: Token\n  };\n  _self.Prism = _;\n\n  // Typescript note:\n  // The following can be used to import the Token type in JSDoc:\n  //\n  //   @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n\n  /**\n   * Creates a new token.\n   *\n   * @param {string} type See {@link Token#type type}\n   * @param {string | TokenStream} content See {@link Token#content content}\n   * @param {string|string[]} [alias] The alias(es) of the token.\n   * @param {string} [matchedStr=\"\"] A copy of the full string this token was created from.\n   * @class\n   * @global\n   * @public\n   */\n  function Token(type, content, alias, matchedStr) {\n    /**\n     * The type of the token.\n     *\n     * This is usually the key of a pattern in a {@link Grammar}.\n     *\n     * @type {string}\n     * @see GrammarToken\n     * @public\n     */\n    this.type = type;\n    /**\n     * The strings or tokens contained by this token.\n     *\n     * This will be a token stream if the pattern matched also defined an `inside` grammar.\n     *\n     * @type {string | TokenStream}\n     * @public\n     */\n    this.content = content;\n    /**\n     * The alias(es) of the token.\n     *\n     * @type {string|string[]}\n     * @see GrammarToken\n     * @public\n     */\n    this.alias = alias;\n    // Copy of the full string this token was created from\n    this.length = (matchedStr || '').length | 0;\n  }\n\n  /**\n   * A token stream is an array of strings and {@link Token Token} objects.\n   *\n   * Token streams have to fulfill a few properties that are assumed by most functions (mostly internal ones) that process\n   * them.\n   *\n   * 1. No adjacent strings.\n   * 2. No empty strings.\n   *\n   *    The only exception here is the token stream that only contains the empty string and nothing else.\n   *\n   * @typedef {Array<string | Token>} TokenStream\n   * @global\n   * @public\n   */\n\n  /**\n   * Converts the given token or token stream to an HTML representation.\n   *\n   * The following hooks will be run:\n   * 1. `wrap`: On each {@link Token}.\n   *\n   * @param {string | Token | TokenStream} o The token or token stream to be converted.\n   * @param {string} language The name of current language.\n   * @returns {string} The HTML representation of the token or token stream.\n   * @memberof Token\n   * @static\n   */\n  Token.stringify = function stringify(o, language) {\n    if (typeof o == 'string') {\n      return o;\n    }\n    if (Array.isArray(o)) {\n      var s = '';\n      o.forEach(function (e) {\n        s += stringify(e, language);\n      });\n      return s;\n    }\n    var env = {\n      type: o.type,\n      content: stringify(o.content, language),\n      tag: 'span',\n      classes: ['token', o.type],\n      attributes: {},\n      language: language\n    };\n    var aliases = o.alias;\n    if (aliases) {\n      if (Array.isArray(aliases)) {\n        Array.prototype.push.apply(env.classes, aliases);\n      } else {\n        env.classes.push(aliases);\n      }\n    }\n    _.hooks.run('wrap', env);\n    var attributes = '';\n    for (var name in env.attributes) {\n      attributes += ' ' + name + '=\"' + (env.attributes[name] || '').replace(/\"/g, '&quot;') + '\"';\n    }\n    return '<' + env.tag + ' class=\"' + env.classes.join(' ') + '\"' + attributes + '>' + env.content + '</' + env.tag + '>';\n  };\n\n  /**\n   * @param {RegExp} pattern\n   * @param {number} pos\n   * @param {string} text\n   * @param {boolean} lookbehind\n   * @returns {RegExpExecArray | null}\n   */\n  function matchPattern(pattern, pos, text, lookbehind) {\n    pattern.lastIndex = pos;\n    var match = pattern.exec(text);\n    if (match && lookbehind && match[1]) {\n      // change the match to remove the text matched by the Prism lookbehind group\n      var lookbehindLength = match[1].length;\n      match.index += lookbehindLength;\n      match[0] = match[0].slice(lookbehindLength);\n    }\n    return match;\n  }\n\n  /**\n   * @param {string} text\n   * @param {LinkedList<string | Token>} tokenList\n   * @param {any} grammar\n   * @param {LinkedListNode<string | Token>} startNode\n   * @param {number} startPos\n   * @param {RematchOptions} [rematch]\n   * @returns {void}\n   * @private\n   *\n   * @typedef RematchOptions\n   * @property {string} cause\n   * @property {number} reach\n   */\n  function matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n    for (var token in grammar) {\n      if (!grammar.hasOwnProperty(token) || !grammar[token]) {\n        continue;\n      }\n      var patterns = grammar[token];\n      patterns = Array.isArray(patterns) ? patterns : [patterns];\n      for (var j = 0; j < patterns.length; ++j) {\n        if (rematch && rematch.cause == token + ',' + j) {\n          return;\n        }\n        var patternObj = patterns[j];\n        var inside = patternObj.inside;\n        var lookbehind = !!patternObj.lookbehind;\n        var greedy = !!patternObj.greedy;\n        var alias = patternObj.alias;\n        if (greedy && !patternObj.pattern.global) {\n          // Without the global flag, lastIndex won't work\n          var flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n          patternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');\n        }\n\n        /** @type {RegExp} */\n        var pattern = patternObj.pattern || patternObj;\n        for (\n        // iterate the token list and keep track of the current token/string position\n        var currentNode = startNode.next, pos = startPos; currentNode !== tokenList.tail; pos += currentNode.value.length, currentNode = currentNode.next) {\n          if (rematch && pos >= rematch.reach) {\n            break;\n          }\n          var str = currentNode.value;\n          if (tokenList.length > text.length) {\n            // Something went terribly wrong, ABORT, ABORT!\n            return;\n          }\n          if (str instanceof Token) {\n            continue;\n          }\n          var removeCount = 1; // this is the to parameter of removeBetween\n          var match;\n          if (greedy) {\n            match = matchPattern(pattern, pos, text, lookbehind);\n            if (!match || match.index >= text.length) {\n              break;\n            }\n            var from = match.index;\n            var to = match.index + match[0].length;\n            var p = pos;\n\n            // find the node that contains the match\n            p += currentNode.value.length;\n            while (from >= p) {\n              currentNode = currentNode.next;\n              p += currentNode.value.length;\n            }\n            // adjust pos (and p)\n            p -= currentNode.value.length;\n            pos = p;\n\n            // the current node is a Token, then the match starts inside another Token, which is invalid\n            if (currentNode.value instanceof Token) {\n              continue;\n            }\n\n            // find the last node which is affected by this match\n            for (var k = currentNode; k !== tokenList.tail && (p < to || typeof k.value === 'string'); k = k.next) {\n              removeCount++;\n              p += k.value.length;\n            }\n            removeCount--;\n\n            // replace with the new match\n            str = text.slice(pos, p);\n            match.index -= pos;\n          } else {\n            match = matchPattern(pattern, 0, str, lookbehind);\n            if (!match) {\n              continue;\n            }\n          }\n\n          // eslint-disable-next-line no-redeclare\n          var from = match.index;\n          var matchStr = match[0];\n          var before = str.slice(0, from);\n          var after = str.slice(from + matchStr.length);\n          var reach = pos + str.length;\n          if (rematch && reach > rematch.reach) {\n            rematch.reach = reach;\n          }\n          var removeFrom = currentNode.prev;\n          if (before) {\n            removeFrom = addAfter(tokenList, removeFrom, before);\n            pos += before.length;\n          }\n          removeRange(tokenList, removeFrom, removeCount);\n          var wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n          currentNode = addAfter(tokenList, removeFrom, wrapped);\n          if (after) {\n            addAfter(tokenList, currentNode, after);\n          }\n          if (removeCount > 1) {\n            // at least one Token object was removed, so we have to do some rematching\n            // this can only happen if the current pattern is greedy\n\n            /** @type {RematchOptions} */\n            var nestedRematch = {\n              cause: token + ',' + j,\n              reach: reach\n            };\n            matchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n\n            // the reach might have been extended because of the rematching\n            if (rematch && nestedRematch.reach > rematch.reach) {\n              rematch.reach = nestedRematch.reach;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * @typedef LinkedListNode\n   * @property {T} value\n   * @property {LinkedListNode<T> | null} prev The previous node.\n   * @property {LinkedListNode<T> | null} next The next node.\n   * @template T\n   * @private\n   */\n\n  /**\n   * @template T\n   * @private\n   */\n  function LinkedList() {\n    /** @type {LinkedListNode<T>} */\n    var head = {\n      value: null,\n      prev: null,\n      next: null\n    };\n    /** @type {LinkedListNode<T>} */\n    var tail = {\n      value: null,\n      prev: head,\n      next: null\n    };\n    head.next = tail;\n\n    /** @type {LinkedListNode<T>} */\n    this.head = head;\n    /** @type {LinkedListNode<T>} */\n    this.tail = tail;\n    this.length = 0;\n  }\n\n  /**\n   * Adds a new node with the given value to the list.\n   *\n   * @param {LinkedList<T>} list\n   * @param {LinkedListNode<T>} node\n   * @param {T} value\n   * @returns {LinkedListNode<T>} The added node.\n   * @template T\n   */\n  function addAfter(list, node, value) {\n    // assumes that node != list.tail && values.length >= 0\n    var next = node.next;\n    var newNode = {\n      value: value,\n      prev: node,\n      next: next\n    };\n    node.next = newNode;\n    next.prev = newNode;\n    list.length++;\n    return newNode;\n  }\n  /**\n   * Removes `count` nodes after the given node. The given node will not be removed.\n   *\n   * @param {LinkedList<T>} list\n   * @param {LinkedListNode<T>} node\n   * @param {number} count\n   * @template T\n   */\n  function removeRange(list, node, count) {\n    var next = node.next;\n    for (var i = 0; i < count && next !== list.tail; i++) {\n      next = next.next;\n    }\n    node.next = next;\n    next.prev = node;\n    list.length -= i;\n  }\n  /**\n   * @param {LinkedList<T>} list\n   * @returns {T[]}\n   * @template T\n   */\n  function toArray(list) {\n    var array = [];\n    var node = list.head.next;\n    while (node !== list.tail) {\n      array.push(node.value);\n      node = node.next;\n    }\n    return array;\n  }\n  if (!_self.document) {\n    if (!_self.addEventListener) {\n      // in Node.js\n      return _;\n    }\n    if (!_.disableWorkerMessageHandler) {\n      // In worker\n      _self.addEventListener('message', function (evt) {\n        var message = JSON.parse(evt.data);\n        var lang = message.language;\n        var code = message.code;\n        var immediateClose = message.immediateClose;\n        _self.postMessage(_.highlight(code, _.languages[lang], lang));\n        if (immediateClose) {\n          _self.close();\n        }\n      }, false);\n    }\n    return _;\n  }\n\n  // Get current script and highlight\n  var script = _.util.currentScript();\n  if (script) {\n    _.filename = script.src;\n    if (script.hasAttribute('data-manual')) {\n      _.manual = true;\n    }\n  }\n  function highlightAutomaticallyCallback() {\n    if (!_.manual) {\n      _.highlightAll();\n    }\n  }\n  if (!_.manual) {\n    // If the document state is \"loading\", then we'll use DOMContentLoaded.\n    // If the document state is \"interactive\" and the prism.js script is deferred, then we'll also use the\n    // DOMContentLoaded event because there might be some plugins or languages which have also been deferred and they\n    // might take longer one animation frame to execute which can create a race condition where only some plugins have\n    // been loaded when Prism.highlightAll() is executed, depending on how fast resources are loaded.\n    // See https://github.com/PrismJS/prism/issues/2102\n    var readyState = document.readyState;\n    if (readyState === 'loading' || readyState === 'interactive' && script && script.defer) {\n      document.addEventListener('DOMContentLoaded', highlightAutomaticallyCallback);\n    } else {\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(highlightAutomaticallyCallback);\n      } else {\n        window.setTimeout(highlightAutomaticallyCallback, 16);\n      }\n    }\n  }\n  return _;\n}(_self);\nif (typeof module !== 'undefined' && module.exports) {\n  module.exports = Prism;\n}\n\n// hack for components to work correctly in node.js\nif (typeof global !== 'undefined') {\n  global.Prism = Prism;\n}\n\n// some additional documentation/types\n\n/**\n * The expansion of a simple `RegExp` literal to support additional properties.\n *\n * @typedef GrammarToken\n * @property {RegExp} pattern The regular expression of the token.\n * @property {boolean} [lookbehind=false] If `true`, then the first capturing group of `pattern` will (effectively)\n * behave as a lookbehind group meaning that the captured text will not be part of the matched text of the new token.\n * @property {boolean} [greedy=false] Whether the token is greedy.\n * @property {string|string[]} [alias] An optional alias or list of aliases.\n * @property {Grammar} [inside] The nested grammar of this token.\n *\n * The `inside` grammar will be used to tokenize the text value of each token of this kind.\n *\n * This can be used to make nested and even recursive language definitions.\n *\n * Note: This can cause infinite recursion. Be careful when you embed different languages or even the same language into\n * each another.\n * @global\n * @public\n */\n\n/**\n * @typedef Grammar\n * @type {Object<string, RegExp | GrammarToken | Array<RegExp | GrammarToken>>}\n * @property {Grammar} [rest] An optional grammar object that will be appended to this grammar.\n * @global\n * @public\n */\n\n/**\n * A function which will invoked after an element was successfully highlighted.\n *\n * @callback HighlightCallback\n * @param {Element} element The element successfully highlighted.\n * @returns {void}\n * @global\n * @public\n */\n\n/**\n * @callback HookCallback\n * @param {Object<string, any>} env The environment variables of the hook.\n * @returns {void}\n * @global\n * @public\n */", "map": {"version": 3, "names": ["_self", "window", "WorkerGlobalScope", "self", "Prism", "lang", "uniqueId", "plainTextGrammar", "_", "manual", "disableWorkerMessageHandler", "util", "encode", "tokens", "Token", "type", "content", "alias", "Array", "isArray", "map", "replace", "o", "Object", "prototype", "toString", "call", "slice", "objId", "obj", "defineProperty", "value", "clone", "deepClone", "visited", "id", "key", "hasOwnProperty", "for<PERSON>ach", "v", "i", "getLanguage", "element", "m", "exec", "className", "toLowerCase", "parentElement", "setLanguage", "language", "RegExp", "classList", "add", "currentScript", "document", "Error", "err", "src", "stack", "scripts", "getElementsByTagName", "isActive", "defaultActivation", "no", "contains", "languages", "plain", "plaintext", "text", "txt", "extend", "redef", "insertBefore", "inside", "before", "insert", "root", "grammar", "ret", "token", "newToken", "old", "DFS", "callback", "property", "propertyType", "plugins", "highlightAll", "async", "highlightAllUnder", "container", "env", "selector", "hooks", "run", "elements", "apply", "querySelectorAll", "highlightElement", "parent", "nodeName", "code", "textContent", "insertHighlightedCode", "highlightedCode", "innerHTML", "hasAttribute", "setAttribute", "Worker", "worker", "filename", "onmessage", "evt", "data", "postMessage", "JSON", "stringify", "immediateClose", "highlight", "tokenize", "rest", "tokenList", "LinkedList", "addAfter", "head", "matchGrammar", "toArray", "all", "name", "push", "callbacks", "length", "matchedStr", "s", "e", "tag", "classes", "attributes", "aliases", "join", "matchPattern", "pattern", "pos", "lookbehind", "lastIndex", "match", "lookbehindLength", "index", "startNode", "startPos", "rematch", "patterns", "j", "cause", "patternObj", "greedy", "global", "flags", "source", "currentNode", "next", "tail", "reach", "str", "removeCount", "from", "to", "p", "k", "matchStr", "after", "removeFrom", "prev", "<PERSON><PERSON><PERSON><PERSON>", "wrapped", "nested<PERSON><PERSON><PERSON>", "list", "node", "newNode", "count", "array", "addEventListener", "message", "parse", "close", "script", "highlightAutomatically<PERSON>allback", "readyState", "defer", "requestAnimationFrame", "setTimeout", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/refractor/node_modules/prismjs/components/prism-core.js"], "sourcesContent": ["/// <reference lib=\"WebWorker\"/>\n\nvar _self = (typeof window !== 'undefined')\n\t? window   // if in browser\n\t: (\n\t\t(typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope)\n\t\t\t? self // if in worker\n\t\t\t: {}   // if in node js\n\t);\n\n/**\n * Prism: Lightweight, robust, elegant syntax highlighting\n *\n * @license MIT <https://opensource.org/licenses/MIT>\n * <AUTHOR> Verou <https://lea.verou.me>\n * @namespace\n * @public\n */\nvar Prism = (function (_self) {\n\n\t// Private helper vars\n\tvar lang = /(?:^|\\s)lang(?:uage)?-([\\w-]+)(?=\\s|$)/i;\n\tvar uniqueId = 0;\n\n\t// The grammar object for plaintext\n\tvar plainTextGrammar = {};\n\n\n\tvar _ = {\n\t\t/**\n\t\t * By default, Prism will attempt to highlight all code elements (by calling {@link Prism.highlightAll}) on the\n\t\t * current page after the page finished loading. This might be a problem if e.g. you wanted to asynchronously load\n\t\t * additional languages or plugins yourself.\n\t\t *\n\t\t * By setting this value to `true`, Prism will not automatically highlight all code elements on the page.\n\t\t *\n\t\t * You obviously have to change this value before the automatic highlighting started. To do this, you can add an\n\t\t * empty Prism object into the global scope before loading the Prism script like this:\n\t\t *\n\t\t * ```js\n\t\t * window.Prism = window.Prism || {};\n\t\t * Prism.manual = true;\n\t\t * // add a new <script> to load Prism's script\n\t\t * ```\n\t\t *\n\t\t * @default false\n\t\t * @type {boolean}\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tmanual: _self.Prism && _self.Prism.manual,\n\t\t/**\n\t\t * By default, if Prism is in a web worker, it assumes that it is in a worker it created itself, so it uses\n\t\t * `addEventListener` to communicate with its parent instance. However, if you're using Prism manually in your\n\t\t * own worker, you don't want it to do this.\n\t\t *\n\t\t * By setting this value to `true`, Prism will not add its own listeners to the worker.\n\t\t *\n\t\t * You obviously have to change this value before Prism executes. To do this, you can add an\n\t\t * empty Prism object into the global scope before loading the Prism script like this:\n\t\t *\n\t\t * ```js\n\t\t * window.Prism = window.Prism || {};\n\t\t * Prism.disableWorkerMessageHandler = true;\n\t\t * // Load Prism's script\n\t\t * ```\n\t\t *\n\t\t * @default false\n\t\t * @type {boolean}\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tdisableWorkerMessageHandler: _self.Prism && _self.Prism.disableWorkerMessageHandler,\n\n\t\t/**\n\t\t * A namespace for utility methods.\n\t\t *\n\t\t * All function in this namespace that are not explicitly marked as _public_ are for __internal use only__ and may\n\t\t * change or disappear at any time.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t */\n\t\tutil: {\n\t\t\tencode: function encode(tokens) {\n\t\t\t\tif (tokens instanceof Token) {\n\t\t\t\t\treturn new Token(tokens.type, encode(tokens.content), tokens.alias);\n\t\t\t\t} else if (Array.isArray(tokens)) {\n\t\t\t\t\treturn tokens.map(encode);\n\t\t\t\t} else {\n\t\t\t\t\treturn tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\\u00a0/g, ' ');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the name of the type of the given value.\n\t\t\t *\n\t\t\t * @param {any} o\n\t\t\t * @returns {string}\n\t\t\t * @example\n\t\t\t * type(null)      === 'Null'\n\t\t\t * type(undefined) === 'Undefined'\n\t\t\t * type(123)       === 'Number'\n\t\t\t * type('foo')     === 'String'\n\t\t\t * type(true)      === 'Boolean'\n\t\t\t * type([1, 2])    === 'Array'\n\t\t\t * type({})        === 'Object'\n\t\t\t * type(String)    === 'Function'\n\t\t\t * type(/abc+/)    === 'RegExp'\n\t\t\t */\n\t\t\ttype: function (o) {\n\t\t\t\treturn Object.prototype.toString.call(o).slice(8, -1);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns a unique number for the given object. Later calls will still return the same number.\n\t\t\t *\n\t\t\t * @param {Object} obj\n\t\t\t * @returns {number}\n\t\t\t */\n\t\t\tobjId: function (obj) {\n\t\t\t\tif (!obj['__id']) {\n\t\t\t\t\tObject.defineProperty(obj, '__id', { value: ++uniqueId });\n\t\t\t\t}\n\t\t\t\treturn obj['__id'];\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Creates a deep clone of the given object.\n\t\t\t *\n\t\t\t * The main intended use of this function is to clone language definitions.\n\t\t\t *\n\t\t\t * @param {T} o\n\t\t\t * @param {Record<number, any>} [visited]\n\t\t\t * @returns {T}\n\t\t\t * @template T\n\t\t\t */\n\t\t\tclone: function deepClone(o, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar clone; var id;\n\t\t\t\tswitch (_.util.type(o)) {\n\t\t\t\t\tcase 'Object':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = /** @type {Record<string, any>} */ ({});\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\tfor (var key in o) {\n\t\t\t\t\t\t\tif (o.hasOwnProperty(key)) {\n\t\t\t\t\t\t\t\tclone[key] = deepClone(o[key], visited);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tcase 'Array':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = [];\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\t(/** @type {Array} */(/** @type {any} */(o))).forEach(function (v, i) {\n\t\t\t\t\t\t\tclone[i] = deepClone(v, visited);\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn o;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the Prism language of the given element set by a `language-xxxx` or `lang-xxxx` class.\n\t\t\t *\n\t\t\t * If no language is set for the element or the element is `null` or `undefined`, `none` will be returned.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @returns {string}\n\t\t\t */\n\t\t\tgetLanguage: function (element) {\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar m = lang.exec(element.className);\n\t\t\t\t\tif (m) {\n\t\t\t\t\t\treturn m[1].toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn 'none';\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Sets the Prism `language-xxxx` class of the given element.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} language\n\t\t\t * @returns {void}\n\t\t\t */\n\t\t\tsetLanguage: function (element, language) {\n\t\t\t\t// remove all `language-xxxx` classes\n\t\t\t\t// (this might leave behind a leading space)\n\t\t\t\telement.className = element.className.replace(RegExp(lang, 'gi'), '');\n\n\t\t\t\t// add the new `language-xxxx` class\n\t\t\t\t// (using `classList` will automatically clean up spaces for us)\n\t\t\t\telement.classList.add('language-' + language);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the script element that is currently executing.\n\t\t\t *\n\t\t\t * This does __not__ work for line script element.\n\t\t\t *\n\t\t\t * @returns {HTMLScriptElement | null}\n\t\t\t */\n\t\t\tcurrentScript: function () {\n\t\t\t\tif (typeof document === 'undefined') {\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t\tif ('currentScript' in document && 1 < 2 /* hack to trip TS' flow analysis */) {\n\t\t\t\t\treturn /** @type {any} */ (document.currentScript);\n\t\t\t\t}\n\n\t\t\t\t// IE11 workaround\n\t\t\t\t// we'll get the src of the current script by parsing IE11's error stack trace\n\t\t\t\t// this will not work for inline scripts\n\n\t\t\t\ttry {\n\t\t\t\t\tthrow new Error();\n\t\t\t\t} catch (err) {\n\t\t\t\t\t// Get file src url from stack. Specifically works with the format of stack traces in IE.\n\t\t\t\t\t// A stack will look like this:\n\t\t\t\t\t//\n\t\t\t\t\t// Error\n\t\t\t\t\t//    at _.util.currentScript (http://localhost/components/prism-core.js:119:5)\n\t\t\t\t\t//    at Global code (http://localhost/components/prism-core.js:606:1)\n\n\t\t\t\t\tvar src = (/at [^(\\r\\n]*\\((.*):[^:]+:[^:]+\\)$/i.exec(err.stack) || [])[1];\n\t\t\t\t\tif (src) {\n\t\t\t\t\t\tvar scripts = document.getElementsByTagName('script');\n\t\t\t\t\t\tfor (var i in scripts) {\n\t\t\t\t\t\t\tif (scripts[i].src == src) {\n\t\t\t\t\t\t\t\treturn scripts[i];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns whether a given class is active for `element`.\n\t\t\t *\n\t\t\t * The class can be activated if `element` or one of its ancestors has the given class and it can be deactivated\n\t\t\t * if `element` or one of its ancestors has the negated version of the given class. The _negated version_ of the\n\t\t\t * given class is just the given class with a `no-` prefix.\n\t\t\t *\n\t\t\t * Whether the class is active is determined by the closest ancestor of `element` (where `element` itself is\n\t\t\t * closest ancestor) that has the given class or the negated version of it. If neither `element` nor any of its\n\t\t\t * ancestors have the given class or the negated version of it, then the default activation will be returned.\n\t\t\t *\n\t\t\t * In the paradoxical situation where the closest ancestor contains __both__ the given class and the negated\n\t\t\t * version of it, the class is considered active.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} className\n\t\t\t * @param {boolean} [defaultActivation=false]\n\t\t\t * @returns {boolean}\n\t\t\t */\n\t\t\tisActive: function (element, className, defaultActivation) {\n\t\t\t\tvar no = 'no-' + className;\n\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar classList = element.classList;\n\t\t\t\t\tif (classList.contains(className)) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\tif (classList.contains(no)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn !!defaultActivation;\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * This namespace contains all currently loaded languages and the some helper functions to create and modify languages.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tlanguages: {\n\t\t\t/**\n\t\t\t * The grammar for plain, unformatted text.\n\t\t\t */\n\t\t\tplain: plainTextGrammar,\n\t\t\tplaintext: plainTextGrammar,\n\t\t\ttext: plainTextGrammar,\n\t\t\ttxt: plainTextGrammar,\n\n\t\t\t/**\n\t\t\t * Creates a deep copy of the language with the given id and appends the given tokens.\n\t\t\t *\n\t\t\t * If a token in `redef` also appears in the copied language, then the existing token in the copied language\n\t\t\t * will be overwritten at its original position.\n\t\t\t *\n\t\t\t * ## Best practices\n\t\t\t *\n\t\t\t * Since the position of overwriting tokens (token in `redef` that overwrite tokens in the copied language)\n\t\t\t * doesn't matter, they can technically be in any order. However, this can be confusing to others that trying to\n\t\t\t * understand the language definition because, normally, the order of tokens matters in Prism grammars.\n\t\t\t *\n\t\t\t * Therefore, it is encouraged to order overwriting tokens according to the positions of the overwritten tokens.\n\t\t\t * Furthermore, all non-overwriting tokens should be placed after the overwriting ones.\n\t\t\t *\n\t\t\t * @param {string} id The id of the language to extend. This has to be a key in `Prism.languages`.\n\t\t\t * @param {Grammar} redef The new tokens to append.\n\t\t\t * @returns {Grammar} The new language created.\n\t\t\t * @public\n\t\t\t * @example\n\t\t\t * Prism.languages['css-with-colors'] = Prism.languages.extend('css', {\n\t\t\t *     // Prism.languages.css already has a 'comment' token, so this token will overwrite CSS' 'comment' token\n\t\t\t *     // at its original position\n\t\t\t *     'comment': { ... },\n\t\t\t *     // CSS doesn't have a 'color' token, so this token will be appended\n\t\t\t *     'color': /\\b(?:red|green|blue)\\b/\n\t\t\t * });\n\t\t\t */\n\t\t\textend: function (id, redef) {\n\t\t\t\tvar lang = _.util.clone(_.languages[id]);\n\n\t\t\t\tfor (var key in redef) {\n\t\t\t\t\tlang[key] = redef[key];\n\t\t\t\t}\n\n\t\t\t\treturn lang;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Inserts tokens _before_ another token in a language definition or any other grammar.\n\t\t\t *\n\t\t\t * ## Usage\n\t\t\t *\n\t\t\t * This helper method makes it easy to modify existing languages. For example, the CSS language definition\n\t\t\t * not only defines CSS highlighting for CSS documents, but also needs to define highlighting for CSS embedded\n\t\t\t * in HTML through `<style>` elements. To do this, it needs to modify `Prism.languages.markup` and add the\n\t\t\t * appropriate tokens. However, `Prism.languages.markup` is a regular JavaScript object literal, so if you do\n\t\t\t * this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.markup.style = {\n\t\t\t *     // token\n\t\t\t * };\n\t\t\t * ```\n\t\t\t *\n\t\t\t * then the `style` token will be added (and processed) at the end. `insertBefore` allows you to insert tokens\n\t\t\t * before existing tokens. For the CSS example above, you would use it like this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'cdata', {\n\t\t\t *     'style': {\n\t\t\t *         // token\n\t\t\t *     }\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Special cases\n\t\t\t *\n\t\t\t * If the grammars of `inside` and `insert` have tokens with the same name, the tokens in `inside`'s grammar\n\t\t\t * will be ignored.\n\t\t\t *\n\t\t\t * This behavior can be used to insert tokens after `before`:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'comment', {\n\t\t\t *     'comment': Prism.languages.markup.comment,\n\t\t\t *     // tokens after 'comment'\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Limitations\n\t\t\t *\n\t\t\t * The main problem `insertBefore` has to solve is iteration order. Since ES2015, the iteration order for object\n\t\t\t * properties is guaranteed to be the insertion order (except for integer keys) but some browsers behave\n\t\t\t * differently when keys are deleted and re-inserted. So `insertBefore` can't be implemented by temporarily\n\t\t\t * deleting properties which is necessary to insert at arbitrary positions.\n\t\t\t *\n\t\t\t * To solve this problem, `insertBefore` doesn't actually insert the given tokens into the target object.\n\t\t\t * Instead, it will create a new object and replace all references to the target object with the new one. This\n\t\t\t * can be done without temporarily deleting properties, so the iteration order is well-defined.\n\t\t\t *\n\t\t\t * However, only references that can be reached from `Prism.languages` or `insert` will be replaced. I.e. if\n\t\t\t * you hold the target object in a variable, then the value of the variable will not change.\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * var oldMarkup = Prism.languages.markup;\n\t\t\t * var newMarkup = Prism.languages.insertBefore('markup', 'comment', { ... });\n\t\t\t *\n\t\t\t * assert(oldMarkup !== Prism.languages.markup);\n\t\t\t * assert(newMarkup === Prism.languages.markup);\n\t\t\t * ```\n\t\t\t *\n\t\t\t * @param {string} inside The property of `root` (e.g. a language id in `Prism.languages`) that contains the\n\t\t\t * object to be modified.\n\t\t\t * @param {string} before The key to insert before.\n\t\t\t * @param {Grammar} insert An object containing the key-value pairs to be inserted.\n\t\t\t * @param {Object<string, any>} [root] The object containing `inside`, i.e. the object that contains the\n\t\t\t * object to be modified.\n\t\t\t *\n\t\t\t * Defaults to `Prism.languages`.\n\t\t\t * @returns {Grammar} The new grammar object.\n\t\t\t * @public\n\t\t\t */\n\t\t\tinsertBefore: function (inside, before, insert, root) {\n\t\t\t\troot = root || /** @type {any} */ (_.languages);\n\t\t\t\tvar grammar = root[inside];\n\t\t\t\t/** @type {Grammar} */\n\t\t\t\tvar ret = {};\n\n\t\t\t\tfor (var token in grammar) {\n\t\t\t\t\tif (grammar.hasOwnProperty(token)) {\n\n\t\t\t\t\t\tif (token == before) {\n\t\t\t\t\t\t\tfor (var newToken in insert) {\n\t\t\t\t\t\t\t\tif (insert.hasOwnProperty(newToken)) {\n\t\t\t\t\t\t\t\t\tret[newToken] = insert[newToken];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Do not insert token which also occur in insert. See #1525\n\t\t\t\t\t\tif (!insert.hasOwnProperty(token)) {\n\t\t\t\t\t\t\tret[token] = grammar[token];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar old = root[inside];\n\t\t\t\troot[inside] = ret;\n\n\t\t\t\t// Update references in other language definitions\n\t\t\t\t_.languages.DFS(_.languages, function (key, value) {\n\t\t\t\t\tif (value === old && key != inside) {\n\t\t\t\t\t\tthis[key] = ret;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn ret;\n\t\t\t},\n\n\t\t\t// Traverse a language definition with Depth First Search\n\t\t\tDFS: function DFS(o, callback, type, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar objId = _.util.objId;\n\n\t\t\t\tfor (var i in o) {\n\t\t\t\t\tif (o.hasOwnProperty(i)) {\n\t\t\t\t\t\tcallback.call(o, i, o[i], type || i);\n\n\t\t\t\t\t\tvar property = o[i];\n\t\t\t\t\t\tvar propertyType = _.util.type(property);\n\n\t\t\t\t\t\tif (propertyType === 'Object' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, null, visited);\n\t\t\t\t\t\t} else if (propertyType === 'Array' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, i, visited);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tplugins: {},\n\n\t\t/**\n\t\t * This is the most high-level function in Prism’s API.\n\t\t * It fetches all the elements that have a `.language-xxxx` class and then calls {@link Prism.highlightElement} on\n\t\t * each one of them.\n\t\t *\n\t\t * This is equivalent to `Prism.highlightAllUnder(document, async, callback)`.\n\t\t *\n\t\t * @param {boolean} [async=false] Same as in {@link Prism.highlightAllUnder}.\n\t\t * @param {HighlightCallback} [callback] Same as in {@link Prism.highlightAllUnder}.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightAll: function (async, callback) {\n\t\t\t_.highlightAllUnder(document, async, callback);\n\t\t},\n\n\t\t/**\n\t\t * Fetches all the descendants of `container` that have a `.language-xxxx` class and then calls\n\t\t * {@link Prism.highlightElement} on each one of them.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-highlightall`\n\t\t * 2. `before-all-elements-highlight`\n\t\t * 3. All hooks of {@link Prism.highlightElement} for each element.\n\t\t *\n\t\t * @param {ParentNode} container The root element, whose descendants that have a `.language-xxxx` class will be highlighted.\n\t\t * @param {boolean} [async=false] Whether each element is to be highlighted asynchronously using Web Workers.\n\t\t * @param {HighlightCallback} [callback] An optional callback to be invoked on each element after its highlighting is done.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightAllUnder: function (container, async, callback) {\n\t\t\tvar env = {\n\t\t\t\tcallback: callback,\n\t\t\t\tcontainer: container,\n\t\t\t\tselector: 'code[class*=\"language-\"], [class*=\"language-\"] code, code[class*=\"lang-\"], [class*=\"lang-\"] code'\n\t\t\t};\n\n\t\t\t_.hooks.run('before-highlightall', env);\n\n\t\t\tenv.elements = Array.prototype.slice.apply(env.container.querySelectorAll(env.selector));\n\n\t\t\t_.hooks.run('before-all-elements-highlight', env);\n\n\t\t\tfor (var i = 0, element; (element = env.elements[i++]);) {\n\t\t\t\t_.highlightElement(element, async === true, env.callback);\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Highlights the code inside a single element.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-sanity-check`\n\t\t * 2. `before-highlight`\n\t\t * 3. All hooks of {@link Prism.highlight}. These hooks will be run by an asynchronous worker if `async` is `true`.\n\t\t * 4. `before-insert`\n\t\t * 5. `after-highlight`\n\t\t * 6. `complete`\n\t\t *\n\t\t * Some the above hooks will be skipped if the element doesn't contain any text or there is no grammar loaded for\n\t\t * the element's language.\n\t\t *\n\t\t * @param {Element} element The element containing the code.\n\t\t * It must have a class of `language-xxxx` to be processed, where `xxxx` is a valid language identifier.\n\t\t * @param {boolean} [async=false] Whether the element is to be highlighted asynchronously using Web Workers\n\t\t * to improve performance and avoid blocking the UI when highlighting very large chunks of code. This option is\n\t\t * [disabled by default](https://prismjs.com/faq.html#why-is-asynchronous-highlighting-disabled-by-default).\n\t\t *\n\t\t * Note: All language definitions required to highlight the code must be included in the main `prism.js` file for\n\t\t * asynchronous highlighting to work. You can build your own bundle on the\n\t\t * [Download page](https://prismjs.com/download.html).\n\t\t * @param {HighlightCallback} [callback] An optional callback to be invoked after the highlighting is done.\n\t\t * Mostly useful when `async` is `true`, since in that case, the highlighting is done asynchronously.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightElement: function (element, async, callback) {\n\t\t\t// Find language\n\t\t\tvar language = _.util.getLanguage(element);\n\t\t\tvar grammar = _.languages[language];\n\n\t\t\t// Set language on the element, if not present\n\t\t\t_.util.setLanguage(element, language);\n\n\t\t\t// Set language on the parent, for styling\n\t\t\tvar parent = element.parentElement;\n\t\t\tif (parent && parent.nodeName.toLowerCase() === 'pre') {\n\t\t\t\t_.util.setLanguage(parent, language);\n\t\t\t}\n\n\t\t\tvar code = element.textContent;\n\n\t\t\tvar env = {\n\t\t\t\telement: element,\n\t\t\t\tlanguage: language,\n\t\t\t\tgrammar: grammar,\n\t\t\t\tcode: code\n\t\t\t};\n\n\t\t\tfunction insertHighlightedCode(highlightedCode) {\n\t\t\t\tenv.highlightedCode = highlightedCode;\n\n\t\t\t\t_.hooks.run('before-insert', env);\n\n\t\t\t\tenv.element.innerHTML = env.highlightedCode;\n\n\t\t\t\t_.hooks.run('after-highlight', env);\n\t\t\t\t_.hooks.run('complete', env);\n\t\t\t\tcallback && callback.call(env.element);\n\t\t\t}\n\n\t\t\t_.hooks.run('before-sanity-check', env);\n\n\t\t\t// plugins may change/add the parent/element\n\t\t\tparent = env.element.parentElement;\n\t\t\tif (parent && parent.nodeName.toLowerCase() === 'pre' && !parent.hasAttribute('tabindex')) {\n\t\t\t\tparent.setAttribute('tabindex', '0');\n\t\t\t}\n\n\t\t\tif (!env.code) {\n\t\t\t\t_.hooks.run('complete', env);\n\t\t\t\tcallback && callback.call(env.element);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t_.hooks.run('before-highlight', env);\n\n\t\t\tif (!env.grammar) {\n\t\t\t\tinsertHighlightedCode(_.util.encode(env.code));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (async && _self.Worker) {\n\t\t\t\tvar worker = new Worker(_.filename);\n\n\t\t\t\tworker.onmessage = function (evt) {\n\t\t\t\t\tinsertHighlightedCode(evt.data);\n\t\t\t\t};\n\n\t\t\t\tworker.postMessage(JSON.stringify({\n\t\t\t\t\tlanguage: env.language,\n\t\t\t\t\tcode: env.code,\n\t\t\t\t\timmediateClose: true\n\t\t\t\t}));\n\t\t\t} else {\n\t\t\t\tinsertHighlightedCode(_.highlight(env.code, env.grammar, env.language));\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Low-level function, only use if you know what you’re doing. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns a string with the HTML produced.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-tokenize`\n\t\t * 2. `after-tokenize`\n\t\t * 3. `wrap`: On each {@link Token}.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @param {string} language The name of the language definition passed to `grammar`.\n\t\t * @returns {string} The highlighted HTML.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * Prism.highlight('var foo = true;', Prism.languages.javascript, 'javascript');\n\t\t */\n\t\thighlight: function (text, grammar, language) {\n\t\t\tvar env = {\n\t\t\t\tcode: text,\n\t\t\t\tgrammar: grammar,\n\t\t\t\tlanguage: language\n\t\t\t};\n\t\t\t_.hooks.run('before-tokenize', env);\n\t\t\tif (!env.grammar) {\n\t\t\t\tthrow new Error('The language \"' + env.language + '\" has no grammar.');\n\t\t\t}\n\t\t\tenv.tokens = _.tokenize(env.code, env.grammar);\n\t\t\t_.hooks.run('after-tokenize', env);\n\t\t\treturn Token.stringify(_.util.encode(env.tokens), env.language);\n\t\t},\n\n\t\t/**\n\t\t * This is the heart of Prism, and the most low-level function you can use. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns an array with the tokenized code.\n\t\t *\n\t\t * When the language definition includes nested tokens, the function is called recursively on each of these tokens.\n\t\t *\n\t\t * This method could be useful in other contexts as well, as a very crude parser.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @returns {TokenStream} An array of strings and tokens, a token stream.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * let code = `var foo = 0;`;\n\t\t * let tokens = Prism.tokenize(code, Prism.languages.javascript);\n\t\t * tokens.forEach(token => {\n\t\t *     if (token instanceof Prism.Token && token.type === 'number') {\n\t\t *         console.log(`Found numeric literal: ${token.content}`);\n\t\t *     }\n\t\t * });\n\t\t */\n\t\ttokenize: function (text, grammar) {\n\t\t\tvar rest = grammar.rest;\n\t\t\tif (rest) {\n\t\t\t\tfor (var token in rest) {\n\t\t\t\t\tgrammar[token] = rest[token];\n\t\t\t\t}\n\n\t\t\t\tdelete grammar.rest;\n\t\t\t}\n\n\t\t\tvar tokenList = new LinkedList();\n\t\t\taddAfter(tokenList, tokenList.head, text);\n\n\t\t\tmatchGrammar(text, tokenList, grammar, tokenList.head, 0);\n\n\t\t\treturn toArray(tokenList);\n\t\t},\n\n\t\t/**\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thooks: {\n\t\t\tall: {},\n\n\t\t\t/**\n\t\t\t * Adds the given callback to the list of callbacks for the given hook.\n\t\t\t *\n\t\t\t * The callback will be invoked when the hook it is registered for is run.\n\t\t\t * Hooks are usually directly run by a highlight function but you can also run hooks yourself.\n\t\t\t *\n\t\t\t * One callback function can be registered to multiple hooks and the same hook multiple times.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {HookCallback} callback The callback function which is given environment variables.\n\t\t\t * @public\n\t\t\t */\n\t\t\tadd: function (name, callback) {\n\t\t\t\tvar hooks = _.hooks.all;\n\n\t\t\t\thooks[name] = hooks[name] || [];\n\n\t\t\t\thooks[name].push(callback);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Runs a hook invoking all registered callbacks with the given environment variables.\n\t\t\t *\n\t\t\t * Callbacks will be invoked synchronously and in the order in which they were registered.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n\t\t\t * @public\n\t\t\t */\n\t\t\trun: function (name, env) {\n\t\t\t\tvar callbacks = _.hooks.all[name];\n\n\t\t\t\tif (!callbacks || !callbacks.length) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tfor (var i = 0, callback; (callback = callbacks[i++]);) {\n\t\t\t\t\tcallback(env);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tToken: Token\n\t};\n\t_self.Prism = _;\n\n\n\t// Typescript note:\n\t// The following can be used to import the Token type in JSDoc:\n\t//\n\t//   @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n\n\t/**\n\t * Creates a new token.\n\t *\n\t * @param {string} type See {@link Token#type type}\n\t * @param {string | TokenStream} content See {@link Token#content content}\n\t * @param {string|string[]} [alias] The alias(es) of the token.\n\t * @param {string} [matchedStr=\"\"] A copy of the full string this token was created from.\n\t * @class\n\t * @global\n\t * @public\n\t */\n\tfunction Token(type, content, alias, matchedStr) {\n\t\t/**\n\t\t * The type of the token.\n\t\t *\n\t\t * This is usually the key of a pattern in a {@link Grammar}.\n\t\t *\n\t\t * @type {string}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.type = type;\n\t\t/**\n\t\t * The strings or tokens contained by this token.\n\t\t *\n\t\t * This will be a token stream if the pattern matched also defined an `inside` grammar.\n\t\t *\n\t\t * @type {string | TokenStream}\n\t\t * @public\n\t\t */\n\t\tthis.content = content;\n\t\t/**\n\t\t * The alias(es) of the token.\n\t\t *\n\t\t * @type {string|string[]}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.alias = alias;\n\t\t// Copy of the full string this token was created from\n\t\tthis.length = (matchedStr || '').length | 0;\n\t}\n\n\t/**\n\t * A token stream is an array of strings and {@link Token Token} objects.\n\t *\n\t * Token streams have to fulfill a few properties that are assumed by most functions (mostly internal ones) that process\n\t * them.\n\t *\n\t * 1. No adjacent strings.\n\t * 2. No empty strings.\n\t *\n\t *    The only exception here is the token stream that only contains the empty string and nothing else.\n\t *\n\t * @typedef {Array<string | Token>} TokenStream\n\t * @global\n\t * @public\n\t */\n\n\t/**\n\t * Converts the given token or token stream to an HTML representation.\n\t *\n\t * The following hooks will be run:\n\t * 1. `wrap`: On each {@link Token}.\n\t *\n\t * @param {string | Token | TokenStream} o The token or token stream to be converted.\n\t * @param {string} language The name of current language.\n\t * @returns {string} The HTML representation of the token or token stream.\n\t * @memberof Token\n\t * @static\n\t */\n\tToken.stringify = function stringify(o, language) {\n\t\tif (typeof o == 'string') {\n\t\t\treturn o;\n\t\t}\n\t\tif (Array.isArray(o)) {\n\t\t\tvar s = '';\n\t\t\to.forEach(function (e) {\n\t\t\t\ts += stringify(e, language);\n\t\t\t});\n\t\t\treturn s;\n\t\t}\n\n\t\tvar env = {\n\t\t\ttype: o.type,\n\t\t\tcontent: stringify(o.content, language),\n\t\t\ttag: 'span',\n\t\t\tclasses: ['token', o.type],\n\t\t\tattributes: {},\n\t\t\tlanguage: language\n\t\t};\n\n\t\tvar aliases = o.alias;\n\t\tif (aliases) {\n\t\t\tif (Array.isArray(aliases)) {\n\t\t\t\tArray.prototype.push.apply(env.classes, aliases);\n\t\t\t} else {\n\t\t\t\tenv.classes.push(aliases);\n\t\t\t}\n\t\t}\n\n\t\t_.hooks.run('wrap', env);\n\n\t\tvar attributes = '';\n\t\tfor (var name in env.attributes) {\n\t\t\tattributes += ' ' + name + '=\"' + (env.attributes[name] || '').replace(/\"/g, '&quot;') + '\"';\n\t\t}\n\n\t\treturn '<' + env.tag + ' class=\"' + env.classes.join(' ') + '\"' + attributes + '>' + env.content + '</' + env.tag + '>';\n\t};\n\n\t/**\n\t * @param {RegExp} pattern\n\t * @param {number} pos\n\t * @param {string} text\n\t * @param {boolean} lookbehind\n\t * @returns {RegExpExecArray | null}\n\t */\n\tfunction matchPattern(pattern, pos, text, lookbehind) {\n\t\tpattern.lastIndex = pos;\n\t\tvar match = pattern.exec(text);\n\t\tif (match && lookbehind && match[1]) {\n\t\t\t// change the match to remove the text matched by the Prism lookbehind group\n\t\t\tvar lookbehindLength = match[1].length;\n\t\t\tmatch.index += lookbehindLength;\n\t\t\tmatch[0] = match[0].slice(lookbehindLength);\n\t\t}\n\t\treturn match;\n\t}\n\n\t/**\n\t * @param {string} text\n\t * @param {LinkedList<string | Token>} tokenList\n\t * @param {any} grammar\n\t * @param {LinkedListNode<string | Token>} startNode\n\t * @param {number} startPos\n\t * @param {RematchOptions} [rematch]\n\t * @returns {void}\n\t * @private\n\t *\n\t * @typedef RematchOptions\n\t * @property {string} cause\n\t * @property {number} reach\n\t */\n\tfunction matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n\t\tfor (var token in grammar) {\n\t\t\tif (!grammar.hasOwnProperty(token) || !grammar[token]) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tvar patterns = grammar[token];\n\t\t\tpatterns = Array.isArray(patterns) ? patterns : [patterns];\n\n\t\t\tfor (var j = 0; j < patterns.length; ++j) {\n\t\t\t\tif (rematch && rematch.cause == token + ',' + j) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar patternObj = patterns[j];\n\t\t\t\tvar inside = patternObj.inside;\n\t\t\t\tvar lookbehind = !!patternObj.lookbehind;\n\t\t\t\tvar greedy = !!patternObj.greedy;\n\t\t\t\tvar alias = patternObj.alias;\n\n\t\t\t\tif (greedy && !patternObj.pattern.global) {\n\t\t\t\t\t// Without the global flag, lastIndex won't work\n\t\t\t\t\tvar flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n\t\t\t\t\tpatternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');\n\t\t\t\t}\n\n\t\t\t\t/** @type {RegExp} */\n\t\t\t\tvar pattern = patternObj.pattern || patternObj;\n\n\t\t\t\tfor ( // iterate the token list and keep track of the current token/string position\n\t\t\t\t\tvar currentNode = startNode.next, pos = startPos;\n\t\t\t\t\tcurrentNode !== tokenList.tail;\n\t\t\t\t\tpos += currentNode.value.length, currentNode = currentNode.next\n\t\t\t\t) {\n\n\t\t\t\t\tif (rematch && pos >= rematch.reach) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar str = currentNode.value;\n\n\t\t\t\t\tif (tokenList.length > text.length) {\n\t\t\t\t\t\t// Something went terribly wrong, ABORT, ABORT!\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (str instanceof Token) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeCount = 1; // this is the to parameter of removeBetween\n\t\t\t\t\tvar match;\n\n\t\t\t\t\tif (greedy) {\n\t\t\t\t\t\tmatch = matchPattern(pattern, pos, text, lookbehind);\n\t\t\t\t\t\tif (!match || match.index >= text.length) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvar from = match.index;\n\t\t\t\t\t\tvar to = match.index + match[0].length;\n\t\t\t\t\t\tvar p = pos;\n\n\t\t\t\t\t\t// find the node that contains the match\n\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\twhile (from >= p) {\n\t\t\t\t\t\t\tcurrentNode = currentNode.next;\n\t\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// adjust pos (and p)\n\t\t\t\t\t\tp -= currentNode.value.length;\n\t\t\t\t\t\tpos = p;\n\n\t\t\t\t\t\t// the current node is a Token, then the match starts inside another Token, which is invalid\n\t\t\t\t\t\tif (currentNode.value instanceof Token) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// find the last node which is affected by this match\n\t\t\t\t\t\tfor (\n\t\t\t\t\t\t\tvar k = currentNode;\n\t\t\t\t\t\t\tk !== tokenList.tail && (p < to || typeof k.value === 'string');\n\t\t\t\t\t\t\tk = k.next\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tremoveCount++;\n\t\t\t\t\t\t\tp += k.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tremoveCount--;\n\n\t\t\t\t\t\t// replace with the new match\n\t\t\t\t\t\tstr = text.slice(pos, p);\n\t\t\t\t\t\tmatch.index -= pos;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmatch = matchPattern(pattern, 0, str, lookbehind);\n\t\t\t\t\t\tif (!match) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// eslint-disable-next-line no-redeclare\n\t\t\t\t\tvar from = match.index;\n\t\t\t\t\tvar matchStr = match[0];\n\t\t\t\t\tvar before = str.slice(0, from);\n\t\t\t\t\tvar after = str.slice(from + matchStr.length);\n\n\t\t\t\t\tvar reach = pos + str.length;\n\t\t\t\t\tif (rematch && reach > rematch.reach) {\n\t\t\t\t\t\trematch.reach = reach;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeFrom = currentNode.prev;\n\n\t\t\t\t\tif (before) {\n\t\t\t\t\t\tremoveFrom = addAfter(tokenList, removeFrom, before);\n\t\t\t\t\t\tpos += before.length;\n\t\t\t\t\t}\n\n\t\t\t\t\tremoveRange(tokenList, removeFrom, removeCount);\n\n\t\t\t\t\tvar wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n\t\t\t\t\tcurrentNode = addAfter(tokenList, removeFrom, wrapped);\n\n\t\t\t\t\tif (after) {\n\t\t\t\t\t\taddAfter(tokenList, currentNode, after);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (removeCount > 1) {\n\t\t\t\t\t\t// at least one Token object was removed, so we have to do some rematching\n\t\t\t\t\t\t// this can only happen if the current pattern is greedy\n\n\t\t\t\t\t\t/** @type {RematchOptions} */\n\t\t\t\t\t\tvar nestedRematch = {\n\t\t\t\t\t\t\tcause: token + ',' + j,\n\t\t\t\t\t\t\treach: reach\n\t\t\t\t\t\t};\n\t\t\t\t\t\tmatchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n\n\t\t\t\t\t\t// the reach might have been extended because of the rematching\n\t\t\t\t\t\tif (rematch && nestedRematch.reach > rematch.reach) {\n\t\t\t\t\t\t\trematch.reach = nestedRematch.reach;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @typedef LinkedListNode\n\t * @property {T} value\n\t * @property {LinkedListNode<T> | null} prev The previous node.\n\t * @property {LinkedListNode<T> | null} next The next node.\n\t * @template T\n\t * @private\n\t */\n\n\t/**\n\t * @template T\n\t * @private\n\t */\n\tfunction LinkedList() {\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar head = { value: null, prev: null, next: null };\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar tail = { value: null, prev: head, next: null };\n\t\thead.next = tail;\n\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.head = head;\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.tail = tail;\n\t\tthis.length = 0;\n\t}\n\n\t/**\n\t * Adds a new node with the given value to the list.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {T} value\n\t * @returns {LinkedListNode<T>} The added node.\n\t * @template T\n\t */\n\tfunction addAfter(list, node, value) {\n\t\t// assumes that node != list.tail && values.length >= 0\n\t\tvar next = node.next;\n\n\t\tvar newNode = { value: value, prev: node, next: next };\n\t\tnode.next = newNode;\n\t\tnext.prev = newNode;\n\t\tlist.length++;\n\n\t\treturn newNode;\n\t}\n\t/**\n\t * Removes `count` nodes after the given node. The given node will not be removed.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {number} count\n\t * @template T\n\t */\n\tfunction removeRange(list, node, count) {\n\t\tvar next = node.next;\n\t\tfor (var i = 0; i < count && next !== list.tail; i++) {\n\t\t\tnext = next.next;\n\t\t}\n\t\tnode.next = next;\n\t\tnext.prev = node;\n\t\tlist.length -= i;\n\t}\n\t/**\n\t * @param {LinkedList<T>} list\n\t * @returns {T[]}\n\t * @template T\n\t */\n\tfunction toArray(list) {\n\t\tvar array = [];\n\t\tvar node = list.head.next;\n\t\twhile (node !== list.tail) {\n\t\t\tarray.push(node.value);\n\t\t\tnode = node.next;\n\t\t}\n\t\treturn array;\n\t}\n\n\n\tif (!_self.document) {\n\t\tif (!_self.addEventListener) {\n\t\t\t// in Node.js\n\t\t\treturn _;\n\t\t}\n\n\t\tif (!_.disableWorkerMessageHandler) {\n\t\t\t// In worker\n\t\t\t_self.addEventListener('message', function (evt) {\n\t\t\t\tvar message = JSON.parse(evt.data);\n\t\t\t\tvar lang = message.language;\n\t\t\t\tvar code = message.code;\n\t\t\t\tvar immediateClose = message.immediateClose;\n\n\t\t\t\t_self.postMessage(_.highlight(code, _.languages[lang], lang));\n\t\t\t\tif (immediateClose) {\n\t\t\t\t\t_self.close();\n\t\t\t\t}\n\t\t\t}, false);\n\t\t}\n\n\t\treturn _;\n\t}\n\n\t// Get current script and highlight\n\tvar script = _.util.currentScript();\n\n\tif (script) {\n\t\t_.filename = script.src;\n\n\t\tif (script.hasAttribute('data-manual')) {\n\t\t\t_.manual = true;\n\t\t}\n\t}\n\n\tfunction highlightAutomaticallyCallback() {\n\t\tif (!_.manual) {\n\t\t\t_.highlightAll();\n\t\t}\n\t}\n\n\tif (!_.manual) {\n\t\t// If the document state is \"loading\", then we'll use DOMContentLoaded.\n\t\t// If the document state is \"interactive\" and the prism.js script is deferred, then we'll also use the\n\t\t// DOMContentLoaded event because there might be some plugins or languages which have also been deferred and they\n\t\t// might take longer one animation frame to execute which can create a race condition where only some plugins have\n\t\t// been loaded when Prism.highlightAll() is executed, depending on how fast resources are loaded.\n\t\t// See https://github.com/PrismJS/prism/issues/2102\n\t\tvar readyState = document.readyState;\n\t\tif (readyState === 'loading' || readyState === 'interactive' && script && script.defer) {\n\t\t\tdocument.addEventListener('DOMContentLoaded', highlightAutomaticallyCallback);\n\t\t} else {\n\t\t\tif (window.requestAnimationFrame) {\n\t\t\t\twindow.requestAnimationFrame(highlightAutomaticallyCallback);\n\t\t\t} else {\n\t\t\t\twindow.setTimeout(highlightAutomaticallyCallback, 16);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn _;\n\n}(_self));\n\nif (typeof module !== 'undefined' && module.exports) {\n\tmodule.exports = Prism;\n}\n\n// hack for components to work correctly in node.js\nif (typeof global !== 'undefined') {\n\tglobal.Prism = Prism;\n}\n\n// some additional documentation/types\n\n/**\n * The expansion of a simple `RegExp` literal to support additional properties.\n *\n * @typedef GrammarToken\n * @property {RegExp} pattern The regular expression of the token.\n * @property {boolean} [lookbehind=false] If `true`, then the first capturing group of `pattern` will (effectively)\n * behave as a lookbehind group meaning that the captured text will not be part of the matched text of the new token.\n * @property {boolean} [greedy=false] Whether the token is greedy.\n * @property {string|string[]} [alias] An optional alias or list of aliases.\n * @property {Grammar} [inside] The nested grammar of this token.\n *\n * The `inside` grammar will be used to tokenize the text value of each token of this kind.\n *\n * This can be used to make nested and even recursive language definitions.\n *\n * Note: This can cause infinite recursion. Be careful when you embed different languages or even the same language into\n * each another.\n * @global\n * @public\n */\n\n/**\n * @typedef Grammar\n * @type {Object<string, RegExp | GrammarToken | Array<RegExp | GrammarToken>>}\n * @property {Grammar} [rest] An optional grammar object that will be appended to this grammar.\n * @global\n * @public\n */\n\n/**\n * A function which will invoked after an element was successfully highlighted.\n *\n * @callback HighlightCallback\n * @param {Element} element The element successfully highlighted.\n * @returns {void}\n * @global\n * @public\n */\n\n/**\n * @callback HookCallback\n * @param {Object<string, any>} env The environment variables of the hook.\n * @returns {void}\n * @global\n * @public\n */\n"], "mappings": "AAAA;;AAEA,IAAIA,KAAK,GAAI,OAAOC,MAAM,KAAK,WAAW,GACvCA,MAAM,CAAG;AAAA,EAET,OAAOC,iBAAiB,KAAK,WAAW,IAAIC,IAAI,YAAYD,iBAAiB,GAC3EC,IAAI,CAAC;AAAA,EACL,CAAC,CAAC,CAAG;AACR;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,GAAI,UAAUJ,KAAK,EAAE;EAE7B;EACA,IAAIK,IAAI,GAAG,yCAAyC;EACpD,IAAIC,QAAQ,GAAG,CAAC;;EAEhB;EACA,IAAIC,gBAAgB,GAAG,CAAC,CAAC;EAGzB,IAAIC,CAAC,GAAG;IACP;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEC,MAAM,EAAET,KAAK,CAACI,KAAK,IAAIJ,KAAK,CAACI,KAAK,CAACK,MAAM;IACzC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEC,2BAA2B,EAAEV,KAAK,CAACI,KAAK,IAAIJ,KAAK,CAACI,KAAK,CAACM,2BAA2B;IAEnF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEC,IAAI,EAAE;MACLC,MAAM,EAAE,SAASA,MAAMA,CAACC,MAAM,EAAE;QAC/B,IAAIA,MAAM,YAAYC,KAAK,EAAE;UAC5B,OAAO,IAAIA,KAAK,CAACD,MAAM,CAACE,IAAI,EAAEH,MAAM,CAACC,MAAM,CAACG,OAAO,CAAC,EAAEH,MAAM,CAACI,KAAK,CAAC;QACpE,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;UACjC,OAAOA,MAAM,CAACO,GAAG,CAACR,MAAM,CAAC;QAC1B,CAAC,MAAM;UACN,OAAOC,MAAM,CAACQ,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;QACnF;MACD,CAAC;MAED;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACGN,IAAI,EAAE,SAAAA,CAAUO,CAAC,EAAE;QAClB,OAAOC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACtD,CAAC;MAED;AACH;AACA;AACA;AACA;AACA;MACGC,KAAK,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACrB,IAAI,CAACA,GAAG,CAAC,MAAM,CAAC,EAAE;UACjBN,MAAM,CAACO,cAAc,CAACD,GAAG,EAAE,MAAM,EAAE;YAAEE,KAAK,EAAE,EAAEzB;UAAS,CAAC,CAAC;QAC1D;QACA,OAAOuB,GAAG,CAAC,MAAM,CAAC;MACnB,CAAC;MAED;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACGG,KAAK,EAAE,SAASC,SAASA,CAACX,CAAC,EAAEY,OAAO,EAAE;QACrCA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;QAEvB,IAAIF,KAAK;QAAE,IAAIG,EAAE;QACjB,QAAQ3B,CAAC,CAACG,IAAI,CAACI,IAAI,CAACO,CAAC,CAAC;UACrB,KAAK,QAAQ;YACZa,EAAE,GAAG3B,CAAC,CAACG,IAAI,CAACiB,KAAK,CAACN,CAAC,CAAC;YACpB,IAAIY,OAAO,CAACC,EAAE,CAAC,EAAE;cAChB,OAAOD,OAAO,CAACC,EAAE,CAAC;YACnB;YACAH,KAAK,GAAG,kCAAoC,CAAC,CAAE;YAC/CE,OAAO,CAACC,EAAE,CAAC,GAAGH,KAAK;YAEnB,KAAK,IAAII,GAAG,IAAId,CAAC,EAAE;cAClB,IAAIA,CAAC,CAACe,cAAc,CAACD,GAAG,CAAC,EAAE;gBAC1BJ,KAAK,CAACI,GAAG,CAAC,GAAGH,SAAS,CAACX,CAAC,CAACc,GAAG,CAAC,EAAEF,OAAO,CAAC;cACxC;YACD;YAEA,OAAO,kBAAoBF,KAAK;UAEjC,KAAK,OAAO;YACXG,EAAE,GAAG3B,CAAC,CAACG,IAAI,CAACiB,KAAK,CAACN,CAAC,CAAC;YACpB,IAAIY,OAAO,CAACC,EAAE,CAAC,EAAE;cAChB,OAAOD,OAAO,CAACC,EAAE,CAAC;YACnB;YACAH,KAAK,GAAG,EAAE;YACVE,OAAO,CAACC,EAAE,CAAC,GAAGH,KAAK;YAEnB,CAAC,qBAAqB,kBAAmBV,CAAC,EAAIgB,OAAO,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;cACrER,KAAK,CAACQ,CAAC,CAAC,GAAGP,SAAS,CAACM,CAAC,EAAEL,OAAO,CAAC;YACjC,CAAC,CAAC;YAEF,OAAO,kBAAoBF,KAAK;UAEjC;YACC,OAAOV,CAAC;QACV;MACD,CAAC;MAED;AACH;AACA;AACA;AACA;AACA;AACA;AACA;MACGmB,WAAW,EAAE,SAAAA,CAAUC,OAAO,EAAE;QAC/B,OAAOA,OAAO,EAAE;UACf,IAAIC,CAAC,GAAGtC,IAAI,CAACuC,IAAI,CAACF,OAAO,CAACG,SAAS,CAAC;UACpC,IAAIF,CAAC,EAAE;YACN,OAAOA,CAAC,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;UAC1B;UACAJ,OAAO,GAAGA,OAAO,CAACK,aAAa;QAChC;QACA,OAAO,MAAM;MACd,CAAC;MAED;AACH;AACA;AACA;AACA;AACA;AACA;MACGC,WAAW,EAAE,SAAAA,CAAUN,OAAO,EAAEO,QAAQ,EAAE;QACzC;QACA;QACAP,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACG,SAAS,CAACxB,OAAO,CAAC6B,MAAM,CAAC7C,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;;QAErE;QACA;QACAqC,OAAO,CAACS,SAAS,CAACC,GAAG,CAAC,WAAW,GAAGH,QAAQ,CAAC;MAC9C,CAAC;MAED;AACH;AACA;AACA;AACA;AACA;AACA;MACGI,aAAa,EAAE,SAAAA,CAAA,EAAY;QAC1B,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;UACpC,OAAO,IAAI;QACZ;QACA,IAAI,eAAe,IAAIA,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,sCAAsC;UAC9E,OAAO,kBAAoBA,QAAQ,CAACD,aAAa;QAClD;;QAEA;QACA;QACA;;QAEA,IAAI;UACH,MAAM,IAAIE,KAAK,CAAC,CAAC;QAClB,CAAC,CAAC,OAAOC,GAAG,EAAE;UACb;UACA;UACA;UACA;UACA;UACA;;UAEA,IAAIC,GAAG,GAAG,CAAC,oCAAoC,CAACb,IAAI,CAACY,GAAG,CAACE,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;UACzE,IAAID,GAAG,EAAE;YACR,IAAIE,OAAO,GAAGL,QAAQ,CAACM,oBAAoB,CAAC,QAAQ,CAAC;YACrD,KAAK,IAAIpB,CAAC,IAAImB,OAAO,EAAE;cACtB,IAAIA,OAAO,CAACnB,CAAC,CAAC,CAACiB,GAAG,IAAIA,GAAG,EAAE;gBAC1B,OAAOE,OAAO,CAACnB,CAAC,CAAC;cAClB;YACD;UACD;UACA,OAAO,IAAI;QACZ;MACD,CAAC;MAED;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACGqB,QAAQ,EAAE,SAAAA,CAAUnB,OAAO,EAAEG,SAAS,EAAEiB,iBAAiB,EAAE;QAC1D,IAAIC,EAAE,GAAG,KAAK,GAAGlB,SAAS;QAE1B,OAAOH,OAAO,EAAE;UACf,IAAIS,SAAS,GAAGT,OAAO,CAACS,SAAS;UACjC,IAAIA,SAAS,CAACa,QAAQ,CAACnB,SAAS,CAAC,EAAE;YAClC,OAAO,IAAI;UACZ;UACA,IAAIM,SAAS,CAACa,QAAQ,CAACD,EAAE,CAAC,EAAE;YAC3B,OAAO,KAAK;UACb;UACArB,OAAO,GAAGA,OAAO,CAACK,aAAa;QAChC;QACA,OAAO,CAAC,CAACe,iBAAiB;MAC3B;IACD,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;IACEG,SAAS,EAAE;MACV;AACH;AACA;MACGC,KAAK,EAAE3D,gBAAgB;MACvB4D,SAAS,EAAE5D,gBAAgB;MAC3B6D,IAAI,EAAE7D,gBAAgB;MACtB8D,GAAG,EAAE9D,gBAAgB;MAErB;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACG+D,MAAM,EAAE,SAAAA,CAAUnC,EAAE,EAAEoC,KAAK,EAAE;QAC5B,IAAIlE,IAAI,GAAGG,CAAC,CAACG,IAAI,CAACqB,KAAK,CAACxB,CAAC,CAACyD,SAAS,CAAC9B,EAAE,CAAC,CAAC;QAExC,KAAK,IAAIC,GAAG,IAAImC,KAAK,EAAE;UACtBlE,IAAI,CAAC+B,GAAG,CAAC,GAAGmC,KAAK,CAACnC,GAAG,CAAC;QACvB;QAEA,OAAO/B,IAAI;MACZ,CAAC;MAED;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACGmE,YAAY,EAAE,SAAAA,CAAUC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAE;QACrDA,IAAI,GAAGA,IAAI,KAAI,kBAAoBpE,CAAC,CAACyD,SAAS,CAAC;QAC/C,IAAIY,OAAO,GAAGD,IAAI,CAACH,MAAM,CAAC;QAC1B;QACA,IAAIK,GAAG,GAAG,CAAC,CAAC;QAEZ,KAAK,IAAIC,KAAK,IAAIF,OAAO,EAAE;UAC1B,IAAIA,OAAO,CAACxC,cAAc,CAAC0C,KAAK,CAAC,EAAE;YAElC,IAAIA,KAAK,IAAIL,MAAM,EAAE;cACpB,KAAK,IAAIM,QAAQ,IAAIL,MAAM,EAAE;gBAC5B,IAAIA,MAAM,CAACtC,cAAc,CAAC2C,QAAQ,CAAC,EAAE;kBACpCF,GAAG,CAACE,QAAQ,CAAC,GAAGL,MAAM,CAACK,QAAQ,CAAC;gBACjC;cACD;YACD;;YAEA;YACA,IAAI,CAACL,MAAM,CAACtC,cAAc,CAAC0C,KAAK,CAAC,EAAE;cAClCD,GAAG,CAACC,KAAK,CAAC,GAAGF,OAAO,CAACE,KAAK,CAAC;YAC5B;UACD;QACD;QAEA,IAAIE,GAAG,GAAGL,IAAI,CAACH,MAAM,CAAC;QACtBG,IAAI,CAACH,MAAM,CAAC,GAAGK,GAAG;;QAElB;QACAtE,CAAC,CAACyD,SAAS,CAACiB,GAAG,CAAC1E,CAAC,CAACyD,SAAS,EAAE,UAAU7B,GAAG,EAAEL,KAAK,EAAE;UAClD,IAAIA,KAAK,KAAKkD,GAAG,IAAI7C,GAAG,IAAIqC,MAAM,EAAE;YACnC,IAAI,CAACrC,GAAG,CAAC,GAAG0C,GAAG;UAChB;QACD,CAAC,CAAC;QAEF,OAAOA,GAAG;MACX,CAAC;MAED;MACAI,GAAG,EAAE,SAASA,GAAGA,CAAC5D,CAAC,EAAE6D,QAAQ,EAAEpE,IAAI,EAAEmB,OAAO,EAAE;QAC7CA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;QAEvB,IAAIN,KAAK,GAAGpB,CAAC,CAACG,IAAI,CAACiB,KAAK;QAExB,KAAK,IAAIY,CAAC,IAAIlB,CAAC,EAAE;UAChB,IAAIA,CAAC,CAACe,cAAc,CAACG,CAAC,CAAC,EAAE;YACxB2C,QAAQ,CAACzD,IAAI,CAACJ,CAAC,EAAEkB,CAAC,EAAElB,CAAC,CAACkB,CAAC,CAAC,EAAEzB,IAAI,IAAIyB,CAAC,CAAC;YAEpC,IAAI4C,QAAQ,GAAG9D,CAAC,CAACkB,CAAC,CAAC;YACnB,IAAI6C,YAAY,GAAG7E,CAAC,CAACG,IAAI,CAACI,IAAI,CAACqE,QAAQ,CAAC;YAExC,IAAIC,YAAY,KAAK,QAAQ,IAAI,CAACnD,OAAO,CAACN,KAAK,CAACwD,QAAQ,CAAC,CAAC,EAAE;cAC3DlD,OAAO,CAACN,KAAK,CAACwD,QAAQ,CAAC,CAAC,GAAG,IAAI;cAC/BF,GAAG,CAACE,QAAQ,EAAED,QAAQ,EAAE,IAAI,EAAEjD,OAAO,CAAC;YACvC,CAAC,MAAM,IAAImD,YAAY,KAAK,OAAO,IAAI,CAACnD,OAAO,CAACN,KAAK,CAACwD,QAAQ,CAAC,CAAC,EAAE;cACjElD,OAAO,CAACN,KAAK,CAACwD,QAAQ,CAAC,CAAC,GAAG,IAAI;cAC/BF,GAAG,CAACE,QAAQ,EAAED,QAAQ,EAAE3C,CAAC,EAAEN,OAAO,CAAC;YACpC;UACD;QACD;MACD;IACD,CAAC;IAEDoD,OAAO,EAAE,CAAC,CAAC;IAEX;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEC,YAAY,EAAE,SAAAA,CAAUC,KAAK,EAAEL,QAAQ,EAAE;MACxC3E,CAAC,CAACiF,iBAAiB,CAACnC,QAAQ,EAAEkC,KAAK,EAAEL,QAAQ,CAAC;IAC/C,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEM,iBAAiB,EAAE,SAAAA,CAAUC,SAAS,EAAEF,KAAK,EAAEL,QAAQ,EAAE;MACxD,IAAIQ,GAAG,GAAG;QACTR,QAAQ,EAAEA,QAAQ;QAClBO,SAAS,EAAEA,SAAS;QACpBE,QAAQ,EAAE;MACX,CAAC;MAEDpF,CAAC,CAACqF,KAAK,CAACC,GAAG,CAAC,qBAAqB,EAAEH,GAAG,CAAC;MAEvCA,GAAG,CAACI,QAAQ,GAAG7E,KAAK,CAACM,SAAS,CAACG,KAAK,CAACqE,KAAK,CAACL,GAAG,CAACD,SAAS,CAACO,gBAAgB,CAACN,GAAG,CAACC,QAAQ,CAAC,CAAC;MAExFpF,CAAC,CAACqF,KAAK,CAACC,GAAG,CAAC,+BAA+B,EAAEH,GAAG,CAAC;MAEjD,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEE,OAAO,EAAGA,OAAO,GAAGiD,GAAG,CAACI,QAAQ,CAACvD,CAAC,EAAE,CAAC,GAAI;QACxDhC,CAAC,CAAC0F,gBAAgB,CAACxD,OAAO,EAAE8C,KAAK,KAAK,IAAI,EAAEG,GAAG,CAACR,QAAQ,CAAC;MAC1D;IACD,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEe,gBAAgB,EAAE,SAAAA,CAAUxD,OAAO,EAAE8C,KAAK,EAAEL,QAAQ,EAAE;MACrD;MACA,IAAIlC,QAAQ,GAAGzC,CAAC,CAACG,IAAI,CAAC8B,WAAW,CAACC,OAAO,CAAC;MAC1C,IAAImC,OAAO,GAAGrE,CAAC,CAACyD,SAAS,CAAChB,QAAQ,CAAC;;MAEnC;MACAzC,CAAC,CAACG,IAAI,CAACqC,WAAW,CAACN,OAAO,EAAEO,QAAQ,CAAC;;MAErC;MACA,IAAIkD,MAAM,GAAGzD,OAAO,CAACK,aAAa;MAClC,IAAIoD,MAAM,IAAIA,MAAM,CAACC,QAAQ,CAACtD,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;QACtDtC,CAAC,CAACG,IAAI,CAACqC,WAAW,CAACmD,MAAM,EAAElD,QAAQ,CAAC;MACrC;MAEA,IAAIoD,IAAI,GAAG3D,OAAO,CAAC4D,WAAW;MAE9B,IAAIX,GAAG,GAAG;QACTjD,OAAO,EAAEA,OAAO;QAChBO,QAAQ,EAAEA,QAAQ;QAClB4B,OAAO,EAAEA,OAAO;QAChBwB,IAAI,EAAEA;MACP,CAAC;MAED,SAASE,qBAAqBA,CAACC,eAAe,EAAE;QAC/Cb,GAAG,CAACa,eAAe,GAAGA,eAAe;QAErChG,CAAC,CAACqF,KAAK,CAACC,GAAG,CAAC,eAAe,EAAEH,GAAG,CAAC;QAEjCA,GAAG,CAACjD,OAAO,CAAC+D,SAAS,GAAGd,GAAG,CAACa,eAAe;QAE3ChG,CAAC,CAACqF,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAEH,GAAG,CAAC;QACnCnF,CAAC,CAACqF,KAAK,CAACC,GAAG,CAAC,UAAU,EAAEH,GAAG,CAAC;QAC5BR,QAAQ,IAAIA,QAAQ,CAACzD,IAAI,CAACiE,GAAG,CAACjD,OAAO,CAAC;MACvC;MAEAlC,CAAC,CAACqF,KAAK,CAACC,GAAG,CAAC,qBAAqB,EAAEH,GAAG,CAAC;;MAEvC;MACAQ,MAAM,GAAGR,GAAG,CAACjD,OAAO,CAACK,aAAa;MAClC,IAAIoD,MAAM,IAAIA,MAAM,CAACC,QAAQ,CAACtD,WAAW,CAAC,CAAC,KAAK,KAAK,IAAI,CAACqD,MAAM,CAACO,YAAY,CAAC,UAAU,CAAC,EAAE;QAC1FP,MAAM,CAACQ,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC;MACrC;MAEA,IAAI,CAAChB,GAAG,CAACU,IAAI,EAAE;QACd7F,CAAC,CAACqF,KAAK,CAACC,GAAG,CAAC,UAAU,EAAEH,GAAG,CAAC;QAC5BR,QAAQ,IAAIA,QAAQ,CAACzD,IAAI,CAACiE,GAAG,CAACjD,OAAO,CAAC;QACtC;MACD;MAEAlC,CAAC,CAACqF,KAAK,CAACC,GAAG,CAAC,kBAAkB,EAAEH,GAAG,CAAC;MAEpC,IAAI,CAACA,GAAG,CAACd,OAAO,EAAE;QACjB0B,qBAAqB,CAAC/F,CAAC,CAACG,IAAI,CAACC,MAAM,CAAC+E,GAAG,CAACU,IAAI,CAAC,CAAC;QAC9C;MACD;MAEA,IAAIb,KAAK,IAAIxF,KAAK,CAAC4G,MAAM,EAAE;QAC1B,IAAIC,MAAM,GAAG,IAAID,MAAM,CAACpG,CAAC,CAACsG,QAAQ,CAAC;QAEnCD,MAAM,CAACE,SAAS,GAAG,UAAUC,GAAG,EAAE;UACjCT,qBAAqB,CAACS,GAAG,CAACC,IAAI,CAAC;QAChC,CAAC;QAEDJ,MAAM,CAACK,WAAW,CAACC,IAAI,CAACC,SAAS,CAAC;UACjCnE,QAAQ,EAAE0C,GAAG,CAAC1C,QAAQ;UACtBoD,IAAI,EAAEV,GAAG,CAACU,IAAI;UACdgB,cAAc,EAAE;QACjB,CAAC,CAAC,CAAC;MACJ,CAAC,MAAM;QACNd,qBAAqB,CAAC/F,CAAC,CAAC8G,SAAS,CAAC3B,GAAG,CAACU,IAAI,EAAEV,GAAG,CAACd,OAAO,EAAEc,GAAG,CAAC1C,QAAQ,CAAC,CAAC;MACxE;IACD,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEqE,SAAS,EAAE,SAAAA,CAAUlD,IAAI,EAAES,OAAO,EAAE5B,QAAQ,EAAE;MAC7C,IAAI0C,GAAG,GAAG;QACTU,IAAI,EAAEjC,IAAI;QACVS,OAAO,EAAEA,OAAO;QAChB5B,QAAQ,EAAEA;MACX,CAAC;MACDzC,CAAC,CAACqF,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAEH,GAAG,CAAC;MACnC,IAAI,CAACA,GAAG,CAACd,OAAO,EAAE;QACjB,MAAM,IAAItB,KAAK,CAAC,gBAAgB,GAAGoC,GAAG,CAAC1C,QAAQ,GAAG,mBAAmB,CAAC;MACvE;MACA0C,GAAG,CAAC9E,MAAM,GAAGL,CAAC,CAAC+G,QAAQ,CAAC5B,GAAG,CAACU,IAAI,EAAEV,GAAG,CAACd,OAAO,CAAC;MAC9CrE,CAAC,CAACqF,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAEH,GAAG,CAAC;MAClC,OAAO7E,KAAK,CAACsG,SAAS,CAAC5G,CAAC,CAACG,IAAI,CAACC,MAAM,CAAC+E,GAAG,CAAC9E,MAAM,CAAC,EAAE8E,GAAG,CAAC1C,QAAQ,CAAC;IAChE,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEsE,QAAQ,EAAE,SAAAA,CAAUnD,IAAI,EAAES,OAAO,EAAE;MAClC,IAAI2C,IAAI,GAAG3C,OAAO,CAAC2C,IAAI;MACvB,IAAIA,IAAI,EAAE;QACT,KAAK,IAAIzC,KAAK,IAAIyC,IAAI,EAAE;UACvB3C,OAAO,CAACE,KAAK,CAAC,GAAGyC,IAAI,CAACzC,KAAK,CAAC;QAC7B;QAEA,OAAOF,OAAO,CAAC2C,IAAI;MACpB;MAEA,IAAIC,SAAS,GAAG,IAAIC,UAAU,CAAC,CAAC;MAChCC,QAAQ,CAACF,SAAS,EAAEA,SAAS,CAACG,IAAI,EAAExD,IAAI,CAAC;MAEzCyD,YAAY,CAACzD,IAAI,EAAEqD,SAAS,EAAE5C,OAAO,EAAE4C,SAAS,CAACG,IAAI,EAAE,CAAC,CAAC;MAEzD,OAAOE,OAAO,CAACL,SAAS,CAAC;IAC1B,CAAC;IAED;AACF;AACA;AACA;AACA;IACE5B,KAAK,EAAE;MACNkC,GAAG,EAAE,CAAC,CAAC;MAEP;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACG3E,GAAG,EAAE,SAAAA,CAAU4E,IAAI,EAAE7C,QAAQ,EAAE;QAC9B,IAAIU,KAAK,GAAGrF,CAAC,CAACqF,KAAK,CAACkC,GAAG;QAEvBlC,KAAK,CAACmC,IAAI,CAAC,GAAGnC,KAAK,CAACmC,IAAI,CAAC,IAAI,EAAE;QAE/BnC,KAAK,CAACmC,IAAI,CAAC,CAACC,IAAI,CAAC9C,QAAQ,CAAC;MAC3B,CAAC;MAED;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACGW,GAAG,EAAE,SAAAA,CAAUkC,IAAI,EAAErC,GAAG,EAAE;QACzB,IAAIuC,SAAS,GAAG1H,CAAC,CAACqF,KAAK,CAACkC,GAAG,CAACC,IAAI,CAAC;QAEjC,IAAI,CAACE,SAAS,IAAI,CAACA,SAAS,CAACC,MAAM,EAAE;UACpC;QACD;QAEA,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAE2C,QAAQ,EAAGA,QAAQ,GAAG+C,SAAS,CAAC1F,CAAC,EAAE,CAAC,GAAI;UACvD2C,QAAQ,CAACQ,GAAG,CAAC;QACd;MACD;IACD,CAAC;IAED7E,KAAK,EAAEA;EACR,CAAC;EACDd,KAAK,CAACI,KAAK,GAAGI,CAAC;;EAGf;EACA;EACA;EACA;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASM,KAAKA,CAACC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEmH,UAAU,EAAE;IAChD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE,IAAI,CAACrH,IAAI,GAAGA,IAAI;IAChB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACE,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB;AACF;AACA;AACA;AACA;AACA;AACA;IACE,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB;IACA,IAAI,CAACkH,MAAM,GAAG,CAACC,UAAU,IAAI,EAAE,EAAED,MAAM,GAAG,CAAC;EAC5C;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCrH,KAAK,CAACsG,SAAS,GAAG,SAASA,SAASA,CAAC9F,CAAC,EAAE2B,QAAQ,EAAE;IACjD,IAAI,OAAO3B,CAAC,IAAI,QAAQ,EAAE;MACzB,OAAOA,CAAC;IACT;IACA,IAAIJ,KAAK,CAACC,OAAO,CAACG,CAAC,CAAC,EAAE;MACrB,IAAI+G,CAAC,GAAG,EAAE;MACV/G,CAAC,CAACgB,OAAO,CAAC,UAAUgG,CAAC,EAAE;QACtBD,CAAC,IAAIjB,SAAS,CAACkB,CAAC,EAAErF,QAAQ,CAAC;MAC5B,CAAC,CAAC;MACF,OAAOoF,CAAC;IACT;IAEA,IAAI1C,GAAG,GAAG;MACT5E,IAAI,EAAEO,CAAC,CAACP,IAAI;MACZC,OAAO,EAAEoG,SAAS,CAAC9F,CAAC,CAACN,OAAO,EAAEiC,QAAQ,CAAC;MACvCsF,GAAG,EAAE,MAAM;MACXC,OAAO,EAAE,CAAC,OAAO,EAAElH,CAAC,CAACP,IAAI,CAAC;MAC1B0H,UAAU,EAAE,CAAC,CAAC;MACdxF,QAAQ,EAAEA;IACX,CAAC;IAED,IAAIyF,OAAO,GAAGpH,CAAC,CAACL,KAAK;IACrB,IAAIyH,OAAO,EAAE;MACZ,IAAIxH,KAAK,CAACC,OAAO,CAACuH,OAAO,CAAC,EAAE;QAC3BxH,KAAK,CAACM,SAAS,CAACyG,IAAI,CAACjC,KAAK,CAACL,GAAG,CAAC6C,OAAO,EAAEE,OAAO,CAAC;MACjD,CAAC,MAAM;QACN/C,GAAG,CAAC6C,OAAO,CAACP,IAAI,CAACS,OAAO,CAAC;MAC1B;IACD;IAEAlI,CAAC,CAACqF,KAAK,CAACC,GAAG,CAAC,MAAM,EAAEH,GAAG,CAAC;IAExB,IAAI8C,UAAU,GAAG,EAAE;IACnB,KAAK,IAAIT,IAAI,IAAIrC,GAAG,CAAC8C,UAAU,EAAE;MAChCA,UAAU,IAAI,GAAG,GAAGT,IAAI,GAAG,IAAI,GAAG,CAACrC,GAAG,CAAC8C,UAAU,CAACT,IAAI,CAAC,IAAI,EAAE,EAAE3G,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,GAAG;IAC7F;IAEA,OAAO,GAAG,GAAGsE,GAAG,CAAC4C,GAAG,GAAG,UAAU,GAAG5C,GAAG,CAAC6C,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAGF,UAAU,GAAG,GAAG,GAAG9C,GAAG,CAAC3E,OAAO,GAAG,IAAI,GAAG2E,GAAG,CAAC4C,GAAG,GAAG,GAAG;EACxH,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;EACC,SAASK,YAAYA,CAACC,OAAO,EAAEC,GAAG,EAAE1E,IAAI,EAAE2E,UAAU,EAAE;IACrDF,OAAO,CAACG,SAAS,GAAGF,GAAG;IACvB,IAAIG,KAAK,GAAGJ,OAAO,CAACjG,IAAI,CAACwB,IAAI,CAAC;IAC9B,IAAI6E,KAAK,IAAIF,UAAU,IAAIE,KAAK,CAAC,CAAC,CAAC,EAAE;MACpC;MACA,IAAIC,gBAAgB,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACd,MAAM;MACtCc,KAAK,CAACE,KAAK,IAAID,gBAAgB;MAC/BD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACtH,KAAK,CAACuH,gBAAgB,CAAC;IAC5C;IACA,OAAOD,KAAK;EACb;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASpB,YAAYA,CAACzD,IAAI,EAAEqD,SAAS,EAAE5C,OAAO,EAAEuE,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAC7E,KAAK,IAAIvE,KAAK,IAAIF,OAAO,EAAE;MAC1B,IAAI,CAACA,OAAO,CAACxC,cAAc,CAAC0C,KAAK,CAAC,IAAI,CAACF,OAAO,CAACE,KAAK,CAAC,EAAE;QACtD;MACD;MAEA,IAAIwE,QAAQ,GAAG1E,OAAO,CAACE,KAAK,CAAC;MAC7BwE,QAAQ,GAAGrI,KAAK,CAACC,OAAO,CAACoI,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;MAE1D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAACpB,MAAM,EAAE,EAAEqB,CAAC,EAAE;QACzC,IAAIF,OAAO,IAAIA,OAAO,CAACG,KAAK,IAAI1E,KAAK,GAAG,GAAG,GAAGyE,CAAC,EAAE;UAChD;QACD;QAEA,IAAIE,UAAU,GAAGH,QAAQ,CAACC,CAAC,CAAC;QAC5B,IAAI/E,MAAM,GAAGiF,UAAU,CAACjF,MAAM;QAC9B,IAAIsE,UAAU,GAAG,CAAC,CAACW,UAAU,CAACX,UAAU;QACxC,IAAIY,MAAM,GAAG,CAAC,CAACD,UAAU,CAACC,MAAM;QAChC,IAAI1I,KAAK,GAAGyI,UAAU,CAACzI,KAAK;QAE5B,IAAI0I,MAAM,IAAI,CAACD,UAAU,CAACb,OAAO,CAACe,MAAM,EAAE;UACzC;UACA,IAAIC,KAAK,GAAGH,UAAU,CAACb,OAAO,CAACpH,QAAQ,CAAC,CAAC,CAACwH,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;UAC/DS,UAAU,CAACb,OAAO,GAAG3F,MAAM,CAACwG,UAAU,CAACb,OAAO,CAACiB,MAAM,EAAED,KAAK,GAAG,GAAG,CAAC;QACpE;;QAEA;QACA,IAAIhB,OAAO,GAAGa,UAAU,CAACb,OAAO,IAAIa,UAAU;QAE9C;QAAM;QACL,IAAIK,WAAW,GAAGX,SAAS,CAACY,IAAI,EAAElB,GAAG,GAAGO,QAAQ,EAChDU,WAAW,KAAKtC,SAAS,CAACwC,IAAI,EAC9BnB,GAAG,IAAIiB,WAAW,CAAChI,KAAK,CAACoG,MAAM,EAAE4B,WAAW,GAAGA,WAAW,CAACC,IAAI,EAC9D;UAED,IAAIV,OAAO,IAAIR,GAAG,IAAIQ,OAAO,CAACY,KAAK,EAAE;YACpC;UACD;UAEA,IAAIC,GAAG,GAAGJ,WAAW,CAAChI,KAAK;UAE3B,IAAI0F,SAAS,CAACU,MAAM,GAAG/D,IAAI,CAAC+D,MAAM,EAAE;YACnC;YACA;UACD;UAEA,IAAIgC,GAAG,YAAYrJ,KAAK,EAAE;YACzB;UACD;UAEA,IAAIsJ,WAAW,GAAG,CAAC,CAAC,CAAC;UACrB,IAAInB,KAAK;UAET,IAAIU,MAAM,EAAE;YACXV,KAAK,GAAGL,YAAY,CAACC,OAAO,EAAEC,GAAG,EAAE1E,IAAI,EAAE2E,UAAU,CAAC;YACpD,IAAI,CAACE,KAAK,IAAIA,KAAK,CAACE,KAAK,IAAI/E,IAAI,CAAC+D,MAAM,EAAE;cACzC;YACD;YAEA,IAAIkC,IAAI,GAAGpB,KAAK,CAACE,KAAK;YACtB,IAAImB,EAAE,GAAGrB,KAAK,CAACE,KAAK,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACd,MAAM;YACtC,IAAIoC,CAAC,GAAGzB,GAAG;;YAEX;YACAyB,CAAC,IAAIR,WAAW,CAAChI,KAAK,CAACoG,MAAM;YAC7B,OAAOkC,IAAI,IAAIE,CAAC,EAAE;cACjBR,WAAW,GAAGA,WAAW,CAACC,IAAI;cAC9BO,CAAC,IAAIR,WAAW,CAAChI,KAAK,CAACoG,MAAM;YAC9B;YACA;YACAoC,CAAC,IAAIR,WAAW,CAAChI,KAAK,CAACoG,MAAM;YAC7BW,GAAG,GAAGyB,CAAC;;YAEP;YACA,IAAIR,WAAW,CAAChI,KAAK,YAAYjB,KAAK,EAAE;cACvC;YACD;;YAEA;YACA,KACC,IAAI0J,CAAC,GAAGT,WAAW,EACnBS,CAAC,KAAK/C,SAAS,CAACwC,IAAI,KAAKM,CAAC,GAAGD,EAAE,IAAI,OAAOE,CAAC,CAACzI,KAAK,KAAK,QAAQ,CAAC,EAC/DyI,CAAC,GAAGA,CAAC,CAACR,IAAI,EACT;cACDI,WAAW,EAAE;cACbG,CAAC,IAAIC,CAAC,CAACzI,KAAK,CAACoG,MAAM;YACpB;YACAiC,WAAW,EAAE;;YAEb;YACAD,GAAG,GAAG/F,IAAI,CAACzC,KAAK,CAACmH,GAAG,EAAEyB,CAAC,CAAC;YACxBtB,KAAK,CAACE,KAAK,IAAIL,GAAG;UACnB,CAAC,MAAM;YACNG,KAAK,GAAGL,YAAY,CAACC,OAAO,EAAE,CAAC,EAAEsB,GAAG,EAAEpB,UAAU,CAAC;YACjD,IAAI,CAACE,KAAK,EAAE;cACX;YACD;UACD;;UAEA;UACA,IAAIoB,IAAI,GAAGpB,KAAK,CAACE,KAAK;UACtB,IAAIsB,QAAQ,GAAGxB,KAAK,CAAC,CAAC,CAAC;UACvB,IAAIvE,MAAM,GAAGyF,GAAG,CAACxI,KAAK,CAAC,CAAC,EAAE0I,IAAI,CAAC;UAC/B,IAAIK,KAAK,GAAGP,GAAG,CAACxI,KAAK,CAAC0I,IAAI,GAAGI,QAAQ,CAACtC,MAAM,CAAC;UAE7C,IAAI+B,KAAK,GAAGpB,GAAG,GAAGqB,GAAG,CAAChC,MAAM;UAC5B,IAAImB,OAAO,IAAIY,KAAK,GAAGZ,OAAO,CAACY,KAAK,EAAE;YACrCZ,OAAO,CAACY,KAAK,GAAGA,KAAK;UACtB;UAEA,IAAIS,UAAU,GAAGZ,WAAW,CAACa,IAAI;UAEjC,IAAIlG,MAAM,EAAE;YACXiG,UAAU,GAAGhD,QAAQ,CAACF,SAAS,EAAEkD,UAAU,EAAEjG,MAAM,CAAC;YACpDoE,GAAG,IAAIpE,MAAM,CAACyD,MAAM;UACrB;UAEA0C,WAAW,CAACpD,SAAS,EAAEkD,UAAU,EAAEP,WAAW,CAAC;UAE/C,IAAIU,OAAO,GAAG,IAAIhK,KAAK,CAACiE,KAAK,EAAEN,MAAM,GAAGjE,CAAC,CAAC+G,QAAQ,CAACkD,QAAQ,EAAEhG,MAAM,CAAC,GAAGgG,QAAQ,EAAExJ,KAAK,EAAEwJ,QAAQ,CAAC;UACjGV,WAAW,GAAGpC,QAAQ,CAACF,SAAS,EAAEkD,UAAU,EAAEG,OAAO,CAAC;UAEtD,IAAIJ,KAAK,EAAE;YACV/C,QAAQ,CAACF,SAAS,EAAEsC,WAAW,EAAEW,KAAK,CAAC;UACxC;UAEA,IAAIN,WAAW,GAAG,CAAC,EAAE;YACpB;YACA;;YAEA;YACA,IAAIW,aAAa,GAAG;cACnBtB,KAAK,EAAE1E,KAAK,GAAG,GAAG,GAAGyE,CAAC;cACtBU,KAAK,EAAEA;YACR,CAAC;YACDrC,YAAY,CAACzD,IAAI,EAAEqD,SAAS,EAAE5C,OAAO,EAAEkF,WAAW,CAACa,IAAI,EAAE9B,GAAG,EAAEiC,aAAa,CAAC;;YAE5E;YACA,IAAIzB,OAAO,IAAIyB,aAAa,CAACb,KAAK,GAAGZ,OAAO,CAACY,KAAK,EAAE;cACnDZ,OAAO,CAACY,KAAK,GAAGa,aAAa,CAACb,KAAK;YACpC;UACD;QACD;MACD;IACD;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC;AACD;AACA;AACA;EACC,SAASxC,UAAUA,CAAA,EAAG;IACrB;IACA,IAAIE,IAAI,GAAG;MAAE7F,KAAK,EAAE,IAAI;MAAE6I,IAAI,EAAE,IAAI;MAAEZ,IAAI,EAAE;IAAK,CAAC;IAClD;IACA,IAAIC,IAAI,GAAG;MAAElI,KAAK,EAAE,IAAI;MAAE6I,IAAI,EAAEhD,IAAI;MAAEoC,IAAI,EAAE;IAAK,CAAC;IAClDpC,IAAI,CAACoC,IAAI,GAAGC,IAAI;;IAEhB;IACA,IAAI,CAACrC,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAACqC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC9B,MAAM,GAAG,CAAC;EAChB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASR,QAAQA,CAACqD,IAAI,EAAEC,IAAI,EAAElJ,KAAK,EAAE;IACpC;IACA,IAAIiI,IAAI,GAAGiB,IAAI,CAACjB,IAAI;IAEpB,IAAIkB,OAAO,GAAG;MAAEnJ,KAAK,EAAEA,KAAK;MAAE6I,IAAI,EAAEK,IAAI;MAAEjB,IAAI,EAAEA;IAAK,CAAC;IACtDiB,IAAI,CAACjB,IAAI,GAAGkB,OAAO;IACnBlB,IAAI,CAACY,IAAI,GAAGM,OAAO;IACnBF,IAAI,CAAC7C,MAAM,EAAE;IAEb,OAAO+C,OAAO;EACf;EACA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASL,WAAWA,CAACG,IAAI,EAAEC,IAAI,EAAEE,KAAK,EAAE;IACvC,IAAInB,IAAI,GAAGiB,IAAI,CAACjB,IAAI;IACpB,KAAK,IAAIxH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2I,KAAK,IAAInB,IAAI,KAAKgB,IAAI,CAACf,IAAI,EAAEzH,CAAC,EAAE,EAAE;MACrDwH,IAAI,GAAGA,IAAI,CAACA,IAAI;IACjB;IACAiB,IAAI,CAACjB,IAAI,GAAGA,IAAI;IAChBA,IAAI,CAACY,IAAI,GAAGK,IAAI;IAChBD,IAAI,CAAC7C,MAAM,IAAI3F,CAAC;EACjB;EACA;AACD;AACA;AACA;AACA;EACC,SAASsF,OAAOA,CAACkD,IAAI,EAAE;IACtB,IAAII,KAAK,GAAG,EAAE;IACd,IAAIH,IAAI,GAAGD,IAAI,CAACpD,IAAI,CAACoC,IAAI;IACzB,OAAOiB,IAAI,KAAKD,IAAI,CAACf,IAAI,EAAE;MAC1BmB,KAAK,CAACnD,IAAI,CAACgD,IAAI,CAAClJ,KAAK,CAAC;MACtBkJ,IAAI,GAAGA,IAAI,CAACjB,IAAI;IACjB;IACA,OAAOoB,KAAK;EACb;EAGA,IAAI,CAACpL,KAAK,CAACsD,QAAQ,EAAE;IACpB,IAAI,CAACtD,KAAK,CAACqL,gBAAgB,EAAE;MAC5B;MACA,OAAO7K,CAAC;IACT;IAEA,IAAI,CAACA,CAAC,CAACE,2BAA2B,EAAE;MACnC;MACAV,KAAK,CAACqL,gBAAgB,CAAC,SAAS,EAAE,UAAUrE,GAAG,EAAE;QAChD,IAAIsE,OAAO,GAAGnE,IAAI,CAACoE,KAAK,CAACvE,GAAG,CAACC,IAAI,CAAC;QAClC,IAAI5G,IAAI,GAAGiL,OAAO,CAACrI,QAAQ;QAC3B,IAAIoD,IAAI,GAAGiF,OAAO,CAACjF,IAAI;QACvB,IAAIgB,cAAc,GAAGiE,OAAO,CAACjE,cAAc;QAE3CrH,KAAK,CAACkH,WAAW,CAAC1G,CAAC,CAAC8G,SAAS,CAACjB,IAAI,EAAE7F,CAAC,CAACyD,SAAS,CAAC5D,IAAI,CAAC,EAAEA,IAAI,CAAC,CAAC;QAC7D,IAAIgH,cAAc,EAAE;UACnBrH,KAAK,CAACwL,KAAK,CAAC,CAAC;QACd;MACD,CAAC,EAAE,KAAK,CAAC;IACV;IAEA,OAAOhL,CAAC;EACT;;EAEA;EACA,IAAIiL,MAAM,GAAGjL,CAAC,CAACG,IAAI,CAAC0C,aAAa,CAAC,CAAC;EAEnC,IAAIoI,MAAM,EAAE;IACXjL,CAAC,CAACsG,QAAQ,GAAG2E,MAAM,CAAChI,GAAG;IAEvB,IAAIgI,MAAM,CAAC/E,YAAY,CAAC,aAAa,CAAC,EAAE;MACvClG,CAAC,CAACC,MAAM,GAAG,IAAI;IAChB;EACD;EAEA,SAASiL,8BAA8BA,CAAA,EAAG;IACzC,IAAI,CAAClL,CAAC,CAACC,MAAM,EAAE;MACdD,CAAC,CAAC+E,YAAY,CAAC,CAAC;IACjB;EACD;EAEA,IAAI,CAAC/E,CAAC,CAACC,MAAM,EAAE;IACd;IACA;IACA;IACA;IACA;IACA;IACA,IAAIkL,UAAU,GAAGrI,QAAQ,CAACqI,UAAU;IACpC,IAAIA,UAAU,KAAK,SAAS,IAAIA,UAAU,KAAK,aAAa,IAAIF,MAAM,IAAIA,MAAM,CAACG,KAAK,EAAE;MACvFtI,QAAQ,CAAC+H,gBAAgB,CAAC,kBAAkB,EAAEK,8BAA8B,CAAC;IAC9E,CAAC,MAAM;MACN,IAAIzL,MAAM,CAAC4L,qBAAqB,EAAE;QACjC5L,MAAM,CAAC4L,qBAAqB,CAACH,8BAA8B,CAAC;MAC7D,CAAC,MAAM;QACNzL,MAAM,CAAC6L,UAAU,CAACJ,8BAA8B,EAAE,EAAE,CAAC;MACtD;IACD;EACD;EAEA,OAAOlL,CAAC;AAET,CAAC,CAACR,KAAK,CAAE;AAET,IAAI,OAAO+L,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,EAAE;EACpDD,MAAM,CAACC,OAAO,GAAG5L,KAAK;AACvB;;AAEA;AACA,IAAI,OAAOwJ,MAAM,KAAK,WAAW,EAAE;EAClCA,MAAM,CAACxJ,KAAK,GAAGA,KAAK;AACrB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}