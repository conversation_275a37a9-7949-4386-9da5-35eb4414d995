import React from "react";
import { render, screen } from "@testing-library/react";

import UserPhonesCreateDialogComponent from "../UserPhonesCreateDialogComponent";
import { MemoryRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import { init } from "@rematch/core";
import { Provider } from "react-redux";
import * as models from "../../../models";

test("renders userPhones create dialog", async () => {
  const store = init({ models });
  render(
    <Provider store={store}>
      <MemoryRouter>
        <UserPhonesCreateDialogComponent show={true} />
      </MemoryRouter>
    </Provider>,
  );
  expect(
    screen.getByRole("userPhones-create-dialog-component"),
  ).toBeInTheDocument();
});
