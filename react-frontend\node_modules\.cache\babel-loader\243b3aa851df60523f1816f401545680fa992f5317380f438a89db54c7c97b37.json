{"ast": null, "code": "'use strict';\n\nmodule.exports = caseSensitiveTransform;\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute;\n}", "map": {"version": 3, "names": ["module", "exports", "caseSensitiveTransform", "attributes", "attribute"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/property-information/lib/util/case-sensitive-transform.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = caseSensitiveTransform\n\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,sBAAsB;AAEvC,SAASA,sBAAsBA,CAACC,UAAU,EAAEC,SAAS,EAAE;EACrD,OAAOA,SAAS,IAAID,UAAU,GAAGA,UAAU,CAACC,SAAS,CAAC,GAAGA,SAAS;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}