import React from "react";
import { render, screen } from "@testing-library/react";

import NotificationsCreateDialogComponent from "../NotificationsCreateDialogComponent";
import { MemoryRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import { init } from "@rematch/core";
import { Provider } from "react-redux";
import * as models from "../../../../models";

test("renders notifications create dialog", async () => {
  const store = init({ models });
  render(
    <Provider store={store}>
      <MemoryRouter>
        <NotificationsCreateDialogComponent show={true} />
      </MemoryRouter>
    </Provider>,
  );
  expect(
    screen.getByRole("notifications-create-dialog-component"),
  ).toBeInTheDocument();
});
