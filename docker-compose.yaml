version: "3"
services:
    ui:
        build:
            context: ./react-frontend
            dockerfile: Dockerfile
        ports:
            - 3000:3000
        container_name: mern-ui
        networks:
            - my-stack-net
        volumes:
            - ./react-frontend/src:/usr/app/src
            - ./react-frontend/public:/usr/app/public
        depends_on:
            - api
        stdin_open: true
        tty: true

    api:
        build:
            context: ./nodejs-backend
            dockerfile: Dockerfile
        environment:
            - MONGODB_URL=mongodb://mongo-db:27017/chatbotwithgemini
        ports:
            - 3030:3030
        container_name: mern-api
        networks:
            - my-stack-net
        volumes:
            - ./nodejs-backend/src:/usr/app/src
        depends_on:
            - db

    db:
        image: mongo:5.0.13
        ports:
            - 27017:27017
        container_name: mongo-db
        networks:
            - my-stack-net
        volumes:
            - mongo-data:/data/db

networks:
    my-stack-net:
        driver: bridge

volumes:
    mongo-data:
        driver: local
