import React from "react";
import { render, screen } from "@testing-library/react";

import UserPhonesEditDialogComponent from "../UserPhonesEditDialogComponent";
import { MemoryRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import { init } from "@rematch/core";
import { Provider } from "react-redux";
import * as models from "../../../models";

test("renders userPhones edit dialog", async () => {
  const store = init({ models });
  render(
    <Provider store={store}>
      <MemoryRouter>
        <UserPhonesEditDialogComponent show={true} />
      </MemoryRouter>
    </Provider>,
  );
  expect(
    screen.getByRole("userPhones-edit-dialog-component"),
  ).toBeInTheDocument();
});
