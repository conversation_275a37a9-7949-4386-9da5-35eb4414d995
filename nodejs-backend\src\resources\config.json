{"projectName": "chatbotwithgemini", "description": null, "auth": "jwt", "database": {"pathToLogo": {"s": "/assets/applications_logos/mongodb-s.png", "l": "/assets/applications_logos/mongodb.png"}, "appName": "mongodb-database", "disabled": false, "type": "database", "name": "mongodb", "label": "MongoDB"}, "stack": [{"appName": "nodejs-backend", "environments": [null, null, null, null, null], "pathToLogo": {"s": "/assets/applications_logos/nodejs-s.png", "l": "/assets/applications_logos/nodejs.png"}, "type": "backend", "name": "nodejs", "label": "Node.js Express Feathers.js"}, {"appName": "react-frontend", "environments": [null, null, null, null, null], "pathToLogo": {"s": "/assets/applications_logos/react-s.png", "l": "/assets/applications_logos/react.png"}, "type": "frontend", "name": "react", "label": "React JavaScript"}], "services": [{"serviceName": "users", "databaseName": "users", "displayName": "Users", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Name", "min": 2, "max": 100, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "email", "type": "String", "unique": true, "lowercase": true, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Email", "min": 5, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "status", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": false, "displayOnEdit": false, "displayOnSingle": false, "displayOnDataTable": false, "creatable": false, "editable": false, "sortable": false, "required": false, "component": "p", "label": "Status", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": true, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "password", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": false, "displayOnSingle": false, "displayOnDataTable": false, "creatable": true, "editable": false, "sortable": false, "required": true, "component": "p", "label": "Password", "min": 5, "max": 300, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "remember_token", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": false, "displayOnEdit": false, "displayOnSingle": false, "displayOnDataTable": false, "creatable": false, "editable": false, "sortable": false, "required": false, "component": "p", "label": "Re<PERSON><PERSON> Me", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "email_verified_at", "type": "DateTime", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": false, "displayOnEdit": false, "displayOnSingle": false, "displayOnDataTable": false, "creatable": false, "editable": false, "sortable": false, "required": false, "component": "p", "label": "Verified On", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": true, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "message", "databaseName": "message", "displayName": "Message", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "message", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Message", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "68d2de12681bfa7a7baa448d"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": false, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}]}