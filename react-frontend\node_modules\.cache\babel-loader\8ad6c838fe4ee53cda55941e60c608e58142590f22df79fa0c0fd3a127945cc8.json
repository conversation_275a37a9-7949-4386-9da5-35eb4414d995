{"ast": null, "code": "'use strict';\n\nexports.parse = parse;\nexports.stringify = stringify;\nvar comma = ',';\nvar space = ' ';\nvar empty = '';\n\n// Parse comma-separated tokens to an array.\nfunction parse(value) {\n  var values = [];\n  var input = String(value || empty);\n  var index = input.indexOf(comma);\n  var lastIndex = 0;\n  var end = false;\n  var val;\n  while (!end) {\n    if (index === -1) {\n      index = input.length;\n      end = true;\n    }\n    val = input.slice(lastIndex, index).trim();\n    if (val || !end) {\n      values.push(val);\n    }\n    lastIndex = index + 1;\n    index = input.indexOf(comma, lastIndex);\n  }\n  return values;\n}\n\n// Compile an array to comma-separated tokens.\n// `options.padLeft` (default: `true`) pads a space left of each token, and\n// `options.padRight` (default: `false`) pads a space to the right of each token.\nfunction stringify(values, options) {\n  var settings = options || {};\n  var left = settings.padLeft === false ? empty : space;\n  var right = settings.padRight ? space : empty;\n\n  // Ensure the last empty entry is seen.\n  if (values[values.length - 1] === empty) {\n    values = values.concat(empty);\n  }\n  return values.join(right + comma + left).trim();\n}", "map": {"version": 3, "names": ["exports", "parse", "stringify", "comma", "space", "empty", "value", "values", "input", "String", "index", "indexOf", "lastIndex", "end", "val", "length", "slice", "trim", "push", "options", "settings", "left", "padLeft", "right", "padRight", "concat", "join"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/comma-separated-tokens/index.js"], "sourcesContent": ["'use strict'\n\nexports.parse = parse\nexports.stringify = stringify\n\nvar comma = ','\nvar space = ' '\nvar empty = ''\n\n// Parse comma-separated tokens to an array.\nfunction parse(value) {\n  var values = []\n  var input = String(value || empty)\n  var index = input.indexOf(comma)\n  var lastIndex = 0\n  var end = false\n  var val\n\n  while (!end) {\n    if (index === -1) {\n      index = input.length\n      end = true\n    }\n\n    val = input.slice(lastIndex, index).trim()\n\n    if (val || !end) {\n      values.push(val)\n    }\n\n    lastIndex = index + 1\n    index = input.indexOf(comma, lastIndex)\n  }\n\n  return values\n}\n\n// Compile an array to comma-separated tokens.\n// `options.padLeft` (default: `true`) pads a space left of each token, and\n// `options.padRight` (default: `false`) pads a space to the right of each token.\nfunction stringify(values, options) {\n  var settings = options || {}\n  var left = settings.padLeft === false ? empty : space\n  var right = settings.padRight ? space : empty\n\n  // Ensure the last empty entry is seen.\n  if (values[values.length - 1] === empty) {\n    values = values.concat(empty)\n  }\n\n  return values.join(right + comma + left).trim()\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,KAAK,GAAGA,KAAK;AACrBD,OAAO,CAACE,SAAS,GAAGA,SAAS;AAE7B,IAAIC,KAAK,GAAG,GAAG;AACf,IAAIC,KAAK,GAAG,GAAG;AACf,IAAIC,KAAK,GAAG,EAAE;;AAEd;AACA,SAASJ,KAAKA,CAACK,KAAK,EAAE;EACpB,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,KAAK,GAAGC,MAAM,CAACH,KAAK,IAAID,KAAK,CAAC;EAClC,IAAIK,KAAK,GAAGF,KAAK,CAACG,OAAO,CAACR,KAAK,CAAC;EAChC,IAAIS,SAAS,GAAG,CAAC;EACjB,IAAIC,GAAG,GAAG,KAAK;EACf,IAAIC,GAAG;EAEP,OAAO,CAACD,GAAG,EAAE;IACX,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBA,KAAK,GAAGF,KAAK,CAACO,MAAM;MACpBF,GAAG,GAAG,IAAI;IACZ;IAEAC,GAAG,GAAGN,KAAK,CAACQ,KAAK,CAACJ,SAAS,EAAEF,KAAK,CAAC,CAACO,IAAI,CAAC,CAAC;IAE1C,IAAIH,GAAG,IAAI,CAACD,GAAG,EAAE;MACfN,MAAM,CAACW,IAAI,CAACJ,GAAG,CAAC;IAClB;IAEAF,SAAS,GAAGF,KAAK,GAAG,CAAC;IACrBA,KAAK,GAAGF,KAAK,CAACG,OAAO,CAACR,KAAK,EAAES,SAAS,CAAC;EACzC;EAEA,OAAOL,MAAM;AACf;;AAEA;AACA;AACA;AACA,SAASL,SAASA,CAACK,MAAM,EAAEY,OAAO,EAAE;EAClC,IAAIC,QAAQ,GAAGD,OAAO,IAAI,CAAC,CAAC;EAC5B,IAAIE,IAAI,GAAGD,QAAQ,CAACE,OAAO,KAAK,KAAK,GAAGjB,KAAK,GAAGD,KAAK;EACrD,IAAImB,KAAK,GAAGH,QAAQ,CAACI,QAAQ,GAAGpB,KAAK,GAAGC,KAAK;;EAE7C;EACA,IAAIE,MAAM,CAACA,MAAM,CAACQ,MAAM,GAAG,CAAC,CAAC,KAAKV,KAAK,EAAE;IACvCE,MAAM,GAAGA,MAAM,CAACkB,MAAM,CAACpB,KAAK,CAAC;EAC/B;EAEA,OAAOE,MAAM,CAACmB,IAAI,CAACH,KAAK,GAAGpB,KAAK,GAAGkB,IAAI,CAAC,CAACJ,IAAI,CAAC,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}