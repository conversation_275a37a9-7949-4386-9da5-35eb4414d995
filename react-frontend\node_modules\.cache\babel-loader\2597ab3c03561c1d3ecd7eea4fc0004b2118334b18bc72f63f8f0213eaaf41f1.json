{"ast": null, "code": "'use strict';\n\nvar powers = 0;\nexports.boolean = increment();\nexports.booleanish = increment();\nexports.overloadedBoolean = increment();\nexports.number = increment();\nexports.spaceSeparated = increment();\nexports.commaSeparated = increment();\nexports.commaOrSpaceSeparated = increment();\nfunction increment() {\n  return Math.pow(2, ++powers);\n}", "map": {"version": 3, "names": ["powers", "exports", "boolean", "increment", "booleanish", "overloadedBoolean", "number", "spaceSeparated", "commaSeparated", "commaOrSpaceSeparated", "Math", "pow"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/property-information/lib/util/types.js"], "sourcesContent": ["'use strict'\n\nvar powers = 0\n\nexports.boolean = increment()\nexports.booleanish = increment()\nexports.overloadedBoolean = increment()\nexports.number = increment()\nexports.spaceSeparated = increment()\nexports.commaSeparated = increment()\nexports.commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return Math.pow(2, ++powers)\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,CAAC;AAEdC,OAAO,CAACC,OAAO,GAAGC,SAAS,CAAC,CAAC;AAC7BF,OAAO,CAACG,UAAU,GAAGD,SAAS,CAAC,CAAC;AAChCF,OAAO,CAACI,iBAAiB,GAAGF,SAAS,CAAC,CAAC;AACvCF,OAAO,CAACK,MAAM,GAAGH,SAAS,CAAC,CAAC;AAC5BF,OAAO,CAACM,cAAc,GAAGJ,SAAS,CAAC,CAAC;AACpCF,OAAO,CAACO,cAAc,GAAGL,SAAS,CAAC,CAAC;AACpCF,OAAO,CAACQ,qBAAqB,GAAGN,SAAS,CAAC,CAAC;AAE3C,SAASA,SAASA,CAAA,EAAG;EACnB,OAAOO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAEX,MAAM,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}