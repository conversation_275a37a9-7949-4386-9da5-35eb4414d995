/* InboxDataTable.css */
.unread-row {
  font-weight: bold;
  background-color: #f9f9f9;
}
.p-button-selected {
  background-color: #007ad9; /* Primary blue or any color for selected state */
  color: #fff;
}

.p-button-outlined {
  border-color: #007ad9;
  color: #007ad9;
}

.user-suggestion {
  padding: 10px;
  cursor: pointer;
}

.user-suggestion:hover {
  background-color: #f0f0f0;
}

.p-tag {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

/* Styles for the suggestions container */
.suggestions {
  position: absolute;
  background-color: #ffffff; /* Background color of suggestions dropdown */
  border: 1px solid #ccc; /* Border color */
  border-radius: 4px; /* Rounded corners */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); /* Shadow effect */
  max-height: 200px; /* Maximum height of the suggestions box */
  overflow-y: auto; /* Enable vertical scrolling if content exceeds max height */
  width: 95%; /* Full width of the input field */
  z-index: 1000; /* Make sure suggestions are on top of other elements */
}

/* Styles for each suggestion item */
.suggestion-item {
  padding: 10px 15px; /* Padding around each suggestion */
  cursor: pointer; /* Pointer cursor on hover */
  transition: background-color 0.2s; /* Smooth transition for background color */
}

/* Change background color on hover */
.suggestion-item:hover {
  background-color: #f0f0f0; /* Light gray background on hover */
}
.reduced-margin {
  margin-top: 0.5rem !important; /* Use !important if necessary */
}
