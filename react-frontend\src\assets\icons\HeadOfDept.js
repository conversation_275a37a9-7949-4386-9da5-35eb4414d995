import * as React from "react";
const HeadOfDept = (props) => (
  <svg
width="18" height="18"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M15.1533 1.29393L15.1346 1.37711C15.1092 1.49017 15.0933 1.56099 15.0767 1.63181C14.9161 2.33167 15.3273 2.99405 16.0266 3.16165C16.4496 3.26339 16.599 3.39726 16.6301 3.8267C16.6596 4.24169 16.6472 4.66204 16.6221 5.07864C16.6012 5.4208 16.3918 5.61518 16.0513 5.69282C15.2443 5.87702 14.862 6.51959 15.0719 7.32547C15.1474 7.61516 15.0976 7.86737 14.8475 8.03872C14.4732 8.29467 14.0936 8.54581 13.6984 8.76696C13.4183 8.92385 13.1945 8.8537 12.9846 8.61274C12.4117 7.95518 11.6267 7.96643 11.0842 8.63898C10.8877 8.88315 10.6762 8.97258 10.4031 8.82693C9.94207 8.58008 9.4971 8.29896 9.06497 8.00338C8.8465 7.85452 8.81223 7.60873 8.88827 7.35171C9.14851 6.46711 8.76297 5.8299 7.85106 5.63178C7.54584 5.56538 7.34932 5.40152 7.33433 5.09898C7.31184 4.62991 7.32094 4.15709 7.35361 3.68855C7.37503 3.38119 7.57797 3.20556 7.88801 3.13862C8.77475 2.94693 9.14529 2.3472 8.9386 1.46367C8.87542 1.19487 8.93271 0.949621 9.16564 0.799689C9.57635 0.535702 9.99883 0.288314 10.4283 0.0553846C10.6526 -0.0661672 10.8599 0.0259337 11.0318 0.199962C11.6695 0.845739 12.2917 0.846275 12.9252 0.201033C13.1062 0.0168307 13.323 -0.0672382 13.5538 0.0639521C14.009 0.322584 14.4609 0.590855 14.8904 0.889112C14.9866 0.955677 15.0447 1.07682 15.0945 1.18057L15.0945 1.18059C15.1148 1.22288 15.1337 1.26229 15.1533 1.29393ZM13.5436 4.43714C13.5362 3.57503 12.8315 2.87732 11.9688 2.87892C11.1099 2.87999 10.4101 3.58896 10.4144 4.45374C10.4181 5.31853 11.119 6.01143 11.9876 6.00875C12.8518 6.00607 13.5511 5.30032 13.5436 4.43714ZM12.0636 18.5334C12.3431 18.5435 12.6287 18.5525 12.9185 18.5617C13.6019 18.5833 14.3089 18.6057 15.0151 18.6437C15.9388 18.6935 16.6306 19.343 16.7104 20.263C16.7586 20.8193 16.7565 21.3869 16.7077 21.9433C16.6247 22.89 15.9249 23.6214 14.9889 23.7671C13.4323 24.0091 11.8671 24.067 10.2965 23.9197L10.2916 23.9192L10.2915 23.9192C9.86749 23.8798 9.44242 23.8402 9.02159 23.7751C7.98117 23.6145 7.27542 22.8423 7.21759 21.7928C7.19242 21.3307 7.19081 20.8654 7.21491 20.4033C7.26739 19.3891 7.95654 18.694 8.97447 18.641C9.66286 18.6058 10.3523 18.5855 11.0638 18.5644C11.3917 18.5547 11.7243 18.5449 12.0636 18.5334ZM17.5324 20.0033C17.6507 21.0796 17.7112 22.1569 17.2015 23.1743C17.2111 23.1776 17.2202 23.1812 17.2289 23.1847C17.2471 23.192 17.2637 23.1986 17.2807 23.2L17.4096 23.2143C17.6059 23.236 17.802 23.2578 17.9988 23.2745C19.4713 23.3992 20.9385 23.3537 22.3939 23.0908C23.1896 22.9473 23.8413 22.2587 23.912 21.4619C23.9591 20.9323 23.9661 20.3931 23.9254 19.863C23.8531 18.9243 23.2078 18.293 22.2649 18.2732C20.9646 18.2458 19.664 18.2432 18.3633 18.2407H18.363H18.363H18.363H18.3629H18.3629H18.3629H18.3628C17.9347 18.2399 17.5065 18.239 17.0783 18.2373C16.9568 18.2369 16.8352 18.2478 16.7043 18.2595C16.6414 18.2652 16.5763 18.271 16.508 18.2758C17.2395 19.0464 17.4638 19.3816 17.5324 20.0033ZM7.43074 18.301L7.35063 18.3754L7.35056 18.3755C7.29392 18.4281 7.24521 18.4732 7.19727 18.5189C6.75604 18.9398 6.45029 19.4485 6.41495 20.0574C6.37425 20.7572 6.37693 21.4635 6.42352 22.1634C6.43913 22.3968 6.5197 22.626 6.60603 22.8716C6.64704 22.9882 6.68934 23.1086 6.72659 23.2348C6.48396 23.2453 6.23993 23.2602 5.995 23.2751L5.99478 23.2751L5.99455 23.2751L5.99432 23.2752L5.9941 23.2752L5.99388 23.2752H5.99385H5.99382H5.99379H5.99376H5.99373C5.43746 23.3091 4.87657 23.3433 4.31697 23.3269C3.41042 23.3002 2.5028 23.2118 1.60374 23.0887C0.761984 22.9735 0.121026 22.2951 0.040705 21.4523C-0.0128421 20.8884 -0.0133775 20.3117 0.0380276 19.7473C0.110852 18.9489 0.735211 18.3364 1.5352 18.2839C3.46718 18.1564 5.40184 18.1554 7.33542 18.27C7.34547 18.2703 7.35532 18.2739 7.37544 18.2814C7.38855 18.2863 7.40601 18.2928 7.43074 18.301ZM9.10031 14.9699C9.09335 16.5581 10.3785 17.8507 11.9688 17.8544C13.5645 17.8582 14.8507 16.5838 14.8582 14.9902C14.8657 13.4009 13.5806 12.1083 11.9897 12.1045C10.3962 12.1008 9.10727 13.379 9.10031 14.9699ZM22.1701 14.8194C22.1696 16.3257 20.9551 17.5342 19.4446 17.531C17.9361 17.5278 16.7276 16.3123 16.734 14.8055C16.741 13.3126 17.949 12.1088 19.4451 12.1056C20.9524 12.1013 22.1706 13.3152 22.1701 14.8194ZM4.52471 12.1056C3.01683 12.0965 1.79702 13.3013 1.78953 14.8081C1.78149 16.3021 2.98041 17.5171 4.47545 17.5315C5.98762 17.5449 7.21438 16.346 7.22455 14.844C7.23526 13.3356 6.03099 12.1142 4.52471 12.1056ZM3.56837 10.5881C3.67974 9.55999 4.10544 8.59668 4.82458 7.76295C5.39378 7.10218 6.0915 6.61758 6.89846 6.28933C7.41251 6.0805 7.91478 6.26256 8.11665 6.72521C8.32442 7.20178 8.11451 7.71315 7.59885 7.93109C6.30087 8.48048 5.53247 9.43148 5.33274 10.8392C5.26634 11.3088 4.83422 11.6167 4.36515 11.5723C3.90036 11.5289 3.55712 11.1353 3.56837 10.5881ZM19.5655 11.5739C20.0357 11.5407 20.3902 11.1594 20.3918 10.6668C20.3915 10.662 20.3913 10.6556 20.391 10.6482C20.3904 10.6294 20.3895 10.6039 20.3864 10.579C20.1674 8.64755 18.8293 6.93029 16.9342 6.24543C16.2788 6.00821 15.6454 6.59241 15.7862 7.2746C15.8553 7.60874 16.085 7.79508 16.3806 7.94769C16.75 8.13832 17.1291 8.33483 17.4461 8.59828C18.1315 9.16856 18.5069 9.92304 18.6226 10.8119C18.6852 11.2933 19.0954 11.6071 19.5655 11.5739Z"
      fill="#2A4454"
    />
  </svg>
);
export default HeadOfDept;
