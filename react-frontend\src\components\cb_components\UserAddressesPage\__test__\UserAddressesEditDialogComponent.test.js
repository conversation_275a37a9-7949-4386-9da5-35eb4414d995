import React from "react";
import { render, screen } from "@testing-library/react";

import UserAddressesEditDialogComponent from "../UserAddressesEditDialogComponent";
import { MemoryRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import { init } from "@rematch/core";
import { Provider } from "react-redux";
import * as models from "../../../models";

test("renders userAddresses edit dialog", async () => {
  const store = init({ models });
  render(
    <Provider store={store}>
      <MemoryRouter>
        <UserAddressesEditDialogComponent show={true} />
      </MemoryRouter>
    </Provider>,
  );
  expect(
    screen.getByRole("userAddresses-edit-dialog-component"),
  ).toBeInTheDocument();
});
