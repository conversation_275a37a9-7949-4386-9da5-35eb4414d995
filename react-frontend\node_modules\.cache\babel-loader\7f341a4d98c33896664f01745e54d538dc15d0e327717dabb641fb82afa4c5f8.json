{"ast": null, "code": "'use strict';\n\nvar create = require('./util/create');\nvar caseInsensitiveTransform = require('./util/case-insensitive-transform');\nmodule.exports = create({\n  space: 'xmlns',\n  attributes: {\n    xmlnsxlink: 'xmlns:xlink'\n  },\n  transform: caseInsensitiveTransform,\n  properties: {\n    xmlns: null,\n    xmlnsXLink: null\n  }\n});", "map": {"version": 3, "names": ["create", "require", "caseInsensitiveTransform", "module", "exports", "space", "attributes", "xmlnsxlink", "transform", "properties", "xmlns", "xmlnsXLink"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/property-information/lib/xmlns.js"], "sourcesContent": ["'use strict'\n\nvar create = require('./util/create')\nvar caseInsensitiveTransform = require('./util/case-insensitive-transform')\n\nmodule.exports = create({\n  space: 'xmlns',\n  attributes: {\n    xmlnsxlink: 'xmlns:xlink'\n  },\n  transform: caseInsensitiveTransform,\n  properties: {\n    xmlns: null,\n    xmlnsXLink: null\n  }\n})\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,eAAe,CAAC;AACrC,IAAIC,wBAAwB,GAAGD,OAAO,CAAC,mCAAmC,CAAC;AAE3EE,MAAM,CAACC,OAAO,GAAGJ,MAAM,CAAC;EACtBK,KAAK,EAAE,OAAO;EACdC,UAAU,EAAE;IACVC,UAAU,EAAE;EACd,CAAC;EACDC,SAAS,EAAEN,wBAAwB;EACnCO,UAAU,EAAE;IACVC,KAAK,EAAE,IAAI;IACXC,UAAU,EAAE;EACd;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}