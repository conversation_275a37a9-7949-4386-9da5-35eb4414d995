import React from "react";
import { render, screen } from "@testing-library/react";

import TemplatesEditDialogComponent from "../TemplatesEditDialogComponent";
import { MemoryRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import { init } from "@rematch/core";
import { Provider } from "react-redux";
import * as models from "../../../models";

test("renders templates edit dialog", async () => {
  const store = init({ models });
  render(
    <Provider store={store}>
      <MemoryRouter>
        <TemplatesEditDialogComponent show={true} />
      </MemoryRouter>
    </Provider>,
  );
  expect(
    screen.getByRole("templates-edit-dialog-component"),
  ).toBeInTheDocument();
});
