import * as React from 'react';
const Positions = (props) => (
	<svg width="18" height="18" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="currentColor" {...props}>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.9986 11.8009C13.998 11.8595 13.9973 11.9211 13.9973 11.9866C14.1205 11.9866 14.2008 11.9866 14.2811 11.992C15.129 11.992 15.9768 11.9926 16.8246 11.9932C18.5203 11.9944 20.216 11.9955 21.9116 11.992C23.1486 11.992 23.9946 11.1459 24 9.90362V7.19947C24 5.81259 23.1914 4.99866 21.8099 4.99866H17.3387C17.2773 4.99866 17.216 4.99477 17.1488 4.99051C17.1045 4.9877 17.0577 4.98473 17.0067 4.9826V4.15261C17.0013 2.82999 16.166 2 14.8434 2H9.16732C7.82863 2 7.00936 2.82999 7.004 4.17403V4.99331H6.64523C6.14169 4.99331 5.63874 4.99271 5.13599 4.99212C4.13108 4.99093 3.12696 4.98974 2.12046 4.99331C0.840676 4.99866 0.00533384 5.83936 0.00533384 7.11379V9.84472C0.0106886 11.1566 0.840676 11.992 2.14724 11.992H9.69744H10.008C10.008 11.9265 10.0067 11.8655 10.0055 11.8075C10.0031 11.6997 10.0011 11.6022 10.008 11.5047C10.0241 11.1941 10.1847 11.0067 10.4899 11.0013C11.4966 10.9906 12.5087 10.9906 13.5154 11.0013C13.8153 11.0013 13.9812 11.1888 13.9973 11.4993C14.0008 11.5934 13.9997 11.6919 13.9986 11.8009ZM9.02275 4.00803H14.9879V4.97189H9.02275V4.00803ZM10.0027 14.3641V12.9826H9.61714C8.79073 12.9826 7.96431 12.982 7.13789 12.9814C5.48506 12.9802 3.83222 12.979 2.17939 12.9826C1.71352 12.9826 1.27443 12.9076 0.88889 12.6238C0.696118 12.4846 0.492638 12.415 0.267738 12.5382C0.0428381 12.6613 0.00535476 12.8755 0.00535476 13.1111C0.0107095 15.3762 0.00535476 17.6466 0 19.9063C0 20.1151 0.0267738 20.3347 0.0856761 20.5382C0.342704 21.4485 1.10843 21.9786 2.15797 21.9786H21.8313C21.9384 21.9786 22.0455 21.9786 22.1526 21.9679C23.1861 21.8769 23.9786 21.0522 23.984 20.0134C24 17.6894 23.9947 15.3601 23.9893 13.0308C23.984 12.5489 23.5663 12.3133 23.1754 12.5864C22.7417 12.8862 22.2758 12.9826 21.7617 12.9826C19.9184 12.9746 18.0721 12.9756 16.2251 12.9766C15.6093 12.9769 14.9934 12.9773 14.3775 12.9773H13.9973C13.9973 13.1401 13.9979 13.3 13.9986 13.4576C13.9997 13.7662 14.0009 14.0664 13.9973 14.3641C13.992 14.8032 13.8153 14.9799 13.3762 14.9799H10.6185C10.1847 14.9799 10.008 14.8032 10.0027 14.3641Z" />
</svg>

);
export default Positions;
