import * as React from 'react';
const Building = (props) => (
	<svg width="18" height="18" viewBox="0 0 24 24"  xmlns="http://www.w3.org/2000/svg" fill="currentColor" {...props}>
<path d="M2.23595 21.7529C2.23595 21.5838 2.23595 21.4524 2.23595 21.3209C2.23595 16.1245 2.23595 10.9282 2.23595 5.7318C2.23595 4.64244 2.63037 4.04141 3.63208 3.64073C6.53078 2.47625 9.42947 1.31802 12.3344 0.159795C13.7243 -0.391145 15.0703 0.541696 15.0703 2.05052C15.0766 8.46771 15.0703 14.8849 15.0703 21.2958C15.0703 21.4273 15.0703 21.565 15.0703 21.7216C15.446 21.7216 15.7966 21.7216 16.1847 21.7216C16.1847 16.544 16.1847 11.3664 16.1847 6.17631C16.2661 6.16378 16.3162 6.15126 16.3663 6.15126C17.5057 6.15126 18.6389 6.13874 19.7784 6.15126C20.9679 6.16378 21.7693 6.99019 21.7755 8.17972C21.7818 11.7608 21.7755 15.3357 21.7755 18.9168C21.7755 19.8434 21.7755 20.7699 21.7755 21.7529C22.2639 21.7529 22.7271 21.7403 23.1904 21.7529C23.7414 21.7654 24.1045 22.2537 23.973 22.7921C23.8979 23.1052 23.6475 23.3431 23.3219 23.3932C23.1967 23.4119 23.0715 23.4119 22.9463 23.4119C20.8051 23.4182 18.6702 23.4307 16.5291 23.4307C12.1779 23.4307 7.82674 23.4307 3.48183 23.4307C2.62411 23.4307 1.77266 23.4245 0.914948 23.4119C0.47044 23.4057 0.163667 23.2116 0.050975 22.8735C-0.143106 22.3226 0.238795 21.7717 0.827299 21.7529C1.28433 21.7403 1.74136 21.7529 2.23595 21.7529ZM8.63436 16.1746C9.34182 16.1746 10.0493 16.1746 10.763 16.1746C11.339 16.1746 11.7334 15.824 11.7271 15.3357C11.7209 14.8536 11.3327 14.5093 10.7818 14.5093C9.36686 14.503 7.94569 14.503 6.53078 14.5093C5.97358 14.5093 5.59167 14.8536 5.58541 15.3357C5.57915 15.824 5.97358 16.1746 6.5433 16.1809C7.23823 16.1809 7.93317 16.1746 8.63436 16.1746ZM8.64688 10.6151C7.93943 10.6151 7.22571 10.6151 6.51825 10.6151C5.96731 10.6151 5.57915 10.9595 5.57915 11.4478C5.57915 11.9173 5.96105 12.2867 6.48069 12.293C7.92691 12.2992 9.37938 12.3055 10.8319 12.293C11.3515 12.2867 11.7334 11.9048 11.7209 11.4353C11.7084 10.9595 11.3327 10.6214 10.8006 10.6151C10.0868 10.6089 9.36686 10.6151 8.64688 10.6151ZM8.64062 20.075C9.37312 20.075 10.0994 20.0813 10.8319 20.075C11.339 20.075 11.7146 19.7307 11.7271 19.2674C11.7397 18.7978 11.3578 18.4097 10.8444 18.4097C9.38564 18.4034 7.92691 18.3971 6.46817 18.4097C5.94853 18.4159 5.57289 18.8041 5.58541 19.2736C5.60419 19.7369 5.97357 20.075 6.48695 20.0813C7.20067 20.075 7.92065 20.075 8.64062 20.075ZM8.63436 8.3738C9.34182 8.3738 10.0493 8.3738 10.763 8.3738C10.8945 8.3738 11.0385 8.36754 11.1637 8.32372C11.5331 8.20476 11.771 7.82286 11.7146 7.44722C11.652 7.0215 11.3202 6.72098 10.8757 6.72098C9.40443 6.71472 7.93943 6.71472 6.46817 6.72098C5.96105 6.72098 5.59167 7.07784 5.58541 7.54113C5.57915 8.01694 5.95479 8.36754 6.48069 8.3738C7.19441 8.3738 7.91438 8.36754 8.63436 8.3738Z" />
</svg>

);
export default Building;
