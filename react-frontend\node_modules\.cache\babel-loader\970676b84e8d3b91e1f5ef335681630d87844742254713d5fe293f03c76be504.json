{"ast": null, "code": "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _arrayLikeToArray as arrayLikeToArray, _arrayWithHoles as arrayWithHoles, _defineProperty as defineProperty, _iterableToArrayLimit as iterableToArrayLimit, _nonIterableRest as nonIterableRest, _objectSpread2 as objectSpread2, _objectWithoutProperties as objectWithoutProperties, _objectWithoutPropertiesLoose as objectWithoutPropertiesLoose, _slicedToArray as slicedToArray, _unsupportedIterableToArray as unsupportedIterableToArray };", "map": {"version": 3, "names": ["_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread2", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "indexOf", "_objectWithoutProperties", "sourceSymbolKeys", "prototype", "propertyIsEnumerable", "call", "_slicedToArray", "arr", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "Array", "isArray", "Symbol", "iterator", "_arr", "_n", "_d", "_e", "undefined", "_i", "_s", "next", "done", "err", "o", "minLen", "_arrayLikeToArray", "n", "toString", "slice", "constructor", "name", "from", "test", "len", "arr2", "TypeError", "arrayLikeToArray", "arrayWithHoles", "iterableToArrayLimit", "nonIterableRest", "objectSpread2", "objectWithoutProperties", "objectWithoutPropertiesLoose", "slicedToArray", "unsupportedIterableToArray"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nexport { _arrayLikeToArray as arrayLikeToArray, _arrayWithHoles as arrayWithHoles, _defineProperty as defineProperty, _iterableToArrayLimit as iterableToArrayLimit, _nonIterableRest as nonIterableRest, _objectSpread2 as objectSpread2, _objectWithoutProperties as objectWithoutProperties, _objectWithoutPropertiesLoose as objectWithoutPropertiesLoose, _slicedToArray as slicedToArray, _unsupportedIterableToArray as unsupportedIterableToArray };\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EACxC,IAAID,GAAG,IAAID,GAAG,EAAE;IACdG,MAAM,CAACC,cAAc,CAACJ,GAAG,EAAEC,GAAG,EAAE;MAC9BC,KAAK,EAAEA,KAAK;MACZG,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLP,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAClB;EAEA,OAAOF,GAAG;AACZ;AAEA,SAASQ,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EACvC,IAAIC,IAAI,GAAGR,MAAM,CAACQ,IAAI,CAACF,MAAM,CAAC;EAE9B,IAAIN,MAAM,CAACS,qBAAqB,EAAE;IAChC,IAAIC,OAAO,GAAGV,MAAM,CAACS,qBAAqB,CAACH,MAAM,CAAC;IAClD,IAAIC,cAAc,EAAEG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAC1D,OAAOZ,MAAM,CAACa,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACV,UAAU;IAChE,CAAC,CAAC;IACFM,IAAI,CAACM,IAAI,CAACC,KAAK,CAACP,IAAI,EAAEE,OAAO,CAAC;EAChC;EAEA,OAAOF,IAAI;AACb;AAEA,SAASQ,cAAcA,CAACC,MAAM,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAErD,IAAIA,CAAC,GAAG,CAAC,EAAE;MACTb,OAAO,CAACL,MAAM,CAACqB,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUxB,GAAG,EAAE;QACnDF,eAAe,CAACqB,MAAM,EAAEnB,GAAG,EAAEuB,MAAM,CAACvB,GAAG,CAAC,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIE,MAAM,CAACuB,yBAAyB,EAAE;MAC3CvB,MAAM,CAACwB,gBAAgB,CAACP,MAAM,EAAEjB,MAAM,CAACuB,yBAAyB,CAACF,MAAM,CAAC,CAAC;IAC3E,CAAC,MAAM;MACLhB,OAAO,CAACL,MAAM,CAACqB,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUxB,GAAG,EAAE;QAC7CE,MAAM,CAACC,cAAc,CAACgB,MAAM,EAAEnB,GAAG,EAAEE,MAAM,CAACa,wBAAwB,CAACQ,MAAM,EAAEvB,GAAG,CAAC,CAAC;MAClF,CAAC,CAAC;IACJ;EACF;EAEA,OAAOmB,MAAM;AACf;AAEA,SAASQ,6BAA6BA,CAACJ,MAAM,EAAEK,QAAQ,EAAE;EACvD,IAAIL,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7B,IAAIJ,MAAM,GAAG,CAAC,CAAC;EACf,IAAIU,UAAU,GAAG3B,MAAM,CAACQ,IAAI,CAACa,MAAM,CAAC;EACpC,IAAIvB,GAAG,EAAEoB,CAAC;EAEV,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,UAAU,CAACP,MAAM,EAAEF,CAAC,EAAE,EAAE;IACtCpB,GAAG,GAAG6B,UAAU,CAACT,CAAC,CAAC;IACnB,IAAIQ,QAAQ,CAACE,OAAO,CAAC9B,GAAG,CAAC,IAAI,CAAC,EAAE;IAChCmB,MAAM,CAACnB,GAAG,CAAC,GAAGuB,MAAM,CAACvB,GAAG,CAAC;EAC3B;EAEA,OAAOmB,MAAM;AACf;AAEA,SAASY,wBAAwBA,CAACR,MAAM,EAAEK,QAAQ,EAAE;EAClD,IAAIL,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAE7B,IAAIJ,MAAM,GAAGQ,6BAA6B,CAACJ,MAAM,EAAEK,QAAQ,CAAC;EAE5D,IAAI5B,GAAG,EAAEoB,CAAC;EAEV,IAAIlB,MAAM,CAACS,qBAAqB,EAAE;IAChC,IAAIqB,gBAAgB,GAAG9B,MAAM,CAACS,qBAAqB,CAACY,MAAM,CAAC;IAE3D,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,gBAAgB,CAACV,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC5CpB,GAAG,GAAGgC,gBAAgB,CAACZ,CAAC,CAAC;MACzB,IAAIQ,QAAQ,CAACE,OAAO,CAAC9B,GAAG,CAAC,IAAI,CAAC,EAAE;MAChC,IAAI,CAACE,MAAM,CAAC+B,SAAS,CAACC,oBAAoB,CAACC,IAAI,CAACZ,MAAM,EAAEvB,GAAG,CAAC,EAAE;MAC9DmB,MAAM,CAACnB,GAAG,CAAC,GAAGuB,MAAM,CAACvB,GAAG,CAAC;IAC3B;EACF;EAEA,OAAOmB,MAAM;AACf;AAEA,SAASiB,cAAcA,CAACC,GAAG,EAAEjB,CAAC,EAAE;EAC9B,OAAOkB,eAAe,CAACD,GAAG,CAAC,IAAIE,qBAAqB,CAACF,GAAG,EAAEjB,CAAC,CAAC,IAAIoB,2BAA2B,CAACH,GAAG,EAAEjB,CAAC,CAAC,IAAIqB,gBAAgB,CAAC,CAAC;AAC3H;AAEA,SAASH,eAAeA,CAACD,GAAG,EAAE;EAC5B,IAAIK,KAAK,CAACC,OAAO,CAACN,GAAG,CAAC,EAAE,OAAOA,GAAG;AACpC;AAEA,SAASE,qBAAqBA,CAACF,GAAG,EAAEjB,CAAC,EAAE;EACrC,IAAI,OAAOwB,MAAM,KAAK,WAAW,IAAI,EAAEA,MAAM,CAACC,QAAQ,IAAI3C,MAAM,CAACmC,GAAG,CAAC,CAAC,EAAE;EACxE,IAAIS,IAAI,GAAG,EAAE;EACb,IAAIC,EAAE,GAAG,IAAI;EACb,IAAIC,EAAE,GAAG,KAAK;EACd,IAAIC,EAAE,GAAGC,SAAS;EAElB,IAAI;IACF,KAAK,IAAIC,EAAE,GAAGd,GAAG,CAACO,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAEO,EAAE,EAAE,EAAEL,EAAE,GAAG,CAACK,EAAE,GAAGD,EAAE,CAACE,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEP,EAAE,GAAG,IAAI,EAAE;MAClFD,IAAI,CAAC9B,IAAI,CAACoC,EAAE,CAACnD,KAAK,CAAC;MAEnB,IAAImB,CAAC,IAAI0B,IAAI,CAACxB,MAAM,KAAKF,CAAC,EAAE;IAC9B;EACF,CAAC,CAAC,OAAOmC,GAAG,EAAE;IACZP,EAAE,GAAG,IAAI;IACTC,EAAE,GAAGM,GAAG;EACV,CAAC,SAAS;IACR,IAAI;MACF,IAAI,CAACR,EAAE,IAAII,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IACjD,CAAC,SAAS;MACR,IAAIH,EAAE,EAAE,MAAMC,EAAE;IAClB;EACF;EAEA,OAAOH,IAAI;AACb;AAEA,SAASN,2BAA2BA,CAACgB,CAAC,EAAEC,MAAM,EAAE;EAC9C,IAAI,CAACD,CAAC,EAAE;EACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAC9D,IAAIE,CAAC,GAAGzD,MAAM,CAAC+B,SAAS,CAAC2B,QAAQ,CAACzB,IAAI,CAACqB,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,IAAIF,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACM,WAAW,EAAEH,CAAC,GAAGH,CAAC,CAACM,WAAW,CAACC,IAAI;EAC3D,IAAIJ,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOjB,KAAK,CAACsB,IAAI,CAACR,CAAC,CAAC;EACpD,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACM,IAAI,CAACN,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAClH;AAEA,SAASC,iBAAiBA,CAACrB,GAAG,EAAE6B,GAAG,EAAE;EACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAG7B,GAAG,CAACf,MAAM,EAAE4C,GAAG,GAAG7B,GAAG,CAACf,MAAM;EAErD,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAE+C,IAAI,GAAG,IAAIzB,KAAK,CAACwB,GAAG,CAAC,EAAE9C,CAAC,GAAG8C,GAAG,EAAE9C,CAAC,EAAE,EAAE+C,IAAI,CAAC/C,CAAC,CAAC,GAAGiB,GAAG,CAACjB,CAAC,CAAC;EAErE,OAAO+C,IAAI;AACb;AAEA,SAAS1B,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAI2B,SAAS,CAAC,2IAA2I,CAAC;AAClK;AAEA,SAASV,iBAAiB,IAAIW,gBAAgB,EAAE/B,eAAe,IAAIgC,cAAc,EAAExE,eAAe,IAAIK,cAAc,EAAEoC,qBAAqB,IAAIgC,oBAAoB,EAAE9B,gBAAgB,IAAI+B,eAAe,EAAEtD,cAAc,IAAIuD,aAAa,EAAE1C,wBAAwB,IAAI2C,uBAAuB,EAAE/C,6BAA6B,IAAIgD,4BAA4B,EAAEvC,cAAc,IAAIwC,aAAa,EAAEpC,2BAA2B,IAAIqC,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}