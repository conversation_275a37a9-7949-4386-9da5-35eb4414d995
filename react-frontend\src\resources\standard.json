{"services": [{"serviceName": "branches", "databaseName": "branches", "displayName": "Branches", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "companyId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Company", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "6662a38e6e9ee9002730dc8c"}, "min": 10, "max": 20, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "companies", "refDatabaseName": "companies", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Name", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "6662a38e6e9ee9002730dc8d"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "isDefault", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_boolean", "label": "Is default", "faker": {"module": "datatype", "subModule": "boolean", "skip": false, "_id": "66615378eaafcce3f19407e5"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"companyId_companies_name_121": "Cloud Basha Sdn Bhd", "name": "Sri Petaling", "isDefault_boolean_boolean": true}], "skip": true, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "companies", "databaseName": "companies", "displayName": "Companies", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Name", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66629b486e9ee9002730d72e"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "companyNo", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Company no", "faker": {"module": "database", "subModule": "mongodbObjectId", "skip": false, "_id": "66615453eaafcce3f19408ce"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "default": "Cas", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "newCompanyNumber", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_number", "label": "New company number", "faker": {"module": "datatype", "subModule": "number", "skip": false, "_id": "6661541eeaafcce3f1940861"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "DateIncorporated", "type": "Date", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_calendar", "label": "Date Incorporated", "faker": {"module": "date", "subModule": "past", "skip": false, "_id": "66615378eaafcce3f19407e0"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "isdefault", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_boolean", "label": "Is default", "faker": {"module": "datatype", "subModule": "boolean", "skip": false, "_id": "66615378eaafcce3f19407e1"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"Name": "Cloud Basha Sdn Bhd", "Company No": "1193937 -P", "New Company Number": 201601022998, "Date Incorporated_date_date": 42567, "isDefault": true, "name": "Cloud Basha Sdn Bhd", "companyNo": "1193937-P", "newCompanyNumber": 202234334345, "DateIncorporated": "2016-07-17T05:24:23.602Z", "isdefault": true}], "skip": true, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "companyAddresses", "databaseName": "company_addresses", "displayName": "Company Addresses", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "companyId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Company", "faker": {"skip": false, "_id": "666151d6eaafcce3f194063e"}, "min": 10, "max": 20, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "companies", "refDatabaseName": "companies", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "Street1", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "inputTextarea", "label": "Street1", "faker": {"skip": false, "_id": "666151d6eaafcce3f194063f"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "Street2", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "inputTextarea", "label": "Street2", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940640"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "Poscode", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Poscode", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940641"}, "min": 0, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "City", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "City", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940642"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "State", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "State", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940643"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "Province", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Province", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940644"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "Country", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Country", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940645"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "isDefault", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p_boolean", "label": "<PERSON>", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"companyId_companies_names_121": "Cloud Basha Sdn Bhd", "Street1_string_textarea": "Unit C-13A-3A, Block C,", "Street2_string_textarea": "3 Two Square No. 2, Jalan 19/1,", "Poscode_string_text": 46300, "City_string_text": "Petaling Jaya,", "State_string_text": "Malaysia", "Province_string_text": "None", "Country_string_text": "Malaysia"}], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "companyPhones", "databaseName": "company_phones", "displayName": "Company Phones", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "companyId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Company", "faker": {"module": "", "subModule": "", "args": "", "skip": false, "_id": "66629c296e9ee9002730d985"}, "min": 10, "max": 20, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "companies", "refDatabaseName": "companies", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "countryCode", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "default", "label": "Country code", "faker": {"module": "datatype", "subModule": "number", "args": "2", "skip": false, "_id": "666298396e9ee9002730d582"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "operatorCode", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "default", "label": "Operator code", "faker": {"module": "datatype", "subModule": "number", "args": "3", "skip": false, "_id": "666298396e9ee9002730d583"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "number", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "default", "label": "Number", "faker": {"module": "datatype", "subModule": "number", "args": "8", "skip": false, "_id": "666298396e9ee9002730d584"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "type", "type": "Array", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdownArray", "label": "Type", "faker": {"module": "", "subModule": "", "args": "", "skip": false, "_id": "66629c296e9ee9002730d989"}, "min": 3, "max": *********, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "default": "[\"Land line\",\"Mobile\",\"Fax\"]", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "isDefault", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p_boolean", "label": "<PERSON>", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"countryCode": 60, "operatorCode": 3, "number": 3433456, "type": "Land line"}, {"countryCode": 60, "operatorCode": 3, "number": 12112111, "type": "Mobile"}], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "departmentAdmin", "databaseName": "department_admin", "displayName": "Department Admins", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "departmentId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "DepartmentId", "faker": {"module": "", "subModule": "", "args": "", "skip": false, "_id": "6698e23ea197de57fb4943f7"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "departments", "refDatabaseName": "departments", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "employeeId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "EmployeeId", "faker": {"module": "", "subModule": "", "args": "", "skip": false, "_id": "6698e23ea197de57fb4943f8"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "employees", "refDatabaseName": "employees", "relationshipType": "one-to-one", "identifierFieldName": ["fullname"]}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "departmentHOD", "databaseName": "department_h_o_d", "displayName": "Head of Department", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "departmentId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "DepartmentId", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "departments", "refDatabaseName": "departments", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "employeeId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "EmployeeId", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "employees", "refDatabaseName": "employees", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "departmentHOS", "databaseName": "department_h_o_s", "displayName": "Head of Section", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "sectionId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "SectionId", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "sections", "refDatabaseName": "sections", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "employeeId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "EmployeeId", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "employees", "refDatabaseName": "employees", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "departments", "databaseName": "departments", "displayName": "Departments", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "companyId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Company", "faker": {"skip": false, "_id": "666151d6eaafcce3f19405f8"}, "min": 10, "max": 20, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "companies", "refDatabaseName": "companies", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Name", "faker": {"skip": false, "_id": "666151d6eaafcce3f19405f9"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "code", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Code", "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "isDefault", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "tick", "label": "Is default", "faker": {"module": "datatype", "subModule": "boolean", "skip": false, "_id": "6662924e89606af2e7718cae"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"companyId_companies_name_121": "Cloud Basha Sdn Bhd", "name": "IT", "code": 1000, "isDefault_boolean_boolean": true}], "skip": true, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "dynaFields", "databaseName": "dyna_fields", "displayName": "Dyna Fields", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "dynaL<PERSON>der", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": true, "component": "dropdown", "label": "Dyna<PERSON><PERSON>der", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666a683dd84b09e4ffd91706"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "dynaL<PERSON>der", "refDatabaseName": "dyna_loader", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "from", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "From Field", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666db598435a80bb59ae5d59"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "fromType", "type": "Object", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": true, "component": "p", "label": "From Field Type", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666db598435a80bb59ae5d5a"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "to2", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "To Field", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666db598435a80bb59ae5d5e"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "toType", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "To Field Type", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666db598435a80bb59ae5d5f"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "default": "", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "fromRefService", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "From Ref Service", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666db598435a80bb59ae5d5b"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "toRefService", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "To Ref Service", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666db598435a80bb59ae5d60"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "default": "", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "fromIdentityFieldName", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "From Field Identity", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666db598435a80bb59ae5d5d"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "default": "", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "toIdentityFieldName", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "To Field Identity", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666db598435a80bb59ae5d62"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "default": "", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "fromRelationship", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "From Field Relationship", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666db598435a80bb59ae5d5c"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "default": "", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "toRelationship", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "To Field Relationship", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666db598435a80bb59ae5d61"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "default": "", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "duplicates", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": false, "creatable": true, "editable": false, "sortable": true, "required": true, "component": "p", "label": "Duplicates", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "666da1f2e769f6e5dbc3519e"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "dynaL<PERSON>der", "databaseName": "dyna_loader", "displayName": "Dyna<PERSON><PERSON>der", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "from", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "From Service", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666a65e2d84b09e4ffd915aa"}, "min": null, "max": 50, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "to2", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "To", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666a65e2d84b09e4ffd915ab"}, "min": null, "max": 50, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Name", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666a8496db0689582d3328b8"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "employees", "databaseName": "employees", "displayName": "Employees", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "empNo", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Emp No", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "667a197963a0b497082703bc"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Name", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "667a197963a0b497082703bd"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "fullname", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Fullname", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "667a19be63a0b49708270481"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "userEmail", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "User Email", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "669c8425ca8c03448366cbbc"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "company", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Company", "faker": {"module": "date", "subModule": "past", "args": "", "skip": false, "_id": "667a1ae07ef53c6881aba3e1"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "companies", "refDatabaseName": "companies", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "department", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Department", "faker": {"module": "date", "subModule": "past", "args": "", "skip": false, "_id": "667a1ae07ef53c6881aba3e2"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "departments", "refDatabaseName": "departments", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "section", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Section", "faker": {"module": "date", "subModule": "past", "args": "", "skip": false, "_id": "667a1ae07ef53c6881aba3e3"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "sections", "refDatabaseName": "sections", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "position", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Position", "faker": {"module": "date", "subModule": "past", "args": "", "skip": false, "_id": "667a1ae07ef53c6881aba3e4"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "positions", "refDatabaseName": "positions", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "supervisor", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Supervisor", "faker": {"module": "date", "subModule": "past", "args": "", "skip": false, "_id": "667a1ae07ef53c6881aba3e5"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "employees", "refDatabaseName": "employees", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "dateJoined", "type": "Date", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_calendar", "label": "Date Joined", "faker": {"module": "date", "subModule": "past", "args": "", "skip": false, "_id": "667a1ae07ef53c6881aba3e9"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "dateTerminated", "type": "Date", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_calendar", "label": "Date Terminated", "faker": {"module": "date", "subModule": "past", "args": "", "skip": false, "_id": "667a1ae07ef53c6881aba3ea"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "resigned", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Resigned", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "667a19be63a0b49708270482"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "empGroup", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Emp Group", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "667a19be63a0b49708270483"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "empCode", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Emp Code", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "667a19be63a0b49708270484"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "errors", "databaseName": "errors", "displayName": "Errors", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "serviceName", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "ServiceName", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "error", "type": "Object", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p", "label": "Error", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "message", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Message", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "stack", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "<PERSON><PERSON>", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "details", "type": "Object", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p", "label": "Details", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "errorsWH", "databaseName": "errors_w_h", "displayName": "Errors Warehouse", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "date", "type": "Date", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_calendar", "label": "Date", "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "details", "type": "Object", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p", "label": "Details", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "inbox", "databaseName": "inbox", "displayName": "Inbox", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "from", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "From", "faker": {"module": "date", "subModule": "past", "args": "1", "skip": false, "_id": "6698d51066ff1070873d4028"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "users", "refDatabaseName": "users", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "to<PERSON><PERSON>", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "ToUser", "faker": {"module": "date", "subModule": "past", "args": "1", "skip": false, "_id": "6698d51066ff1070873d4029"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "users", "refDatabaseName": "users", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "content", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Content", "faker": {"module": "date", "subModule": "past", "args": "1", "skip": false, "_id": "6698d51066ff1070873d402a"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "read", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p_boolean", "label": "Read", "faker": {"module": "date", "subModule": "past", "args": "1", "skip": false, "_id": "6698d51066ff1070873d402b"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "sent", "type": "Date", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "calendar_12", "label": "<PERSON><PERSON>", "faker": {"module": "date", "subModule": "past", "args": "1", "skip": false, "_id": "6698d51066ff1070873d402c"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "jobQues", "databaseName": "job_ques", "displayName": "<PERSON>", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "p", "label": "Name", "faker": {"module": "date", "subModule": "past", "args": "1", "skip": false, "_id": "6676cca4887c6fdbcd1a8e96"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "type", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "p", "label": "Type", "faker": {"module": "date", "subModule": "past", "args": "1", "skip": false, "_id": "6676cca4887c6fdbcd1a8e97"}, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "fromService", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "p", "label": "From Service", "faker": {"module": "date", "subModule": "past", "args": "1", "skip": false, "_id": "6676cca4887c6fdbcd1a8e98"}, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "toService", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "p", "label": "To Service", "faker": {"module": "date", "subModule": "past", "args": "1", "skip": false, "_id": "6676cca4887c6fdbcd1a8e99"}, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "start", "type": "Date", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "p_date", "label": "Start", "faker": {"module": "date", "subModule": "past", "args": "1", "skip": false, "_id": "6676cca4887c6fdbcd1a8e9a"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "end", "type": "Date", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "p_date", "label": "End", "faker": {"module": "date", "subModule": "past", "args": "1", "skip": false, "_id": "6676cca4887c6fdbcd1a8e9b"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "jobId", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "p", "label": "Job Id", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6691f1b581f9fe545b57799b"}, "min": 2, "max": 1000000, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "status", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": false, "required": false, "component": "p", "label": "Status", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6691f1b581f9fe545b57799c"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "dynaLoaderId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "DynaLoader Id", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "6692828f74080decd3f424c3"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "dynaL<PERSON>der", "refDatabaseName": "dyna_loader", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "email", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Email", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "6692828f74080decd3f424c4"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "mailQues", "databaseName": "mail_ques", "displayName": "Mail Ques", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "p", "label": "Name", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "667a1b6c7ef53c6881aba592"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "from", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "p", "label": "From", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "667a1b6c7ef53c6881aba595"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "recipients", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "chip", "label": "Recipients", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "667a1b6c7ef53c6881aba596"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "status", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": false, "required": false, "component": "tick", "label": "Status", "faker": {"module": "datatype", "subModule": "number", "args": "", "skip": false, "_id": "6691eb3ad8f74bfb83e24930"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "templateId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": false, "required": false, "component": "p", "label": "Template", "faker": {"module": "datatype", "subModule": "number", "args": "", "skip": false, "_id": "6691eb3ad8f74bfb83e24932"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "templates", "refDatabaseName": "templates", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "subject", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "inputTextarea", "label": "Subject", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66921b32ea966103bc264f54"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "content", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "inputTextarea", "label": "Content", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "667a1b6c7ef53c6881aba59a"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "jobId", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "p_number", "label": "Job Id", "faker": {"module": "datatype", "subModule": "number", "args": "", "skip": false, "_id": "6691eb3ad8f74bfb83e24934"}, "min": 2, "max": 1000000, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "errors", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "inputTextarea", "label": "Errors", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "667a1b6c7ef53c6881aba598"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "end", "type": "Date", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_calendar", "label": "End", "faker": {"module": "date", "subModule": "past", "args": "", "skip": false, "_id": "6692217bea966103bc265120"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "mails", "databaseName": "mails", "displayName": "Mail Logs", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "sentDateTime", "type": "Date", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_date", "label": "Sent Date Time", "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "sentStatus", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p_boolean", "label": "Sent Status", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "mailType", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Mail Type", "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "toHistory", "type": "Object", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p", "label": "To History", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"status_string_text": true, "subject_string_textarea": "first reminder", "body_string_textarea": "some body"}], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "mailWH", "databaseName": "mail_w_h", "displayName": "Mail Warehouse", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "date", "type": "Date", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_calendar", "label": "Date", "faker": {"module": "date", "subModule": "past", "args": "", "skip": false, "_id": "6698d70966ff1070873d4214"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "data", "type": "Object", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p", "label": "Data", "faker": {"module": "date", "subModule": "past", "args": "", "skip": false, "_id": "6698d70966ff1070873d4215"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "notifications", "databaseName": "notifications", "displayName": "Notifications", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "to<PERSON><PERSON>", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "ToUser", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "users", "refDatabaseName": "users", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "content", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Content", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "read", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p_boolean", "label": "Read", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "sent", "type": "Date", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "calendar_12", "label": "<PERSON><PERSON>", "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "permissionFields", "databaseName": "permission_fields", "displayName": "Field Permissions", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "servicePermissionId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "ServicePermissionId", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6698d98066ff1070873d43a0"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "permissionServices", "refDatabaseName": "permission_services", "relationshipType": "one-to-one", "identifierFieldName": ["service"]}}, {"fieldName": "fieldName", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Field Name", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "6663b132d551a24b5d0f8f2f"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "onCreate", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_boolean", "label": "OnCreate", "faker": {"module": "datatype", "subModule": "boolean", "skip": false, "_id": "666292a389606af2e7718d57"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "default": true, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "onUpdate", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_boolean", "label": "OnUpdate", "faker": {"module": "datatype", "subModule": "boolean", "skip": false, "_id": "666292a389606af2e7718d59"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "default": true, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "onDetail", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_boolean", "label": "OnDetail", "faker": {"module": "datatype", "subModule": "boolean", "skip": false, "_id": "666292a389606af2e7718d5c"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "default": true, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "onTable", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p_boolean", "label": "OnTable", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6698d98066ff1070873d43a5"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": true, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"userId_sers_name_121": "<PERSON><PERSON>", "serviceId_service_text": "users", "fieldId_string_text": "email", "read_boolean_boolean": true, "readAll_boolean_boolean": true, "updateAll_boolean_boolean": true, "updateOwn_boolean_boolean": true, "deleteAll_boolean_boolean": true, "deleteOwn_boolean_boolean": true}], "skip": true, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "permissionServices", "databaseName": "permission_services", "displayName": "Service Permissions", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "service", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Service", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66629e906e9ee9002730d9f3"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "create", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p_boolean", "label": "Create", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6698d8af66ff1070873d4294"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": true, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "read", "type": "Array", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p", "label": "Read", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6698d8af66ff1070873d4295"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "[\"all\",\"own\",\"subordinates\"]", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "update", "type": "Array", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p", "label": "Update", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6698d8af66ff1070873d4296"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "[\"all\",\"own\",\"subordinate\"]", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "delete", "type": "Array", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p", "label": "Delete", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6698d8af66ff1070873d4297"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "[\"all\",\"own\",\"subordinate\"]", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "profile", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Profile", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66629e906e9ee9002730d9f2"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "profiles", "refDatabaseName": "profiles", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "roleId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "RoleId", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6698d8af66ff1070873d4299"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "roles", "refDatabaseName": "roles", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "positionId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "PositionId", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6698d8af66ff1070873d429a"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "positions", "refDatabaseName": "positions", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "employeeId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "EmployeeId", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6698d8af66ff1070873d429b"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "employees", "refDatabaseName": "employees", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "userId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "UserId", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6698d8af66ff1070873d429c"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "users", "refDatabaseName": "users", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}], "layout": false, "seeder": [{"userId": "<PERSON><PERSON>", "serviceId_string_text": "users", "read_boolean_boolean": true, "readAll_boolean_boolean": true, "updateAll_boolean_boolean": true, "updateOwn_boolean_boolean": true, "deleteAll_boolean_boolean": true, "deleteOwn_boolean_boolean": true}], "skip": true, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "positions", "databaseName": "positions", "displayName": "Positions", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "roleId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Role", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "6663cf29d551a24b5d0f8f8f"}, "min": 10, "max": 20, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "roles", "refDatabaseName": "roles", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Name", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "6663cf29d551a24b5d0f8f90"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "description", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "inputTextarea", "label": "Description", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "6663cf29d551a24b5d0f8f91"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "abbr", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Abbr", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "6663cf29d551a24b5d0f8f92"}, "min": 2, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "isDefault", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "tick", "label": "Is default", "faker": {"module": "datatype", "subModule": "boolean", "skip": false, "_id": "666291d589606af2e771899f"}, "min": 3, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"roleId_roles_name_121": "staff", "name": "CEO", "description_string_textarea": "Chief Executive Officer", "abbr": "CEO", "isDefault_boolean_boolean": false}, {"roleId_roles_name_121": "developer", "name": "Tester", "description_string_textarea": "Chief Testing Officer", "abbr": "CTO", "isDefault_boolean_boolean": false}, {"roleId_roles_name_121": "external", "name": "Visitor", "description_string_textarea": "Visitor", "abbr": "VS", "isDefault_boolean_boolean": true}, {"roleId_roles_name_121": "developer", "name": "Developer", "description_string_textarea": "Software Developer", "abbr": "SD", "isDefault_boolean_boolean": false}], "skip": true, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "profiles", "databaseName": "profiles", "displayName": "Profiles", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Name", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "6669278e058354c658938e10"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "userId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "User", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666280dc314c91cc457fee16"}, "min": 10, "max": 20, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "users", "refDatabaseName": "users", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "image", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "image", "label": "Image", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66695eabc520c5f1671847b5"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "bio", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "inputTextarea", "label": "Bio", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666280dc314c91cc457fee17"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "department", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": true, "component": "dropdown", "label": "Department", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6665acd45520a12ab4a08bdd"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "departments", "refDatabaseName": "departments", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "hod", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": true, "component": "tick", "label": "Head of Department", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6665acd45520a12ab4a08be2"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "section", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Section", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666280dc314c91cc457fee1a"}, "min": 10, "max": 20, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "sections", "refDatabaseName": "sections", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "hos", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": true, "component": "tick", "label": "Head of Section", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6665acd45520a12ab4a08be3"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "position", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": true, "component": "dropdown", "label": "Position", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666280dc314c91cc457fee19"}, "min": 10, "max": 20, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "positions", "refDatabaseName": "positions", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "manager", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Manager", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "6669278e058354c658938e15"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "users", "refDatabaseName": "users", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "company", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": true, "component": "dropdown", "label": "Company", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6665acd45520a12ab4a08bd9"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "companies", "refDatabaseName": "companies", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "branch", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Branch", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6665acd45520a12ab4a08bdb"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "branches", "refDatabaseName": "branches", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "skills", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "chip", "label": "Skills", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666280dc314c91cc457fee18"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "address", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Address", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "666280dc314c91cc457fee1b"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "userAddresses", "refDatabaseName": "user_addresses", "relationshipType": "one-to-one", "identifierFieldName": ["Street1"]}}, {"fieldName": "phone", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Phone", "faker": {"module": "datatype", "subModule": "boolean", "args": "", "skip": false, "_id": "6665acd45520a12ab4a08bdf"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "userPhones", "refDatabaseName": "user_phones", "relationshipType": "one-to-one", "identifierFieldName": ["number"]}}], "layout": false, "seeder": [{"userId_users_name_121": "<PERSON><PERSON>", "bio": "Faucibus pulvinar elementum integer enim neque volutpat ac tincidunt vitae. Commodo odio aenean sed adipiscing diam donec adipiscing tristique. Eget felis eget nunc lobortis mattis aliquam faucibus purus in.", "skills": "Algorithms, Javascript, Python", "positionId_positions_name_121": "Developer", "sectionId_sections_name_121": "Software Development"}], "skip": true, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "roles", "databaseName": "roles", "displayName": "Roles", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Name", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940602"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "description", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "inputTextarea", "label": "Description", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940603"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "isDefault", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_boolean", "label": "Is default", "faker": {"module": "datatype", "subModule": "boolean", "skip": false, "_id": "6662922789606af2e7718bd3"}, "min": 3, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"name": "Staff", "description_string_textarea": "Internal users of the company", "isDefault_boolean_boolean": false, "isDefault": false, "description": "Internal Users of the company"}, {"name": "External", "description_string_textarea": "External users of the company", "isDefault_boolean_boolean": true, "isDefault": true, "description": "External user that have limited access to the system"}, {"name": "Developer", "description_string_textarea": "Software Developer of the application", "isDefault_boolean_boolean": false, "isDefault": false, "description": "For the developer to manage the bugs and maintenance"}, {"name": "Super", "description_string_textarea": "Super user of the application", "isDefault_boolean_boolean": false, "isDefault": false, "description": "Super user that is capable of all functions and features."}, {"name": "Admin", "description_string_textarea": "Administrator of the application", "isDefault_boolean_boolean": false, "isDefault": false, "description": "Internal staff user that is responsible for first level support and system maintenance."}], "skip": true, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "sections", "databaseName": "sections", "displayName": "Sections", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "departmentId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Department", "faker": {"skip": false, "_id": "666151d6eaafcce3f19405fd"}, "min": 10, "max": 20, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "departments", "refDatabaseName": "departments", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Name", "faker": {"skip": false, "_id": "666151d6eaafcce3f19405fe"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "code", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Code", "faker": {"skip": false, "_id": "666151d6eaafcce3f19405ff"}, "min": 2, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "isDefault", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_boolean", "label": "Is default", "faker": {"module": "datatype", "subModule": "boolean", "skip": false, "_id": "6662923c89606af2e7718c41"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"departmentId_departments_name_121": "IT", "name": "Software Department", "code_string_text": 1001, "isDefault_boolean_boolean": true}], "skip": true, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "staffinfo", "databaseName": "staffinfo", "displayName": "Staffinfo", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "empno", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_number", "label": "Empno", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c65893911e"}, "min": 0, "max": 1000000, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Name", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c65893911f"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "<PERSON><PERSON><PERSON>", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "<PERSON><PERSON><PERSON>", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c658939120"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "compcode", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_number", "label": "Compcode", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c658939121"}, "min": 0, "max": 10000000, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "compname", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Compname", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c658939122"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "deptcode", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Deptcode", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c658939123"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "deptdesc", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Deptdesc", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c658939124"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "sectcode", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_number", "label": "Sectcode", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c658939125"}, "min": 0, "max": 50035066, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "sectdesc", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Sectdesc", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c658939126"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "designation", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Designation", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c658939127"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "email", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Email", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c658939128"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "resign", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Resign", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c658939129"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "supervisor", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Supervisor", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c65893912a"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "datejoin", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_number", "label": "Datejoin", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c65893912b"}, "min": 0, "max": 10000000, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "empgroup", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Empgroup", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c65893912c"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "empgradecode", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Empgradecode", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c65893912d"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "terminationdate", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Terminationdate", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66692918058354c65893912e"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"empno": 8559, "name": "MOHD HELMY BIN GHAZALEE", "namenric": "MOHD HELMY BIN GHAZALEE", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2030, "deptdesc": "Production", "sectcode": 50025677, "sectdesc": "Production Unit-Post Press/SA", "designation": "Mailroom Technician 1", "email": "<EMAIL>", "resign": "False", "supervisor": "6668", "datejoin": 39272, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8554, "name": "SHARON LEE LI WEI", "namenric": "LEE LI WEI", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3160, "deptdesc": "Client Brand Mk", "sectcode": 50000101, "sectdesc": "Client Brand Marketing - General", "designation": "Senior General Manager, CBM", "email": "shar<PERSON><PERSON>@thestar.com.my", "resign": "False", "supervisor": "8741", "datejoin": 39295, "empgroup": "03 ", "empgradecode": "A2"}, {"empno": 8552, "name": "DEVI MANJIT KAUR", "namenric": "DEVI MANJIT KAUR A/P MOKHTIAR SINGH", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000129, "sectdesc": "Content-News", "designation": "Assistant Editor, News", "email": "devi<PERSON><PERSON>@thestar.com.my", "resign": "False", "supervisor": "8477", "datejoin": 39573, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8548, "name": "<PERSON><PERSON><PERSON>", "namenric": "MOHD KHAIRUL NIZAM BIN ISMAIL", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "TECH", "deptdesc": "Technology", "sectcode": 50026832, "sectdesc": "Tech-Software Engineering-Publishing", "designation": "IT Executive", "email": "m<PERSON><PERSON><PERSON>@thestar.com.my", "resign": "False", "supervisor": "7471", "datejoin": 39328, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8538, "name": "EUGENE MAHALINGAM", "namenric": "EUGENE A/L MAHALINGAM", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000130, "sectdesc": "Content-Business", "designation": "Assistant News Editor", "email": "eugen<PERSON>@thestar.com.my", "resign": "False", "supervisor": "6074", "datejoin": 39328, "empgroup": "03 ", "empgradecode": "E1"}, {"empno": 8530, "name": "SEELAN A/L KALAYCHEBUM", "namenric": "SEELAN A/L KALAYCHEBUM", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "PEOP", "deptdesc": "Group People", "sectcode": 50026830, "sectdesc": "GROUP PEOPLE DEPARTMENT", "designation": "Senior Clerical Assistant", "email": "<EMAIL>", "resign": "False", "supervisor": "70096", "datejoin": 39364, "empgroup": "04 ", "empgradecode": "U1"}, {"empno": 8528, "name": "FINTAN NG WEI LOONG", "namenric": "FINTAN NG WEI LOONG", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50034387, "sectdesc": "Content- StarBiz7", "designation": "Deputy Editor, Features", "email": "<EMAIL>", "resign": "False", "supervisor": "8188", "datejoin": 45048, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8510, "name": "CHAN TAK KONG", "namenric": "CHAN TAK KONG", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000145, "sectdesc": "Content-Photo", "designation": "Photographer Special Grade", "email": "<EMAIL>", "resign": "False", "supervisor": "5314", "datejoin": 39371, "empgroup": "05 ", "empgradecode": "J1"}, {"empno": 8506, "name": "LEE LI CHOO", "namenric": "LEE LI CHOO", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "TECH", "deptdesc": "Technology", "sectcode": 50026826, "sectdesc": "Tech-Software Engineering-Apps", "designation": "Assistant Manager, IT (Applications)", "email": "<EMAIL>", "resign": "False", "supervisor": "7183", "datejoin": 39510, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8505, "name": "WAN ZURIAH BT WAN ZAKWAN", "namenric": "WAN ZURIAH BT WAN ZAKWAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000126, "sectdesc": "Content-Administration", "designation": "Executive, Content Administration", "email": "<EMAIL>", "resign": "False", "supervisor": "8108", "datejoin": 39573, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8498, "name": "ARIFFUDDIN BIN ISHAK", "namenric": "ARIFFUDDIN BIN ISHAK", "compcode": 1230, "compname": "Magnet Bizz Sdn Bhd", "deptcode": "C100", "deptdesc": "Content", "sectcode": 50032002, "sectdesc": "MSTAR-Content", "designation": "Assistant News Editor", "email": "<EMAIL>", "resign": "False", "supervisor": "8264", "datejoin": 39539, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8482, "name": "KUMAR A/L M. PERUMAL", "namenric": "KUMAR A/L M. PERUMAL", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3060, "deptdesc": "Prop Mgt & Devt", "sectcode": 50027896, "sectdesc": "Prop Mgmt & Dev-BPS", "designation": "Senior Handyman", "resign": "False", "supervisor": "8170", "datejoin": 39539, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8480, "name": "PETER YAP", "namenric": "YAP YING FAH", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "PROC", "deptdesc": "Procurement", "sectcode": 50026828, "sectdesc": "PROCUREMENT UNIT", "designation": "Senior Manager, Procurement", "email": "<EMAIL>", "resign": "False", "supervisor": "8170", "datejoin": 39539, "empgroup": "03 ", "empgradecode": "M1"}, {"empno": 8477, "name": "MARAN A/L BALASTAN", "namenric": "MARAN A/L BALASTAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3170, "deptdesc": "HSSE", "sectcode": 50028090, "sectdesc": "HSSE-Transport", "designation": "Senior Driver, Light Vehicle", "email": "<EMAIL>", "resign": "False", "supervisor": "7383", "datejoin": 39541, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8476, "name": "PUVINDRAN A/L KANEES", "namenric": "PUVINDRAN A/L KANEES", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2030, "deptdesc": "Production", "sectcode": 50025673, "sectdesc": "Production Unit-Admin/Facilities/SA", "designation": "Senior Asst. Storekeeper", "email": "<EMAIL>", "resign": "False", "supervisor": "6668", "datejoin": 39573, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8475, "name": "GOH KONG JENG", "namenric": "GOH KONG JENG", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2030, "deptdesc": "Production", "sectcode": 50025679, "sectdesc": "Production Unit-PrePress/SA", "designation": "Assistant Manager, Press Operation", "email": "<EMAIL>", "resign": "False", "supervisor": "5624", "datejoin": 39570, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8469, "name": "KHAIRUL NIZAM BIN YAAKUB", "namenric": "KHAIRUL NIZAM BIN YA'AKUB", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2030, "deptdesc": "Production", "sectcode": 50025673, "sectdesc": "Production Unit-Admin/Facilities/SA", "designation": "Assistant Manager, Press Operation", "email": "nizkha<PERSON><PERSON>@thestar.com.my", "resign": "False", "supervisor": "6668", "datejoin": 39570, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8464, "name": "NURBAITI HANIM BINTI HAMDAN", "namenric": "NURBAITI HANIM BINTI HAMDAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000129, "sectdesc": "Content-News", "designation": "Assistant News Editor", "email": "<EMAIL>", "resign": "False", "supervisor": "5899", "datejoin": 39612, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8454, "name": "SHONA NAIR A/P RAVINDRAN", "namenric": "SHONA NAIR A/P RAVINDRAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2010, "deptdesc": "Circulation", "sectcode": 50027933, "sectdesc": "Circulation Unit-General", "designation": "Clerical Asst 1", "email": "<EMAIL>", "resign": "False", "supervisor": "7436", "datejoin": 39615, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8449, "name": "ROWENA CHUA", "namenric": "CHUA WEN HUI", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000135, "sectdesc": "Content-Education", "designation": "Assistant News Editor", "email": "<EMAIL>", "resign": "False", "supervisor": "2217", "datejoin": 41799, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8445, "name": "LESTER WYETH KONG YIT SIN", "namenric": "LESTER WYETH KONG YIT SIN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000129, "sectdesc": "Content-News", "designation": "Specialist Writer", "email": "<EMAIL>", "resign": "False", "supervisor": "5899", "datejoin": 45369, "empgroup": "03 ", "empgradecode": "M1"}, {"empno": 8440, "name": "FRANCIS LEE JIH CHUAN", "namenric": "LEE JIH CHUAN", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "TECH", "deptdesc": "Technology", "sectcode": 50026825, "sectdesc": "Tech-Infrastructure", "designation": "Senior Cyber Security Officer", "email": "<EMAIL>", "resign": "False", "supervisor": "7071", "datejoin": 43745, "empgroup": "03 ", "empgradecode": "E1"}, {"empno": 8432, "name": "ARIVALAGAN A/L MANOHARAN", "namenric": "ARIVALAGAN A/L MANOHARAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3170, "deptdesc": "HSSE", "sectcode": 50028089, "sectdesc": "HSSE-Security", "designation": "Assistant Security Supervisor", "resign": "False", "supervisor": "7383", "datejoin": 39643, "empgroup": "07 ", "empgradecode": "S1"}, {"empno": 8418, "name": "CECILIA KOK", "namenric": "CECILIA KOK MEI LING", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000130, "sectdesc": "Content-Business", "designation": "Deputy News Editor", "email": "<EMAIL>", "resign": "False", "supervisor": "6074", "datejoin": 45180, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8410, "name": "JOWYN KWAN", "namenric": "KWAN CHING PUI", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000126, "sectdesc": "Content-Administration", "designation": "Manager, Content Administration", "email": "<EMAIL>", "resign": "False", "supervisor": "6630", "datejoin": 39652, "empgroup": "03 ", "empgradecode": "M2"}, {"empno": 8397, "name": "YEOW HONG SIAN", "namenric": "YEOW HONG SIAN", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "FIN ", "deptdesc": "Finance", "sectcode": 50026813, "sectdesc": "Finance-PTP to RTR", "designation": "Manager, PTP", "email": "<EMAIL>", "resign": "False", "supervisor": "6965", "datejoin": 39661, "empgroup": "03 ", "empgradecode": "M2"}, {"empno": 8390, "name": "YUEN MEIKENG", "namenric": "YUEN MEIKENG", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000129, "sectdesc": "Content-News", "designation": "Chief Reporter", "email": "<EMAIL>", "resign": "False", "supervisor": "8505", "datejoin": 39671, "empgroup": "03 ", "empgradecode": "E1"}, {"empno": 8381, "name": "CHAI MING JYE", "namenric": "CHAI MING JYE", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2070, "deptdesc": "Audit", "sectcode": 50000182, "sectdesc": "INTERNAL AUDIT DEPARTMENT", "designation": "General Manager, Internal Audit", "email": "<EMAIL>", "resign": "False", "supervisor": "70650", "datejoin": 39671, "empgroup": "01 ", "empgradecode": "A2"}, {"empno": 8380, "name": "SANTHAKUMAR A/L MAHALINGAM", "namenric": "SANTHAKUMAR A/L MAHALINGAM", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000129, "sectdesc": "Content-News", "designation": "Chief Reporter", "email": "<EMAIL>", "resign": "False", "supervisor": "5899", "datejoin": 39755, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8377, "name": "THANABALAN A/L SIVALINGAM", "namenric": "THANABALAN A/L SIVALINGAM", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2030, "deptdesc": "Production", "sectcode": 50025673, "sectdesc": "Production Unit-Admin/Facilities/SA", "designation": "Engineering Tech. 1", "email": "<EMAIL>", "resign": "False", "supervisor": "6668", "datejoin": 39783, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8373, "name": "WONG SIN LAN", "namenric": "WONG SIN LAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 1010, "deptdesc": "Corporate", "sectcode": 50000209, "sectdesc": "Corp - General", "designation": "Chief Office Helper/Messenger", "resign": "False", "supervisor": "8656", "datejoin": 39965, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8372, "name": "PATRICK CHIN KA JUN", "namenric": "PATRICK CHIN KA JUN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50028072, "sectdesc": "Content-Video Central", "designation": "Assistant Technical Producer", "email": "patrick<PERSON><EMAIL>", "resign": "False", "supervisor": "7994", "datejoin": 40337, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8370, "name": "<PERSON><PERSON>", "namenric": "YEO ENG SIANG", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3060, "deptdesc": "Prop Mgt & Devt", "sectcode": 50027907, "sectdesc": "Prop Mgmt & Dev-General", "designation": "General Manager, Property Management", "email": "<EMAIL>", "resign": "False", "supervisor": "70650", "datejoin": 45359, "empgroup": "01 ", "empgradecode": "A2"}, {"empno": 8362, "name": "<PERSON><PERSON>", "namenric": "SOH SZE JEAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 1010, "deptdesc": "Corporate", "sectcode": 50000209, "sectdesc": "Corp - General", "designation": "Senior General Counsel", "email": "sze<PERSON><EMAIL>", "resign": "False", "supervisor": "70650", "datejoin": 39995, "empgroup": "01 ", "empgradecode": "A2"}, {"empno": 8356, "name": "ROZAID BIN ABDUL RAHMAN", "namenric": "ROZAID BIN ABDUL RAHMAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000126, "sectdesc": "Content-Administration", "designation": "Group Consulting Editor", "email": "<EMAIL>", "resign": "False", "supervisor": "5761", "datejoin": 44928, "empgroup": "01 ", "empgradecode": "A2"}, {"empno": 8352, "name": "MUHAMMAD HAFEEZ BIN AMINUDDIN", "namenric": "MUHAMMAD HAFEEZ BIN AMINUDDIN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000132, "sectdesc": "Content-Design & Creative", "designation": "Chief Designer", "email": "<EMAIL>", "resign": "False", "supervisor": "6563", "datejoin": 40035, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8345, "name": "RISEN JAYA SEELAN DENNIS", "namenric": "RISEN JAYA SEELAN DENNIS", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50034387, "sectdesc": "Content- StarBiz7", "designation": "Features Editor, Business", "email": "<EMAIL>", "resign": "False", "supervisor": "6074", "datejoin": 44938, "empgroup": "03 ", "empgradecode": "M2"}, {"empno": 8332, "name": "LOH QING WEN", "namenric": "LOH QING WEN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3160, "deptdesc": "Client Brand Mk", "sectcode": 50030302, "sectdesc": "Client Brand Marketing-Programmatic", "designation": "Senior Executive, Customer Relations Mgt", "email": "<EMAIL>", "resign": "False", "supervisor": "70442", "datejoin": 40087, "empgroup": "03 ", "empgradecode": "E1"}, {"empno": 8330, "name": "KU SITI 'AISHAH BTE KU MOKHTAR", "namenric": "KU SITI 'AISHAH BTE KU MOKHTAR", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2040, "deptdesc": "Administration", "sectcode": 50028087, "sectdesc": "Administration-Telephone Operators", "designation": "Telephonist", "resign": "False", "supervisor": "8656", "datejoin": 40087, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8328, "name": "RAZZIAH BINTI ABDUL RASHID", "namenric": "RAZZIAH BINTI ABDUL RASHID", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000132, "sectdesc": "Content-Design & Creative", "designation": "Senior Designer", "email": "<EMAIL>", "resign": "False", "supervisor": "6563", "datejoin": 40148, "empgroup": "03 ", "empgradecode": "E1"}, {"empno": 8315, "name": "MOHD AL QAYUM BIN AZIZI", "namenric": "MOHD AL QAYUM BIN AZIZI", "compcode": 1230, "compname": "Magnet Bizz Sdn Bhd", "deptcode": "C100", "deptdesc": "Content", "sectcode": 50032002, "sectdesc": "MSTAR-Content", "designation": "Senior Writer", "email": "qayuma<PERSON><PERSON>@thestar.com.my", "resign": "False", "supervisor": "8264", "datejoin": 40148, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8312, "name": "MOHD KHAIRUL BIN MUHAMAD", "namenric": "MOHD KHAIRUL BIN MUHAMAD", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000132, "sectdesc": "Content-Design & Creative", "designation": "Assistant Chief Designer", "email": "<EMAIL>", "resign": "False", "supervisor": "6626", "datejoin": 40161, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8305, "name": "<PERSON>", "namenric": "JULIET CHOW YOKE JING", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3110, "deptdesc": "Analytics", "sectcode": 50035066, "sectdesc": "Analytics-Product Strategy & Management", "designation": "Senior Manager, News Vertical", "email": "<EMAIL>", "resign": "False", "supervisor": "6464", "datejoin": 40196, "empgroup": "03 ", "empgradecode": "M2"}, {"empno": 8299, "name": "MOHD HUZAINI BIN CHE MAHMOOD", "namenric": "MOHD HUZAINI BIN CHE MAHMOOD", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2030, "deptdesc": "Production", "sectcode": 50025674, "sectdesc": "Production Unit-Press/SA", "designation": "Executive, Press Operation", "email": "h<PERSON><PERSON>@thestar.com.my", "resign": "False", "supervisor": "5624", "datejoin": 40210, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8290, "name": "JAYA CHRISTINA A/P MUNIANDY", "namenric": "JAYA CHRISTINA A/P MUNIANDY", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "TECH", "deptdesc": "Technology", "sectcode": 50027890, "sectdesc": "Tech-Software Engineering-Web Devt", "designation": "Senior Web Developer", "resign": "False", "supervisor": "7746", "datejoin": 40269, "empgroup": "03 ", "empgradecode": "E1"}, {"empno": 8271, "name": "SURENDRAN VERASENA", "namenric": "SURENDRAN A/L VERASENA", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "PEOP", "deptdesc": "Group People", "sectcode": 50026830, "sectdesc": "GROUP PEOPLE DEPARTMENT", "designation": "Senior Executive, Group People", "email": "<EMAIL>", "resign": "False", "supervisor": "7686", "datejoin": 40269, "empgroup": "03 ", "empgradecode": "E1"}, {"empno": 8266, "name": "NOR ZUBIDAH BINTI MUSTAFA", "namenric": "NOR ZUBIDAH BINTI MUSTAFA", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50010815, "sectdesc": "Content-CP/Kuntum", "designation": "Journalist Special Grade", "email": "<EMAIL>", "resign": "False", "supervisor": "8809", "datejoin": 40283, "empgroup": "05 ", "empgradecode": "J1"}, {"empno": 8264, "name": "IZRAYATUL MAIZURA BT ABD RAHMAN", "namenric": "IZRAYATUL MAIZURA BT ABD RAHMAN", "compcode": 1230, "compname": "Magnet Bizz Sdn Bhd", "deptcode": "C100", "deptdesc": "Content", "sectcode": 50032002, "sectdesc": "MSTAR-Content", "designation": "Deputy News Editor", "email": "i<PERSON><EMAIL>", "resign": "False", "supervisor": "8264", "datejoin": 40301, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8258, "name": "IWAN SHU-ASWAD BIN SHUAIB", "namenric": "IWAN SHU-ASWAD BIN SHUAIB", "compcode": 1230, "compname": "Magnet Bizz Sdn Bhd", "deptcode": "C100", "deptdesc": "Content", "sectcode": 50032002, "sectdesc": "MSTAR-Content", "designation": "Senior Writer", "email": "<EMAIL>", "resign": "False", "supervisor": "8264", "datejoin": 40301, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8254, "name": "RANDEEP SINGH A/L SUKHDEV SINGH", "namenric": "RANDEEP SINGH A/L SUKHDEV SINGH", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000130, "sectdesc": "Content-Business", "designation": "Chief Sub Editor (Business)", "email": "<EMAIL>", "resign": "False", "supervisor": "6074", "datejoin": 40301, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8253, "name": "HAZLIN BINTI AMINUDIN", "namenric": "HAZLIN BINTI AMINUDIN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50010869, "sectdesc": "Content-Subs", "designation": "Assistant Chief Sub Editor", "email": "<EMAIL>", "resign": "False", "supervisor": "5578", "datejoin": 40301, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8252, "name": "SITI AISHAH BINTI SHAH BANI", "namenric": "SITI AISHAH BINTI SHAH BANI", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50010815, "sectdesc": "Content-CP/Kuntum", "designation": "Journalist <PERSON><PERSON><PERSON>", "email": "<EMAIL>", "resign": "False", "supervisor": "8809", "datejoin": 40308, "empgroup": "05 ", "empgradecode": "J1"}, {"empno": 8251, "name": "NIEZAM ABDULLAH", "namenric": "MOHAMMAD NIZAM BIN ABDULLAH", "compcode": 1230, "compname": "Magnet Bizz Sdn Bhd", "deptcode": "C100", "deptdesc": "Content", "sectcode": 50032002, "sectdesc": "MSTAR-Content", "designation": "Chief News Editor", "email": "<EMAIL>", "resign": "False", "supervisor": "5761", "datejoin": 40330, "empgroup": "03 ", "empgradecode": "M1"}, {"empno": 8248, "name": "AIDA BINTI AHMAD", "namenric": "AIDA BINTI AHMAD", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000139, "sectdesc": "Content-Metro", "designation": "Chief Reporter", "email": "<EMAIL>", "resign": "False", "supervisor": "5619", "datejoin": 40330, "empgroup": "03 ", "empgradecode": "E1"}, {"empno": 8245, "name": "ONG HAN SEAN", "namenric": "ONG HAN SEAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000129, "sectdesc": "Content-News", "designation": "Senior Writer", "email": "<EMAIL>", "resign": "False", "supervisor": "5899", "datejoin": 40360, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8240, "name": "TEE TZE YEE", "namenric": "TEE TZE YEE", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 1010, "deptdesc": "Corporate", "sectcode": 50000209, "sectdesc": "Corp - General", "designation": "Senior Manager, Legal", "email": "<EMAIL>", "resign": "False", "supervisor": "8173", "datejoin": 42366, "empgroup": "03 ", "empgradecode": "M1"}, {"empno": 8221, "name": "ELAINE CHENG XIAO FUNN", "namenric": "ELAINE CHENG XIAO FUNN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50028072, "sectdesc": "Content-Video Central", "designation": "Senior Chief Video Editor", "email": "el<PERSON><PERSON>@thestar.com.my", "resign": "False", "supervisor": "8899", "datejoin": 40392, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8218, "name": "YAP WAN YING", "namenric": "YAP WAN YING", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2010, "deptdesc": "Circulation", "sectcode": 50027933, "sectdesc": "Circulation Unit-General", "designation": "Manager, Circulation", "email": "<EMAIL>", "resign": "False", "supervisor": "70301", "datejoin": 40399, "empgroup": "03 ", "empgradecode": "M2"}, {"empno": 8213, "name": "NAZLIN BINTI YUSOFF", "namenric": "NAZLIN BINTI YUSOFF", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "PEOP", "deptdesc": "Group People", "sectcode": 50026830, "sectdesc": "GROUP PEOPLE DEPARTMENT", "designation": "Executive, Group People", "email": "<EMAIL>", "resign": "False", "supervisor": "3353", "datejoin": 40413, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8207, "name": "<PERSON>", "namenric": "JESSIE LIM YU MIN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3160, "deptdesc": "Client Brand Mk", "sectcode": 50032577, "sectdesc": "Client Brand Marketing-SMG Brand Studio", "designation": "Senior Editor, SMG Brand Studio", "email": "<EMAIL>", "resign": "False", "supervisor": "70154", "datejoin": 45048, "empgroup": "03 ", "empgradecode": "M1"}, {"empno": 8204, "name": "JOEL A/L JOHN ALEXANDER", "namenric": "JOEL A/L JOHN ALEXANDER", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000142, "sectdesc": "Content-Sports", "designation": "Assistant Chief Sub-Editor", "email": "<EMAIL>", "resign": "False", "supervisor": "6858", "datejoin": 40492, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8192, "name": "RAHIMY BIN A RAHIM", "namenric": "RAHIMY BIN A RAHIM", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000129, "sectdesc": "Content-News", "designation": "Chief Reporter", "email": "rahi<PERSON><PERSON>@thestar.com.my", "resign": "False", "supervisor": "5899", "datejoin": 40497, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8190, "name": "KOW GUAN SIN", "namenric": "KOW GUAN SIN", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "TECH", "deptdesc": "Technology", "sectcode": 50026826, "sectdesc": "Tech-Software Engineering-Apps", "designation": "IT Analyst (Applications)", "email": "<EMAIL>", "resign": "False", "supervisor": "7183", "datejoin": 40513, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8188, "name": "NURUL AMNI BINTI MAT ARIFFIN", "namenric": "NURUL AMNI BINTI MAT ARIFFIN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000132, "sectdesc": "Content-Design & Creative", "designation": "Senior Designer", "email": "<EMAIL>", "resign": "False", "supervisor": "6626", "datejoin": 40548, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8185, "name": "TEOH SU-LIN", "namenric": "TEOH SU-LIN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3160, "deptdesc": "Client Brand Mk", "sectcode": 50032577, "sectdesc": "Client Brand Marketing-SMG Brand Studio", "designation": "Copy Editor, SMG Brand Studio", "email": "<EMAIL>", "resign": "False", "supervisor": "8315", "datejoin": 40581, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8184, "name": "VIJENTHI NAIR A/P BAILAND", "namenric": "VIJENTHI NAIR A/P BAILAND", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000139, "sectdesc": "Content-Metro", "designation": "Journalist <PERSON><PERSON><PERSON>", "email": "<EMAIL>", "resign": "False", "supervisor": "5619", "datejoin": 40603, "empgroup": "05 ", "empgradecode": "J1"}, {"empno": 8173, "name": "KIMBERLY HOH", "namenric": "HOH YIK SIEW", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 1000, "deptdesc": "General Management", "sectcode": 50000211, "sectdesc": "General Management - PJ", "designation": "Senior Group Company Secretary", "email": "<EMAIL>", "resign": "False", "supervisor": "70650", "datejoin": 40618, "empgroup": "03 ", "empgradecode": "A2"}, {"empno": 8170, "name": "AUSTIN MANUEL CAMOENS", "namenric": "AUSTIN MANUEL CAMOENS", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000129, "sectdesc": "Content-News", "designation": "Journalist Special Grade", "email": "<EMAIL>", "resign": "False", "supervisor": "5899", "datejoin": 40637, "empgroup": "05 ", "empgradecode": "J1"}, {"empno": 8166, "name": "FAIHAN GHANI", "namenric": "MOHD FAIHAN BIN MOHD GHANI", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000145, "sectdesc": "Content-Photo", "designation": "Photographer <PERSON><PERSON><PERSON>", "email": "<EMAIL>", "resign": "False", "supervisor": "5314", "datejoin": 40637, "empgroup": "05 ", "empgradecode": "J1"}, {"empno": 8163, "name": "AHMAD IZZRAFIQ BIN ALIAS", "namenric": "AHMAD IZZRAFIQ BIN ALIAS", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000145, "sectdesc": "Content-Photo", "designation": "Photographer <PERSON><PERSON><PERSON>", "email": "<EMAIL>", "resign": "False", "supervisor": "5314", "datejoin": 40637, "empgroup": "05 ", "empgradecode": "J1"}, {"empno": 8139, "name": "ADAM G", "namenric": "SHREE GANESAN SUNDRARAJ", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "TECH", "deptdesc": "Technology", "sectcode": 50026825, "sectdesc": "Tech-Infrastructure", "designation": "Systems Administrator", "email": "<EMAIL>", "resign": "False", "supervisor": "7071", "datejoin": 40666, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8130, "name": "REMANATHAN A/L NARANISAMY", "namenric": "REMANATHAN A/L NARANISAMY", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3090, "deptdesc": "Print", "sectcode": 50025677, "sectdesc": "Production Unit-Post Press/SA", "designation": "Mailroom Technician 1", "email": "<EMAIL>", "resign": "False", "supervisor": "6668", "datejoin": 40666, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8117, "name": "S. GUNASEGARAN A/L SUBRAMANIAM", "namenric": "S. GUNASEGARAN A/L SUBRAMANIAM", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3170, "deptdesc": "HSSE", "sectcode": 50028090, "sectdesc": "HSSE-Transport", "designation": "Assistant Chief Driver, Light Vehicle", "email": "<EMAIL>", "resign": "False", "supervisor": "7383", "datejoin": 40666, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8116, "name": "MANIMARAN A/L SHALIVAHANAN", "namenric": "MANIMARAN A/L SHALIVAHANAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2030, "deptdesc": "Production", "sectcode": 50025677, "sectdesc": "Production Unit-Post Press/SA", "designation": "Clerical Assistant 1", "email": "<EMAIL>", "resign": "False", "supervisor": "8794", "datejoin": 40695, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8113, "name": "FUNG CHEN HOONG", "namenric": "FUNG CHEN HOONG", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3160, "deptdesc": "Client Brand Mk", "sectcode": 50027964, "sectdesc": "Client Brand Marketing-PJ", "designation": "Senior Team Lead, Client Brand Marketing", "email": "<EMAIL>", "resign": "False", "supervisor": "8008", "datejoin": 40717, "empgroup": "03 ", "empgradecode": "M2"}, {"empno": 8108, "name": "SHAQIRAH BINTI KAMAL AZMAN", "namenric": "SHAQIRAH BINTI KAMAL AZMAN", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "TECH", "deptdesc": "Technology", "sectcode": 50027891, "sectdesc": "Tech-Software Engineering-Graphics", "designation": "Graphic Designer", "email": "sha<PERSON><PERSON>@thestar.com.my", "resign": "False", "supervisor": "50017", "datejoin": 40756, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8107, "name": "ANGELIN YEOH XIN LIN", "namenric": "ANGELIN YEOH XIN LIN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50015226, "sectdesc": "Content-Features Central", "designation": "Journalist Special Grade", "email": "<EMAIL>", "resign": "False", "supervisor": "6130", "datejoin": 41071, "empgroup": "05 ", "empgradecode": "J1"}, {"empno": 8104, "name": "GOMATHAN A/L BALACHANDRAN", "namenric": "GOMATHAN A/L BALACHANDRAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2010, "deptdesc": "Circulation", "sectcode": 50000118, "sectdesc": "Cir - General", "designation": "Clerical Asst 1", "email": "<EMAIL>", "resign": "False", "supervisor": "7436", "datejoin": 40791, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8102, "name": "MOHAMAD SAIFUDIN BIN AMTIYAJ AHMAD", "namenric": "MOHAMAD SAIFUDIN BIN AMTIYAJ AHMAD", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2030, "deptdesc": "Production", "sectcode": 50025674, "sectdesc": "Production Unit-Press/SA", "designation": "Senior Engineering Technician", "email": "<EMAIL>", "resign": "False", "supervisor": "3958", "datejoin": 40805, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8101, "name": "PATRICK FONG WHYE KHEONG", "namenric": "PATRICK FONG WHYE KHEONG", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000139, "sectdesc": "Content-Metro", "designation": "Assistant Editor, Metro", "email": "<EMAIL>", "resign": "True", "supervisor": "5619", "datejoin": 40819, "empgroup": "03 ", "empgradecode": "M3", "terminationdate": "2024-03-29"}, {"empno": 8089, "name": "SYAZWIN HAYATI BINTI MOHAMED RIDZWAN", "namenric": "SYAZWIN HAYATI BINTI MOHAMED RIDZWAN", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "TECH", "deptdesc": "Technology", "sectcode": 50027891, "sectdesc": "Tech-Software Engineering-Graphics", "designation": "Team Lead, Designer", "email": "<EMAIL>", "resign": "False", "supervisor": "50017", "datejoin": 40819, "empgroup": "03 ", "empgradecode": "E1"}, {"empno": 8088, "name": "KHOO CHIN HIAN @ DANIEL KHOO", "namenric": "KHOO CHIN HIAN @ DANIEL KHOO", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000130, "sectdesc": "Content-Business", "designation": "Senior Writer", "email": "<EMAIL>", "resign": "False", "supervisor": "6074", "datejoin": 40855, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8085, "name": "PRISCILLA ANAK RICKY", "namenric": "PRISCILLA ANAK RICKY", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3160, "deptdesc": "Client Brand Mk", "sectcode": 50027963, "sectdesc": "Client Brand Marketing-Support", "designation": "Clerical Asst 1", "email": "<EMAIL>", "resign": "False", "supervisor": "5577", "datejoin": 40878, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8077, "name": "ANDREW LIM SWEE SOON", "namenric": "LIM SWEE SOON", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2010, "deptdesc": "Circulation", "sectcode": 50027933, "sectdesc": "Circulation Unit-General", "designation": "Manager, Circulation", "email": "andrew<PERSON>@thestar.com.my", "resign": "False", "supervisor": "70301", "datejoin": 40882, "empgroup": "03 ", "empgradecode": "M2"}, {"empno": 8076, "name": "HASSIM BIN ABD RANI", "namenric": "HASSIM BIN ABD RANI", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2030, "deptdesc": "Production", "sectcode": 50025677, "sectdesc": "Production Unit-Post Press/SA", "designation": "Executive, Post Press Operation", "email": "<EMAIL>", "resign": "False", "supervisor": "6668", "datejoin": 40940, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8071, "name": "PRITHIVIRAJ A/L HARIKISHNAN", "namenric": "PRITHIVIRAJ A/L HARIKISHNAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2010, "deptdesc": "Circulation", "sectcode": 50000118, "sectdesc": "Cir - General", "designation": "Marketing Rep 1", "email": "<EMAIL>", "resign": "False", "supervisor": "8617", "datejoin": 40940, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8066, "name": "MICHELLE TAM LI PENG", "namenric": "MICHELLE TAM LI PENG", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50029029, "sectdesc": "Content-Social Media Unit", "designation": "Editor, TSOL", "email": "<EMAIL>", "resign": "False", "supervisor": "5761", "datejoin": 40959, "empgroup": "03 ", "empgradecode": "M2"}, {"empno": 8064, "name": "JENNY SEE MAY CHING", "namenric": "JENNY SEE MAY CHING", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3130, "deptdesc": "Prod&Innovation", "sectcode": 50032431, "sectdesc": "PI-Subscription", "designation": "Head - News Subscription", "email": "jenny<PERSON>@thestar.com.my", "resign": "False", "supervisor": "70301", "datejoin": 40982, "empgroup": "03 ", "empgradecode": "M2"}, {"empno": 8062, "name": "DANIAL FAHMI BIN MOHSEN", "namenric": "DANIAL FAHMI BIN MOHSEN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3160, "deptdesc": "Client Brand Mk", "sectcode": 50027963, "sectdesc": "Client Brand Marketing-Support", "designation": "Clerical Asst 2", "email": "<EMAIL>", "resign": "False", "supervisor": "5577", "datejoin": 40973, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8061, "name": "NUR SYAFIQAH BINTI NOR RASHID", "namenric": "NUR SYAFIQAH BINTI NOR RASHID", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3160, "deptdesc": "Client Brand Mk", "sectcode": 50027963, "sectdesc": "Client Brand Marketing-Support", "designation": "Snr Exec, Client Brand Marketing Support", "email": "<EMAIL>", "resign": "False", "supervisor": "6095", "datejoin": 41031, "empgroup": "03 ", "empgradecode": "E1"}, {"empno": 8057, "name": "ABDUL RAZAK BIN AHMAD IDRIS", "namenric": "ABDUL RAZAK BIN AHMAD IDRIS", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000129, "sectdesc": "Content-News", "designation": "Senior News Editor", "email": "<EMAIL>", "resign": "False", "supervisor": "5899", "datejoin": 41051, "empgroup": "03 ", "empgradecode": "M1"}, {"empno": 8056, "name": "TEOH BAN PIN", "namenric": "TEOH BAN PIN", "compcode": 1700, "compname": "SMG Business Services Sdn Bhd", "deptcode": "TECH", "deptdesc": "Technology", "sectcode": 50026826, "sectdesc": "Tech-Software Engineering-Apps", "designation": "Assistant Manager, IT (Applications)", "email": "<EMAIL>", "resign": "False", "supervisor": "7183", "datejoin": 41061, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8032, "name": "LIM JIAN YANG", "namenric": "LIM JIAN YANG", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000139, "sectdesc": "Content-Metro", "designation": "Senior Writer", "email": "<EMAIL>", "resign": "False", "supervisor": "5619", "datejoin": 41071, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8031, "name": "JOHAN BIN MOHD HULAIMI", "namenric": "JOHAN BIN MOHD HULAIMI", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000129, "sectdesc": "Content-News", "designation": "Assistant Editor, Online", "email": "joh<PERSON><PERSON><PERSON><PERSON>@thestar.com.my", "resign": "False", "supervisor": "5899", "datejoin": 41092, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8029, "name": "AVINESHWARAN A/L TAHARUMALENGAM", "namenric": "AVINESHWARAN A/L TAHARUMALENGAM", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50000142, "sectdesc": "Content-Sports", "designation": "Senior Writer", "email": "a<PERSON><PERSON>@thestar.com.my", "resign": "False", "supervisor": "6858", "datejoin": 43782, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8025, "name": "BERVIN CHEONG PECK YANG", "namenric": "BERVIN CHEONG PECK YANG", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50015226, "sectdesc": "Content-Features Central", "designation": "Journalist <PERSON><PERSON><PERSON>", "email": "<EMAIL>", "resign": "False", "supervisor": "7658", "datejoin": 41122, "empgroup": "05 ", "empgradecode": "J1"}, {"empno": 8021, "name": "NAVALAN A/L T.SUBRAMANIAM", "namenric": "NAVALAN A/L T.SUBRAMANIAM", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50010869, "sectdesc": "Content-Subs", "designation": "Assistant Editor, Production", "resign": "False", "supervisor": "5578", "datejoin": 41134, "empgroup": "03 ", "empgradecode": "M3"}, {"empno": 8014, "name": "THIVAGAR A/L KRISHNAN", "namenric": "THIVAGAR A/L KRISHNAN", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 2030, "deptdesc": "Production", "sectcode": 50025677, "sectdesc": "Production Unit-Post Press/SA", "designation": "Clerical Assistant 2", "email": "<EMAIL>", "resign": "False", "supervisor": "8794", "datejoin": 41155, "empgroup": "06 ", "empgradecode": "U1"}, {"empno": 8008, "name": "MING TEOH", "namenric": "JULIE TEOH AI MING", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3070, "deptdesc": "Content", "sectcode": 50015226, "sectdesc": "Content-Features Central", "designation": "Senior Writer", "email": "<EMAIL>", "resign": "False", "supervisor": "6130", "datejoin": 41155, "empgroup": "03 ", "empgradecode": "E2"}, {"empno": 8002, "name": "AFIFAH BINTI ABDUL LATIB", "namenric": "AFIFAH BINTI ABDUL LATIB", "compcode": 1105, "compname": "Star Media Group Berhad", "deptcode": 3160, "deptdesc": "Client Brand Mk", "sectcode": 50027963, "sectdesc": "Client Brand Marketing-Support", "designation": "Executive, Advertisement Sales Support", "email": "<EMAIL>", "resign": "False", "supervisor": "1878", "datejoin": 41170, "empgroup": "03 ", "empgradecode": "E2"}], "skip": true, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "superior", "databaseName": "superior", "displayName": "Superior", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "superior", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_number", "label": "Superior", "faker": {"module": "datatype", "subModule": "number", "args": "", "skip": false, "_id": "6698dac866ff1070873d4523"}, "min": 0, "max": 10000000, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "subordinate", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "p", "label": "Subordinate", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "669209225684898de7639752"}, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "templates", "databaseName": "templates", "displayName": "Email Templates", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Name", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940612"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "subject", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "inputTextarea", "label": "Subject", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940613"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "body", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "inputTextarea", "label": "Body", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940614"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "variables", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "inputTextarea", "label": "Variables", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940615"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "image", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "image", "label": "Image", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940616"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"name_string_text": "Invitation", "subject_string_textarea": "Welcome to [Software Name] - Your Journey Begins Now!", "body_string_textarea": "Sure! Here's a sample onboarding email for the launch of a new software application:\r\n\r\n---\r\n\r\n**Subject:** Welcome to [Software Name] - Your Journey Begins Now!\r\n\r\n---\r\n\r\nDear [Recipient's Name],\r\n\r\nWe are thrilled to welcome you to [Software Name], your new go-to solution for [briefly describe the primary function or benefit of the software]. Our team has been working tirelessly to create an application that not only meets your needs but exceeds your expectations.\r\n\r\n**Getting Started**\r\n\r\nTo help you get the most out of [Software Name], we've put together a few resources to guide you through the initial setup and beyond:\r\n\r\n1. **User Guide:** [Link to user guide]\r\n2. **Video Tutorials:** [Link to video tutorials]\r\n3. **Knowledge Base:** [Link to knowledge base]\r\n\r\n**Your First Steps**\r\n\r\n1. **Log In:** Visit [login URL] and enter your credentials to access your account.\r\n2. **Profile Setup:** Complete your profile to personalize your experience.\r\n3. **Explore Features:** Take a tour of the key features by visiting the [features page/link].\r\n\r\n**Support and Community**\r\n\r\nWe’re here to support you every step of the way. If you have any questions or run into any issues, please don’t hesitate to reach out to our support team at [support email] or visit our community forum at [forum link].\r\n\r\n**Feedback**\r\n\r\nYour feedback is invaluable to us. We encourage you to share your thoughts, suggestions, and any issues you encounter. You can submit feedback directly within the app or by emailing us at [feedback email].\r\n\r\n**Stay Connected**\r\n\r\nFollow us on [social media links] to stay updated with the latest news, tips, and updates.\r\n\r\nThank you for choosing [Software Name]. We’re excited to have you on board and can’t wait to see how [Software Name] enhances your [specific area of work or life].\r\n\r\nBest regards,\r\n\r\n[Your Name]  \r\n[Your Position]  \r\n[Company Name]  \r\n[Company Contact Information]\r\n\r\n---\r\n\r\nFeel free to customize this template to better suit your software application and company’s voice.", "variables_string_textarea": "none", "image_string_image": "none"}, {"name_string_text": "First reminder", "subject_string_textarea": "Friendly Reminder: Incomplete Task - [Task Name/Description]", "body_string_textarea": "Dear [Recipient's Name],\r\n\r\nI hope this message finds you well.\r\n\r\nThis is a friendly reminder that the task [Task Name/Description] assigned to you on [Assignment Date] is still pending completion. The due date for this task is [Due Date], and we want to ensure everything stays on track.\r\n\r\nTask Details:\r\n\r\nTask: [Task Name/Description]\r\nDue Date: [Due Date]\r\nAssigned By: [Assignor's Name]\r\nDetails: [Link to task details/documentation]\r\nCompleting this task on time is crucial because [briefly explain the importance of the task and any potential consequences of delays].\r\n\r\nNeed Help?\r\n\r\nIf you’re facing any challenges or need further assistance, please don’t hesitate to reach out. We’re here to help and support you in any way we can to ensure this task is completed successfully.\r\n\r\nThank you for your attention to this matter. We appreciate your prompt action.\r\n\r\nBest regards,\r\n\r\n[Your Name]\r\n[Your Position]\r\n[Company Name]\r\n[Contact Information]", "variables_string_textarea": "none", "image_string_image": "none"}, {"name_string_text": "Second reminder", "subject_string_textarea": "Task Incomplete 2nd Reminder", "body_string_textarea": "Dear [Recipient's Name],\r\n\r\nI hope this message finds you well.\r\n\r\nThis is a friendly reminder that the task [Task Name/Description] assigned to you on [Assignment Date] is still pending completion. The due date for this task is [Due Date], and we want to ensure everything stays on track.\r\n\r\nTask Details:\r\n\r\nTask: [Task Name/Description]\r\nDue Date: [Due Date]\r\nAssigned By: [Assignor's Name]\r\nDetails: [Link to task details/documentation]\r\nCompleting this task on time is crucial because [briefly explain the importance of the task and any potential consequences of delays].\r\n\r\nNeed Help?\r\n\r\nIf you’re facing any challenges or need further assistance, please don’t hesitate to reach out. We’re here to help and support you in any way we can to ensure this task is completed successfully.\r\n\r\nThank you for your attention to this matter. We appreciate your prompt action.\r\n\r\nBest regards,\r\n\r\n[Your Name]\r\n[Your Position]\r\n[Company Name]\r\n[Contact Information]", "variables_string_textarea": "none", "image_string_image": "none"}, {"name_string_text": "Close", "subject_string_textarea": "Important Notice: Temporary Closure of [Application Name]", "body_string_textarea": "Dear [Recipient's Name],\r\n\r\nWe hope this message finds you well.\r\n\r\nWe are writing to inform you that [Application Name] will be temporarily closed effective immediately due to [briefly explain the reason, e.g., maintenance, system upgrades, unforeseen technical issues]. This decision was made to ensure we can provide you with the best possible experience moving forward.\r\n\r\nWhat's Happening?\r\n\r\nReason for Closure: [Detailed reason for the temporary closure]\r\nDuration: Until further notice\r\nImpact: [Describe how this will impact the users and any specific features that will be unavailable]\r\nWhat You Need to Know:\r\n\r\nDuring this period, you will not be able to access [Application Name] or its associated services. We understand the inconvenience this may cause and are working diligently to resolve the issues as quickly as possible.\r\n\r\nHow We Are Handling It:\r\n\r\nOur team is dedicated to resolving the situation and ensuring that [Application Name] is back online with improved performance and stability. We will keep you updated on our progress and notify you immediately once the application is available again.\r\n\r\nSupport and Assistance:\r\n\r\nIf you have any questions or need assistance during this time, please contact our support team at [support email] or visit our help center at [help center link]. We are here to assist you with any concerns you may have.\r\n\r\nWe sincerely apologize for any inconvenience this may cause and appreciate your patience and understanding. Thank you for your continued support of [Application Name].\r\n\r\nBest regards,\r\n\r\n[Your Name]\r\n[Your Position]\r\n[Company Name]\r\n[Contact Information]", "variables_string_textarea": "none", "image_string_image": "none"}, {"name_string_text": "Bug Reported", "subject_string_textarea": "Bug Report Received - We're Working on It!", "body_string_textarea": "Dear [Recipient's Name],\r\n\r\nThank you for reaching out and bringing the issue with [brief description of the bug or feature] to our attention.\r\n\r\nWe have received your bug report and our development team is currently investigating the issue. We understand how important it is to have a seamless experience with [Application Name], and we are committed to resolving this as quickly as possible.\r\n\r\nBug Details:\r\n\r\nReported Issue: [Detailed description of the bug as reported]\r\nDate Reported: [Date]\r\nStatus: Under investigation\r\nNext Steps:\r\n\r\nOur team will be conducting a thorough review to identify the root cause of the problem. We will update you on our progress and let you know as soon as a fix is implemented.\r\n\r\nTemporary Solutions:\r\n\r\nIn the meantime, you might find the following workaround helpful: [provide any possible temporary solutions or workarounds, if available].\r\n\r\nContact Us:\r\n\r\nIf you have any additional information about the issue or need further assistance, please don't hesitate to contact our support team at [support email] or visit our help center at [help center link].\r\n\r\nWe appreciate your patience and understanding as we work to resolve this issue. Thank you for helping us improve [Application Name].\r\n\r\nBest regards,\r\n\r\n[Your Name]\r\n[Your Position]\r\n[Company Name]\r\n[Contact Information]", "variables_string_textarea": "none", "image_string_image": "none"}], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "tests", "databaseName": "tests", "displayName": "Tests", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "stack", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "<PERSON><PERSON>", "faker": {"skip": false, "_id": "666151d6eaafcce3f194061c"}, "min": 3, "max": 1000000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "service", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Service", "faker": {"skip": false, "_id": "666151d6eaafcce3f194061d"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "passed", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "badge", "label": "Passed", "faker": {"skip": false, "_id": "666151d6eaafcce3f194061e"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "failed", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "badge", "label": "Failed", "faker": {"skip": false, "_id": "666151d6eaafcce3f194061f"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "notes", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Notes", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940620"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"stack": "nodejs", "service": "users", "passed_number_badge": 1, "failed_number_badge": 2, "notes_string_text": "sone note"}], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "userAddresses", "databaseName": "user_addresses", "displayName": "User Addresses", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "userId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "User", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940635"}, "min": 10, "max": 20, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "users", "refDatabaseName": "users", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "Street1", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "inputTextarea", "label": "Street1", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940636"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "Street2", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "inputTextarea", "label": "Street2", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940637"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "Poscode", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Poscode", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940638"}, "min": 0, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "City", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "City", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940639"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "State", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "State", "faker": {"skip": false, "_id": "666151d6eaafcce3f194063a"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "Province", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Province", "faker": {"skip": false, "_id": "666151d6eaafcce3f194063b"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "Country", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Country", "faker": {"skip": false, "_id": "666151d6eaafcce3f194063c"}, "min": 3, "max": *********, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"userId_users_name_121": "<PERSON><PERSON>", "Street1_string_textarea": "22, <PERSON><PERSON> 14/48", "Street2_string_textarea": "Section 14", "Poscode_string_text": 46100, "City_string_text": "Petaling Jaya", "State_string_text": "Selangor", "Province_string_text": "None", "Country_string_text": "Malaysia"}], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "userChangePassword", "databaseName": "user_change_password", "displayName": "UserChangePassword", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "userEmail", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "User Email", "faker": {"module": "datatype", "subModule": "number", "args": "1", "skip": false, "_id": "66a1380cd2593655636f8020"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "server", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Server", "faker": {"module": "datatype", "subModule": "number", "args": "1", "skip": false, "_id": "66a1380cd2593655636f8021"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "environment", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Environment", "faker": {"module": "datatype", "subModule": "number", "args": "1", "skip": false, "_id": "66a1380cd2593655636f8022"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "code", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Code", "faker": {"module": "datatype", "subModule": "number", "args": "1", "skip": false, "_id": "66a1380cd2593655636f8023"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "status", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p_boolean", "label": "Status", "faker": {"module": "datatype", "subModule": "number", "args": "1", "skip": false, "_id": "66a1380cd2593655636f8024"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "sendEmailCounter", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_number", "label": "SendEmailCounter", "faker": {"module": "datatype", "subModule": "number", "args": "1", "skip": false, "_id": "66a1380cd2593655636f8025"}, "min": 0, "max": 1000000, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "0", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "userInvites", "databaseName": "user_invites", "displayName": "User Invites", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "emailToInvite", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Invitation Email", "faker": {"module": "datatype", "subModule": "boolean", "args": "1", "skip": false, "_id": "6667c614dd05e82b9021d500"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "status", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": null, "displayOnEdit": null, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": false, "required": false, "component": "p", "label": "Status", "faker": {"module": "datatype", "subModule": "boolean", "args": "1", "skip": false, "_id": "6667c614dd05e82b9021d501"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "code", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": false, "displayOnEdit": false, "displayOnSingle": true, "displayOnDataTable": true, "creatable": false, "editable": false, "sortable": true, "required": false, "component": "p", "label": "Code", "faker": {"module": "datatype", "subModule": "number", "args": "", "skip": false, "_id": "66987a749bdf3701d5c734e3"}, "min": 0, "max": 1000000, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "sendMailCounter", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p_number", "label": "SendMailCounter", "faker": {"module": "datatype", "subModule": "number", "args": "", "skip": false, "_id": "669dd1683689a8f87d132183"}, "min": 0, "max": 10000000, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "0", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "userLogin", "databaseName": "user_login", "displayName": "UserLogin", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "loginEmail", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "<PERSON><PERSON>", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "669c6cfbcfe11dcf760e2172"}, "min": null, "max": null, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "access", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Access", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "669c6cfbcfe11dcf760e2173"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "userPhones", "databaseName": "user_phones", "displayName": "User Phones", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "userId", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "User", "faker": {"skip": false, "_id": "666151d6eaafcce3f194064d"}, "min": 10, "max": 20, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "users", "refDatabaseName": "users", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "countryCode", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "default", "label": "Countrycode", "faker": {"module": "datatype", "subModule": "number", "args": "2", "skip": false, "_id": "666298096e9ee9002730d513"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "operatorCode", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "default", "label": "Operatorcode", "faker": {"module": "datatype", "subModule": "number", "args": "2", "skip": false, "_id": "666298096e9ee9002730d514"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "number", "type": "Number", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "default", "label": "Number", "faker": {"module": "datatype", "subModule": "number", "args": "8", "skip": false, "_id": "666298096e9ee9002730d515"}, "min": 0, "max": *********, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "type", "type": "Array", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdownArray", "label": "Type", "faker": {"skip": false, "_id": "666151d6eaafcce3f1940651"}, "min": 3, "max": *********, "enum": [], "index": false, "autocomplete": true, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "default": "[\"Land line\",\"Mobile\",\"Fax\"]", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "isDefault", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p_boolean", "label": "<PERSON>", "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": true, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"countryCode": 60, "operatorCode": 3, "number": 2345333, "type": "Land line"}, {"countryCode": 60, "operatorCode": 12, "number": 2223344, "type": "Mobile"}, {"countryCode": 65, "operatorCode": 9, "number": 32332332, "type": "Fax"}], "skip": true, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "users", "databaseName": "users", "displayName": "Users", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Name", "faker": {"module": "datatype", "subModule": "boolean", "args": "1", "skip": false, "_id": "66a87a6774adb13d2ff35eea"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "email", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Email", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66a4827774d043b1ed36eda5"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "password", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Password", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66a4827774d043b1ed36eda6"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "status", "type": "Boolean", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p_boolean", "label": "Status", "faker": {"module": "datatype", "subModule": "boolean", "args": "1", "skip": false, "_id": "66a87a6774adb13d2ff35eed"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": false, "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": true, "seeder": [], "skip": true, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "tickets", "databaseName": "tickets", "displayName": "Tickets", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": ["users"], "schemaList": [{"fieldName": "ticket", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Ticket", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66a85ae28c6658f6ad17ea10"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "project", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Project", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66a85ae28c6658f6ad17ea11"}, "min": 2, "max": 150, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": ["8"], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "title", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Title", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66a85ae28c6658f6ad17ea12"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "description", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "inputTextarea", "label": "Description", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66a85ae28c6658f6ad17ea13"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "status", "type": "Array", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p", "label": "Status", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66a85ae28c6658f6ad17ea14"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "[\"open\",\"closed\",\"inprogress\",\"reopened\"]", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "priority", "type": "Array", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p", "label": "Priority", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66a85ae28c6658f6ad17ea15"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "[\"high\",\"medium\",\"low\",\"critical\"]", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "type", "type": "Array", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "p", "label": "Type", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66a85ae28c6658f6ad17ea16"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "default": "[\"bug\",\"feature\",\"task\"]", "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}, {"fieldName": "reporter", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Reporter", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66a85ae28c6658f6ad17ea17"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "users", "refDatabaseName": "users", "relationshipType": "one-to-one", "identifierFieldName": [null]}}, {"fieldName": "assignee", "type": "ObjectId", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": false, "required": false, "component": "dropdown", "label": "Assignee", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66a85ae28c6658f6ad17ea18"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"refServiceName": "users", "refDatabaseName": "users", "relationshipType": "one-to-one", "identifierFieldName": ["name"]}}, {"fieldName": "closed", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": true, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": false, "component": "p", "label": "Closed", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66a85ae28c6658f6ad17ea19"}, "min": 2, "max": 1000, "enum": [], "index": true, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "prefix": "", "suffix": "", "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [{"ticket_id": 1, "project_id": 3, "title": "Bug #1 - Issue with feature 14", "description": "Description of bug 1 affecting feature 14", "status": "open", "priority": "high", "type": "bug", "reporter_id": 4, "assignee_id": 2, "closed_at": "None"}, {"ticket_id": 2, "project_id": 1, "title": "Bug #2 - Issue with feature 8", "description": "Description of bug 2 affecting feature 8", "status": "in progress", "priority": "medium", "type": "feature", "reporter_id": 5, "assignee_id": 3, "closed_at": "None"}, {"ticket_id": 3, "project_id": 2, "title": "Bug #3 - Issue with feature 11", "description": "Description of bug 3 affecting feature 11", "status": "closed", "priority": "critical", "type": "task", "reporter_id": 1, "assignee_id": 6, "closed_at": 45433.60766203704}, {"ticket_id": 4, "project_id": 5, "title": "Bug #4 - Issue with feature 5", "description": "Description of bug 4 affecting feature 5", "status": "open", "priority": "low", "type": "bug", "reporter_id": 7, "assignee_id": 8, "closed_at": "None"}, {"ticket_id": 5, "project_id": 3, "title": "Bug #5 - Issue with feature 9", "description": "Description of bug 5 affecting feature 9", "status": "reopened", "priority": "high", "type": "feature", "reporter_id": 2, "assignee_id": 5, "closed_at": "None"}, {"ticket_id": 6, "project_id": 4, "title": "Bug #6 - Issue with feature 3", "description": "Description of bug 6 affecting feature 3", "status": "in progress", "priority": "medium", "type": "task", "reporter_id": 6, "assignee_id": 9, "closed_at": "None"}, {"ticket_id": 7, "project_id": 2, "title": "Bug #7 - Issue with feature 12", "description": "Description of bug 7 affecting feature 12", "status": "closed", "priority": "critical", "type": "bug", "reporter_id": 3, "assignee_id": 7, "closed_at": 45366.38246527778}, {"ticket_id": 8, "project_id": 1, "title": "Bug #8 - Issue with feature 7", "description": "Description of bug 8 affecting feature 7", "status": "open", "priority": "low", "type": "feature", "reporter_id": 8, "assignee_id": 4, "closed_at": "None"}, {"ticket_id": 9, "project_id": 5, "title": "Bug #9 - Issue with feature 10", "description": "Description of bug 9 affecting feature 10", "status": "closed", "priority": "high", "type": "task", "reporter_id": 9, "assignee_id": 1, "closed_at": 45485.76770833333}, {"ticket_id": 10, "project_id": 4, "title": "Bug #10 - Issue with feature 6", "description": "Description of bug 10 affecting feature 6", "status": "reopened", "priority": "medium", "type": "bug", "reporter_id": 10, "assignee_id": 2, "closed_at": "None"}], "skip": false, "downloadable": true, "uploadable": true, "sharable": false, "ai": false, "warehouse": false}, {"serviceName": "testet", "databaseName": "testet", "displayName": "Testet", "icon": "pi pi-cog", "create": true, "edit": true, "delete": true, "single": true, "sidebar": [], "schemaList": [{"fieldName": "name", "type": "String", "unique": false, "lowercase": false, "uppercase": false, "trim": false, "display": true, "displayOnEdit": true, "displayOnSingle": true, "displayOnDataTable": true, "creatable": true, "editable": true, "sortable": true, "required": true, "component": "p", "label": "Name", "faker": {"module": "lorem", "subModule": "sentence", "args": "1", "skip": false, "_id": "66bc917ccb54f045d2318347"}, "enum": [], "index": false, "autocomplete": false, "lazy": false, "reverse": false, "morph": false, "through": false, "args": [], "auditing": false, "warehousing": false, "searchable": false, "mode": "currency", "currency": {"currency": "MYR", "locale": "en-US"}, "reference": {"identifierFieldName": []}}], "layout": false, "seeder": [], "skip": false, "downloadable": true, "uploadable": false, "sharable": false, "ai": false, "warehouse": false}]}