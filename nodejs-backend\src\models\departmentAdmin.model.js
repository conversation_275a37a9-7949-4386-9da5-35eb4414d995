module.exports = function (app) {
  const modelName = "department_admin";
  const mongooseClient = app.get("mongooseClient");
  const { Schema } = mongooseClient;
  const schema = new Schema(
    {
      departmentId: { type: Schema.Types.ObjectId, ref: "departments" },
      employeeId: { type: Schema.Types.ObjectId, ref: "employees" },

      createdBy: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: "created by field is required",
      },
      updatedBy: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true,
      },
    },
    {
      timestamps: true,
    },
  );

  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName);
  }
  return mongooseClient.model(modelName, schema);
};
