[{"name_string_text": "Invitation", "subject_string_textarea": "Welcome to [Software Name] - Your Journey Begins Now!", "body_string_textarea": "Sure! Here's a sample onboarding email for the launch of a new software application:\r\n\r\n---\r\n\r\n**Subject:** Welcome to [Software Name] - Your Journey Begins Now!\r\n\r\n---\r\n\r\nDear [Recipient's Name],\r\n\r\nWe are thrilled to welcome you to [Software Name], your new go-to solution for [briefly describe the primary function or benefit of the software]. Our team has been working tirelessly to create an application that not only meets your needs but exceeds your expectations.\r\n\r\n**Getting Started**\r\n\r\nTo help you get the most out of [Software Name], we've put together a few resources to guide you through the initial setup and beyond:\r\n\r\n1. **User Guide:** [Link to user guide]\r\n2. **Video Tutorials:** [Link to video tutorials]\r\n3. **Knowledge Base:** [Link to knowledge base]\r\n\r\n**Your First Steps**\r\n\r\n1. **Log In:** Visit [login URL] and enter your credentials to access your account.\r\n2. **Profile Setup:** Complete your profile to personalize your experience.\r\n3. **Explore Features:** Take a tour of the key features by visiting the [features page/link].\r\n\r\n**Support and Community**\r\n\r\nWe’re here to support you every step of the way. If you have any questions or run into any issues, please don’t hesitate to reach out to our support team at [support email] or visit our community forum at [forum link].\r\n\r\n**Feedback**\r\n\r\nYour feedback is invaluable to us. We encourage you to share your thoughts, suggestions, and any issues you encounter. You can submit feedback directly within the app or by emailing us at [feedback email].\r\n\r\n**Stay Connected**\r\n\r\nFollow us on [social media links] to stay updated with the latest news, tips, and updates.\r\n\r\nThank you for choosing [Software Name]. We’re excited to have you on board and can’t wait to see how [Software Name] enhances your [specific area of work or life].\r\n\r\nBest regards,\r\n\r\n[Your Name]  \r\n[Your Position]  \r\n[Company Name]  \r\n[Company Contact Information]\r\n\r\n---\r\n\r\nFeel free to customize this template to better suit your software application and company’s voice.", "variables_string_textarea": "none", "image_string_image": "none"}, {"name_string_text": "First reminder", "subject_string_textarea": "Friendly Reminder: Incomplete Task - [Task Name/Description]", "body_string_textarea": "Dear [Recipient's Name],\r\n\r\nI hope this message finds you well.\r\n\r\nThis is a friendly reminder that the task [Task Name/Description] assigned to you on [Assignment Date] is still pending completion. The due date for this task is [Due Date], and we want to ensure everything stays on track.\r\n\r\nTask Details:\r\n\r\nTask: [Task Name/Description]\r\nDue Date: [Due Date]\r\nAssigned By: [Assignor's Name]\r\nDetails: [Link to task details/documentation]\r\nCompleting this task on time is crucial because [briefly explain the importance of the task and any potential consequences of delays].\r\n\r\nNeed Help?\r\n\r\nIf you’re facing any challenges or need further assistance, please don’t hesitate to reach out. We’re here to help and support you in any way we can to ensure this task is completed successfully.\r\n\r\nThank you for your attention to this matter. We appreciate your prompt action.\r\n\r\nBest regards,\r\n\r\n[Your Name]\r\n[Your Position]\r\n[Company Name]\r\n[Contact Information]", "variables_string_textarea": "none", "image_string_image": "none"}, {"name_string_text": "Second reminder", "subject_string_textarea": "Task Incomplete 2nd Reminder", "body_string_textarea": "Dear [Recipient's Name],\r\n\r\nI hope this message finds you well.\r\n\r\nThis is a friendly reminder that the task [Task Name/Description] assigned to you on [Assignment Date] is still pending completion. The due date for this task is [Due Date], and we want to ensure everything stays on track.\r\n\r\nTask Details:\r\n\r\nTask: [Task Name/Description]\r\nDue Date: [Due Date]\r\nAssigned By: [Assignor's Name]\r\nDetails: [Link to task details/documentation]\r\nCompleting this task on time is crucial because [briefly explain the importance of the task and any potential consequences of delays].\r\n\r\nNeed Help?\r\n\r\nIf you’re facing any challenges or need further assistance, please don’t hesitate to reach out. We’re here to help and support you in any way we can to ensure this task is completed successfully.\r\n\r\nThank you for your attention to this matter. We appreciate your prompt action.\r\n\r\nBest regards,\r\n\r\n[Your Name]\r\n[Your Position]\r\n[Company Name]\r\n[Contact Information]", "variables_string_textarea": "none", "image_string_image": "none"}, {"name_string_text": "Close", "subject_string_textarea": "Important Notice: Temporary Closure of [Application Name]", "body_string_textarea": "Dear [Recipient's Name],\r\n\r\nWe hope this message finds you well.\r\n\r\nWe are writing to inform you that [Application Name] will be temporarily closed effective immediately due to [briefly explain the reason, e.g., maintenance, system upgrades, unforeseen technical issues]. This decision was made to ensure we can provide you with the best possible experience moving forward.\r\n\r\nWhat's Happening?\r\n\r\nReason for Closure: [Detailed reason for the temporary closure]\r\nDuration: Until further notice\r\nImpact: [Describe how this will impact the users and any specific features that will be unavailable]\r\nWhat You Need to Know:\r\n\r\nDuring this period, you will not be able to access [Application Name] or its associated services. We understand the inconvenience this may cause and are working diligently to resolve the issues as quickly as possible.\r\n\r\nHow We Are Handling It:\r\n\r\nOur team is dedicated to resolving the situation and ensuring that [Application Name] is back online with improved performance and stability. We will keep you updated on our progress and notify you immediately once the application is available again.\r\n\r\nSupport and Assistance:\r\n\r\nIf you have any questions or need assistance during this time, please contact our support team at [support email] or visit our help center at [help center link]. We are here to assist you with any concerns you may have.\r\n\r\nWe sincerely apologize for any inconvenience this may cause and appreciate your patience and understanding. Thank you for your continued support of [Application Name].\r\n\r\nBest regards,\r\n\r\n[Your Name]\r\n[Your Position]\r\n[Company Name]\r\n[Contact Information]", "variables_string_textarea": "none", "image_string_image": "none"}, {"name_string_text": "Bug Reported", "subject_string_textarea": "Bug Report Received - We're Working on It!", "body_string_textarea": "Dear [Recipient's Name],\r\n\r\nThank you for reaching out and bringing the issue with [brief description of the bug or feature] to our attention.\r\n\r\nWe have received your bug report and our development team is currently investigating the issue. We understand how important it is to have a seamless experience with [Application Name], and we are committed to resolving this as quickly as possible.\r\n\r\nBug Details:\r\n\r\nReported Issue: [Detailed description of the bug as reported]\r\nDate Reported: [Date]\r\nStatus: Under investigation\r\nNext Steps:\r\n\r\nOur team will be conducting a thorough review to identify the root cause of the problem. We will update you on our progress and let you know as soon as a fix is implemented.\r\n\r\nTemporary Solutions:\r\n\r\nIn the meantime, you might find the following workaround helpful: [provide any possible temporary solutions or workarounds, if available].\r\n\r\nContact Us:\r\n\r\nIf you have any additional information about the issue or need further assistance, please don't hesitate to contact our support team at [support email] or visit our help center at [help center link].\r\n\r\nWe appreciate your patience and understanding as we work to resolve this issue. Thank you for helping us improve [Application Name].\r\n\r\nBest regards,\r\n\r\n[Your Name]\r\n[Your Position]\r\n[Company Name]\r\n[Contact Information]", "variables_string_textarea": "none", "image_string_image": "none"}]