import * as React from 'react';
const MailSent = (props) => (
	<svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} fill="currentColor" {...props}>
		<path
			fill="currentColor"
			fillRule="evenodd"
			d="m10.51 9.183-2.742 3.304c.041.016.074.03.103.043l.05.022 1.862.638 5.438 1.86c.396.134.703-.054.765-.477.21-1.388.416-2.776.623-4.165l.31-2.082c.242-1.614.48-3.227.719-4.841l.311-2.086c.03-.212-.027-.4-.2-.53a.51.51 0 0 0-.576-.054l-.17.086-.172.087a9659.745 9659.745 0 0 1-15.756 8.23c-.22.115-.342.291-.323.541.02.246.169.403.403.484l1.234.42c1.025.349 2.048.697 3.073 ***********.189-.004.25-.05.19-.148.373-.306.557-.463.069-.06.138-.12.208-.178l2.777-2.375c1.852-1.584 3.704-3.168 5.556-4.749.026-.023.055-.042.084-.062l.043-.**************.013-4.45 5.36Zm-.782 5.486.214-.292c-.983-.334-1.936-.66-2.897-.991a3.13 3.13 0 0 1-.007.052c-.005.033-.008.059-.008.082l-.001 1.05c0 .699-.002 1.398 0 2.097 0 .28.14.477.366.553.*************.622-.219.148-.195.294-.395.44-.595l.129-.177 1.142-1.56Z"
			clipRule="evenodd"
		/>
	</svg>
);
export default MailSent;
