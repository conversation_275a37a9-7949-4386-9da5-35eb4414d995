.notification-icon {
  position: relative;
  cursor: pointer;
  margin-right: 15px;
}

.notification-icon .p-badge {
  position: absolute;
  top: -15px;
  right: -10px;
}

.notification-panel {
  position: absolute;
  top: 45px;
  right: 0;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* Softer shadow */
  border-radius: 6px;
  z-index: 100;
  width: 400px;
  max-height: 400px; /* Limit the height */
  overflow-y: auto; /* Add scroll if needed */
  overflow-x: hidden;
}

.notification-item {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0; /* Lighter border */
  display: flex;
  left: 0;
  width: 400px;
  align-items: flex-start; /* Align items to the top */
  transition: background-color 0.2s; /* Smooth hover effect */
}

.notification-item:hover {
  background-color: #f5f5f5; /* Light gray on hover */
}

.notification-message {
  flex-grow: 1; /* Allow message to take up available space */
}

.notification-time {
  font-size: 0.8em;
  color: #888;
  margin-top: 5px; /* Add some space above the time */
  white-space: nowrap; /* Prevent time from wrapping */
}

.notification-item.unread {
  font-weight: 600;
}

.notification-content {
  display: flex;
  align-items: center;
}

.notification-text {
  margin-left: 10px; /* Space between avatar and text */
}

.unread-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: red;
  border-radius: 50%;
  margin-left: auto; /* Push the dot to the right */
}

.notification-item.unread {
  background-color: #e0f7fa; /* Light blue background for unread */
}
