{"ast": null, "code": "'use strict';\n\nvar merge = require('./lib/util/merge');\nvar xlink = require('./lib/xlink');\nvar xml = require('./lib/xml');\nvar xmlns = require('./lib/xmlns');\nvar aria = require('./lib/aria');\nvar html = require('./lib/html');\nmodule.exports = merge([xml, xlink, xmlns, aria, html]);", "map": {"version": 3, "names": ["merge", "require", "xlink", "xml", "xmlns", "aria", "html", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Chatbot E-Learn by Gemini API/capstoneproject/chatbotwithgemini-aa4449/react-frontend/node_modules/property-information/html.js"], "sourcesContent": ["'use strict'\n\nvar merge = require('./lib/util/merge')\nvar xlink = require('./lib/xlink')\nvar xml = require('./lib/xml')\nvar xmlns = require('./lib/xmlns')\nvar aria = require('./lib/aria')\nvar html = require('./lib/html')\n\nmodule.exports = merge([xml, xlink, xmlns, aria, html])\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACvC,IAAIC,KAAK,GAAGD,OAAO,CAAC,aAAa,CAAC;AAClC,IAAIE,GAAG,GAAGF,OAAO,CAAC,WAAW,CAAC;AAC9B,IAAIG,KAAK,GAAGH,OAAO,CAAC,aAAa,CAAC;AAClC,IAAII,IAAI,GAAGJ,OAAO,CAAC,YAAY,CAAC;AAChC,IAAIK,IAAI,GAAGL,OAAO,CAAC,YAAY,CAAC;AAEhCM,MAAM,CAACC,OAAO,GAAGR,KAAK,CAAC,CAACG,GAAG,EAAED,KAAK,EAAEE,KAAK,EAAEC,IAAI,EAAEC,IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}