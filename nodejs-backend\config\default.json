{"host": "localhost", "port": 3030, "public": "../public/", "paginate": {"default": 1000, "max": 100000}, "mongodb": "mongodb://localhost:27017/chatbotwithgemini", "authentication": {"entity": "user", "service": "users", "secret": "CAF3vPsTFN7gHhNnlfQprciJLCM=", "authStrategies": ["jwt", "local"], "jwtOptions": {"header": {"type": "access"}, "audience": "http://localhost", "issuer": "feathers", "algorithm": "HS256", "expiresIn": "1d"}, "local": {"usernameField": "email", "passwordField": "password"}, "oauth": {"redirect": "/", "auth0": {"key": "<auth0 oauth key>", "secret": "<auth0 oauth secret>", "subdomain": "<auth0 subdomain>", "scope": ["profile", "openid", "email"]}}}, "isDev": true, "isPrd": false, "isStg": false, "isUat": false, "isSit": false}